1. <PERSON> navigates to 'Role Change Requests' section via sidebar link in StaffDashboardPage
Business Process Context: `{"heading_number": "1.1", "title": "1.1 Staff", "Content": "{\n \n\"source\":\n \n\"requests\",\n \n\"target\":\n \n\"request_history\",\n \n\"type\":\n \n\"one-to-many\"\n \n}\n \n]\n \n}\n \nII.\n \nRequirement\n \nSpecifications\n \n \n1.\n \nUse\n \nCase\n \nDescription\n \n1.1\n \nStaff\n \na.Diagram\n\n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \nview\n \ntheir\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"SendRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \ncreate\n \nnew\n \nrequests\n \n(e.g.,\n \nTraining\n \nor\n \nRole\n \nChange).\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \nif\n \ndirectly\n \naccessible\n \nand\n \ndistinct\n \nfor\n \nStaff\n \nflow\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nlist'\n \n(e.g.,\n \nUC006,\n \nUC009)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"SendRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Send\n \nnew\n \nrequest'\n \n(e.g.,\n \nUC005,\n \nUC-RC001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestListPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"SendRequestPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nor\n \nafter\n \nsuccessful\n \nsubmission\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nStaff\n \nuser.\"\n \n}\n \n \nb.\n \nDescriptions\n \n \n1.1.1\n \nUse\n \nCase:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \n \n \nTable\n \n12\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-TR001\n \nUse\n \nCase\n \nName:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nStaff\n \nSecondary\n \nActors:\n \nSýtem\n \nStaff,\n \nSystem\n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \nsection\n \nfor\n \nviewing\n \ntheir\n \nsubmitted\n \nrole\n \nchange\n \nrequests,\n \ntypically\n \nfrom\n \ntheir\n \ndashboard\n \nor\n \nprofile.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nview\n \na\n \nlist\n \nof\n \nall\n \nrole\n \nchange\n \nrequests\n \nthey\n \nhave\n \npreviously\n \nsubmitted,\n \nalong\n \nwith\n \nthe\n \ncurrent\n \nstatus\n \nand\n \nany\n \nreviewer\n \ncomments\n \nfor\n \neach\n \nrequest.\n \nPreconditions:\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession\n \n(references\n \nUC002:\n \nUser\n \nLogin).\n \n<br>\n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \nsubmitted\n \nat\n \nleast\n \none\n \nrole\n \nchange\n \nrequest.\n \nPostconditions:\n \n-\n \nSuccess:\n \nThe\n \nStaff\n \nuser\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nof\n \ntheir\n \nsubmitted\n \nrole\n \nchange\n \nrequests,\n \nincluding\n \ndetails\n \nlike\n \nRequested\n \nRole,\n \nReason,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \n<br>\n \n-\n \nFailure\n \n(No\n \nRequests):\n \nIf\n \nthe\n \nuser\n \nhas\n \nnot\n \nsubmitted\n \nany\n \nrole\n \nchange\n \nrequests,\n \na\n \nmessage\n \nindicating\n \n\"You\n \ndon't\n \nhave\n \nany\n \nrole\n \nchange\n \nrequests.\"\n \n(or\n \nsimilar)\n \nis\n \ndisplayed.\n \nExpected\n \nResults:\n \nThe\n \nStaff\n \nuser\n \ncan\n \nsuccessfully\n \nview\n \nthe\n \nstatus\n \nand\n \ndetails\n \nof\n \nall\n \nrole\n \nchange\n \nrequests\n \nthey\n \nhave\n \ninitiated.\n\nNormal\n \nFlow:\n \n1.\n \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC002:\n \nUser\n \nLogin)\n \nand\n \nis\n \non\n \nStaffDashboardPage.\n \n<br>\n \n2.\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Role\n \nChange\n \nRequests'\n \nsection\n \n(e.g.,\n \nvia\n \na\n \nsidebar\n \nlink\n \non\n \nStaffDashboardPage).\n \n<br>\n \n3.\n \nThe\n \nsystem\n \ndisplays\n \nStaffRoleChangeRequestViewPage.\n \n<br>\n \n4.\n \nSystem\n \nretrieves\n \nand\n \ndisplays\n \nall\n \nrole\n \nchange\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nlogged-in\n \nStaff\n \nuser\n \nfrom\n \nthe\n \nrole_requests\n \ntable.\n \n<br>\n \n5.\n \nThe\n \nlist\n \nshows\n \nkey\n \ndetails\n \nfor\n \neach\n \nrequest:\n \nRequested\n \nRole,\n \nReason,\n \nSubmission\n \nDate,\n \nCurrent\n \nStatus,\n \nand\n \nany\n \nReviewer\n \nComments\n \nif\n \navailable.\n \nAlternative\n \nFlows:\n \nAF1:\n \nNo\n \nRole\n \nChange\n \nRequests\n \nFound\n \n<br>\n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrole\n \nchange\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \nthe\n \nmessage\n \n\"You\n \ndon't\n \nhave\n \nany\n \nrole\n \nchange\n \nrequests.\"\n \nwithin\n \nthe\n \nuser\n \nrequests\n \nsection\n \nof\n \nStaffRoleChangeRequestViewPage.\n \nExceptions:\n \nE1:\n \nSystem\n \nError:\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nrequests,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nOccasionally,\n \nwhen\n \nstaff\n \nwant\n \nto\n \ncheck\n \nthe\n \nprogress\n \nof\n \ntheir\n \nrequests.\n \nBusiness\n \nRules:\n \n-\n \nBR-RC009-1:\n \nStaff\n \ncan\n \nonly\n \nview\n \nrole\n \nchange\n \nrequests\n \nthey\n \npersonally\n \nsubmitted.\n \n<br>\n \n-\n \nBR-RC009-2:\n \nThe\n \ndisplayed\n \nstatus\n \nshould\n \nbe\n \nclear\n \nand\n \nreflect\n \nthe\n \nlatest\n \nstate\n \nin\n \nthe\n \napproval\n \nworkflow.\n\nRelated\n \nUse\n \nCases:\n \nUC002:\n \nUser\n \nLogin,\n \nUC-RC001:\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nStaffRoleChangeRequestViewPage\n \nAssumptions:\n \n-\n \nThe\n \napproval\n \nworkflow\n \nstatuses\n \nfor\n \nrole\n \nchanges\n \nare\n \ndefined\n \nand\n \nconsistently\n \nused."}`
