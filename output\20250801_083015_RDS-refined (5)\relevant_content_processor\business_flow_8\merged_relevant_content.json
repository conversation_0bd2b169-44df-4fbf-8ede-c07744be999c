[{"heading_number": "2.1", "title": "2.1 Screens Flow", "Content": "2.\n \nOverall\n \nFunctionalities\n \n2.1\n \nScreens\n \nFlow\n \n2.1.0)\n \n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \n-\n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"Homepage\"\n,\n \n\"description\"\n:\n \n\"Initial\n \nlanding\n \npage\n \nfor\n \nall\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RegistrationPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n}\n \n    \n//\n \nRole-specific\n \ndashboards\n \nare\n \nlisted\n \nin\n \ntheir\n \nrespective\n \nflows\n \nbut\n \nare\n \ntargets\n \nhere\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Login'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register\n \nhere'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RegistrationPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nregistration\n \nsubmission\n \n(UC001_RegisterNewAccount)\n \nOR\n \nClicks\n \n'Already\n \nhave\n \nan\n \naccount?\n \nLogin'\n \nlink\"\n \n},\n\n//\n \nLogin\n \nsuccess\n \ntransitions\n \nare\n \nrole-specific,\n \ndetailed\n \nbelow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nSTAFF\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nMANAGER\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nDIRECTOR\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nCEO\n \n(UC002_UserLogin)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \ndescribes\n \nthe\n \ninitial\n \naccess\n \nand\n \nauthentication\n \npathways.\n \nPost-login\n \nflows\n \nare\n \ndetailed\n \nper\n \nrole.\"\n \n}\n \n2.1.1)\n \nStaff\n \nscreen\n \nflow\n \n \nFigur e\n \n1:\n \nStaff\n \nScreen\n \nFlow\n \ndiagram\n \n•\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nStaff\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nStaffDashboard,\n \nRequestList,\n \nSendRequest\n  \n \no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \nrequest\n \nlist,\n \nBack,\n \nSend\n \nnew\n \nrequest,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nStaffDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nStaffDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \nrequest\n \nlist\"\n \nor\n \n\"Send\n \nnew\n \nrequest\".\n  \n \n5.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nStaffDashboard\n \nfrom\n \neither\n \nRequestList\n \nor\n \nSendRequest.\n\n6.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nstaff\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \nStaff's\n \nown\n \ntraining\n \nand\n \nrole\n \nchange\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewTrainingRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest.\"\n \n},\n \n    \n//\n \nAdd\n \nRoleChangeRequestDetailsPage\n \nif\n \nit\n's\n \na\n \nseparate\n \nscreen\n \nfor\n \nviewing\n \nrole\n \nrequest\n \ndetails\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\",\n \n\"description\":\n \n\"Staff'\ns\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\n\"\n \n},\n \n    \n{\n \n\"\nname\n\":\n \n\"\nLoginPage\n\",\n \n\"\ndescription\n\":\n \n\"\nTarget\n \npage\n \nafter\n \nlogout\n.\n\"\n \n}\n \n  \n],\n \n  \n\"\ntransitions\n\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nMy\n \nRequests'\n \nor\n \nsimilar\n \nlink\n \n(UC006,\n \nUC009)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'CreateNewTrainingRequestPage'\n \n(UC005)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Create\n \nRole\n \nChange\n \nRequest'\n \n(UC-RC001)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nProfilePage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Profile'\n \nlink\n \n(UC012)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \ntraining\n \nrequest\n\"\n \n},\n \n    \n//\n \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nRoleChangeRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \ntraining\n \nrequest\n \n(UC005)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \nrole\n \nchange\n \nrequest\n \n(UC-RC001)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n \n    \n//\n \nLogout\n \nfrom\n \nvarious\n \nstaff\n \npages\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n}\n \n  \n],\n \n  \n\"\nnotes\n\":\n \n\"\nPrimary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nStaff\n \nuser.\n\"\n \n}\n \n \n2.1.2)\n \nManager\n \nscreen\n \nflow\n \n \nFigur e\n \n2:\n \nManager\n \nScreen\n \nFlow\n \ndiagram\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nManager\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nManagerDashboard,\n \nManageRequest,\n \nRequestHistory,\n \nUpdateStatus\n\no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \n/\n \nManage\n \nrequests,\n \nView\n \nrequest\n \nhistory,\n \nBack,\n \nChange\n \nrequest\n \nstatus,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nManagerDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nManagerDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \n/\n \nManage\n \nrequests\"\n \nor\n \n\"View\n \nrequest\n \nhistory\".\n  \n \n5.\n \nWithin\n \n\"Manage\n \nrequests\",\n \nthe\n \nuser\n \ncan\n \n\"Change\n \nrequest\n \nstatus\",\n \nleading\n \nto\n \nthe\n \nUpdateStatus\n \nscreen.\n  \n \n6.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nManagerDashboard\n \nfrom\n \neither\n \nManageRequest\n \nor\n \nRequestHistory.\n  \n \n7.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nManager\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \ntraining\n \nrequests\n \npending\n \nManager's\n \naction\n \nor\n \nrelevant\n \nto\n \ntheir\n \nteam.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest\n \nfor\n \nManager\n \nreview/action.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \nhistorical\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \nthe\n \nManager.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Manager's\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Target\n \npage\n \nafter\n \nlogout.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nTraining\n \nRequest\n \nHistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n//\n \nLogout\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003_UserLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nlogout\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"Primary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nManager\n \nuser.\"\n \n}\n \n2.1.3)\n \nDirector\n \nscreen\n \nflow\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nDIRECTOR\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nDIRECTOR,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-DR001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \nif\n \ndifferent\n \nfrom\n \nManager's\n \nlist\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nDIRECTOR's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\"\n:\n \n[\n\n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\"\n:\n \n\"The\n \nDIRECTOR\n \ncan\n \nview\n \nand\n \nreview\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers.\n \nThey\n \ntypically\n \ndo\n \nnot\n \nmanage\n \nrole\n \nchange\n \nrequests\n \ndirectly,\n \nwhich\n \nare\n \nhandled\n \nby\n \nthe\n \nCEO.\"\n \n}\n \n2.1.4)\n \nCEO\n \nScreen\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nCEO\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"CEODashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nCEO,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests,\n \nrole\n \nrequests,\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-CE001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n\n{\n \n\"name\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \npending\n \nrole\n \nchange\n \nrequests\n \n(UC-RC002).\"\n \n},\n \n//\n \nCorresponds\n \nto\n \nscreen\n \non\n \np64\n \n    \n{\n \n\"name\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"description\"\n:\n \n\"Modal\n \nwithin\n \nApproveRoleChangeRequestPage\n \nfor\n \nCEO\n \nto\n \naction\n \na\n \nspecific\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n//\n \nFrom\n \np66\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nCEO's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-CE001:\n \nCEO\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Approve\n \nRole\n \nChange\n \nRequests'\n \nlink\n \n(UC-RC002:\n \nCEO\n \nManages\n \nRole\n \nChange\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \ntraining\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Process'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Submits\n \ndecision\n \nor\n \nCancels\n \nmodal\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"The\n \nCEO\n \nhas\n \norganization-wide\n \nauthority\n \nfor\n \nfinal\n \napproval\n \nof\n \ntraining\n \nrequests\n \nand\n \nmanages", "match_type": "exact", "matched_with": "2.1 Screens Flow"}, {"heading_number": "2.3", "title": "2.3 Screen Authorization", "Content": "}\n \n2.2\n \nScreen\n \nDescriptions\n \n2.3\n \nScreen\n \nAuthorization\n \n \nTable\n \n4:\n \nScreen\n \nauthorization\n \nScreen\n \nStaff\n \nManager\n \nDirector\n \nCEO\n \n**Training\n \nRequests**\n \nCreateNewTrainingRequestPage\n \nX\n \n \n \n \nView\n \nOwn\n \nTraining\n \nRequests\n \nX\n \nX\n \nX\n \nX\n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nManager)\n \n \nX\n \n \n \nApprove/Reject\n \n(Manager\n \nLevel)\n \n \nX\n \n \n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nDirector)\n \n \n \nX\n \n \nApprove/Reject\n \n(Director\n \nLevel)\n \n \n \nX\n \n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nCEO)\n \n \n \n \nX\n \nApprove/Reject\n \n(CEO\n \nLevel)\n \n \n \n \nX\n \nView\n \nAll/Detailed\n \nTraining\n \nRequests\n \n \n(Scope)\n \n(Scope)\n \nX\n \nView\n \nTraining\n \nRequest\n \nHistory\n \nX\n \nX\n \nX\n \nX\n \n**Role\n \nChange\n \nRequests**\n \nCreate\n \nNew\n \nRole\n \nChange\n \nRequest\n \nX\n \n \n \n \nView\n \nOwn\n \nRole\n \nChange\n \nRequests\n \nX\n \n \n \n \nView\n \nPending\n \nRole\n \nChange\n \nRequests\n \n \n \n \nX\n \nReview\n \n(Approve/Reject)\n \nRole\n \nChange\n \nRequest\n \n \n \n \nX\n \nView\n \nRole\n \nChange\n \nRequests\n \nby\n \nStatus\n \n \n \n \nX\n \n**General**\n \nLogin\n \nX\n \nX\n \nX\n \nX\n \nDashboard\n \nX\n \nX\n \nX\n \nX\n \nUser\n \nProfile\n \nManagement\n \nX\n \nX\n \nX\n \nX\n \n \n**Notes:**\n \n*\n   \n'X'\n \nindicates\n \nthe\n \nrole\n \nhas\n \naccess\n \nto\n \nthe\n \nfeature.\n \n*\n   \n'(Scope)'\n \nindicates\n \naccess\n \nmight\n \nbe\n \nlimited\n \nto\n \nrequests\n \nwithin\n \ntheir\n \ndirect\n \nteam\n \nor\n \ndepartment,\n \nor\n \nspecific\n \nstatuses\n \nrelevant\n \nto\n \ntheir\n \nrole,\n \nrather\n \nthan\n \nall\n \nrequests\n \nsystem-wide\n \n(except\n \nfor\n \nCEO\n \nwho\n \ngenerally\n \nhas\n \nfull\n \nvisibility).", "match_type": "exact", "matched_with": "2.3 Screen Authorization"}, {"heading_number": "2.4", "title": "2.4 State transition diagram of Request:", "Content": "Request:\n\nFigur e\n \n3:\n \nState\n \ntransition\n \ndiagram\n \nof\n \nRequest\n \n \nElements:\n \n \n•\n \nStates:\n \nPending,\n \nProcessing,\n \nApproved,\n \nRejected,\n \nClosed.\n \n \n•\n \nState\n \nTransitions:\n \n \no\n \nPending\n \n→\n \nProcessing:\n \nWhen\n \nthe\n \nmanager\n \nstarts\n \nreviewing\n \nthe\n \nrequest.\n \n \no\n \nProcessing\n \n→\n \nApproved:\n \nWhen\n \nthe\n \nmanager\n \napproves\n \nthe\n \nrequest.\n \n \no\n \nProcessing\n \n→\n \nRejected:\n \nWhen\n \nthe\n \nmanager\n \nrejects\n \nthe\n \nrequest.\n \n \no\n \nApproved\n \n→\n \nClosed:\n \nWhen\n \nthe\n \nrequest\n \nis\n \nfulfilled.\n \n \no\n \nRejected\n \n→\n \nClosed:\n \nWhen\n \nthe\n \nrequest\n \nis\n \nfinalized\n \nand\n \nno\n \nfurther\n \nprocessing\n \nis\n \nneeded.\n \n \n \nFlow:\n \n \n1.\n \nThe\n \nstaff\n \ncreates\n \na\n \nnew\n \nrequest\n \n→\n \nthe\n \nstate\n \ntransitions\n \nto\n \nPending.\n \n \n2.\n \nThe\n \nmanager\n \nreviews\n \nthe\n \nrequest\n \n→\n \nthe\n \nstate\n \ntransitions\n \nto\n \nProcessing.\n \n \n3.\n \nThe\n \nmanager\n \nmakes\n \na\n \ndecision:\n \n \no\n \nIf\n \napproved,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nApproved.\n \n \no\n \nIf\n \nrejected,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nRejected.\n \n \n4.\n \nWhen\n \nthe\n \nrequest\n \nis\n \ncompleted:\n \n \no\n \nIf\n \nApproved,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nClosed.\n \n \no\n \nIf\n \nRejected,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nClosed.\n \n \n5.\n \nThe\n \nsystem\n \ncompletes\n \nthe\n \nrequest\n \nlifecycle\n \nwhen\n \nthe\n \nClosed\n \nstate\n \nis\n \nreached.\n \n \na)\n \nTraining\n \nRequest\n \nState\n \nTransitions\n\n{\n \n  \n\"title\":\n \n\"Training\n \nRequest\n \nState\n \nTransition\",\n \n  \n\"states\":\n \n[\n \n    \n\"Draft\",\n \n    \n\"Submitted\",\n \n    \n\"Approved_by_Manager\",\n \n    \n\"Rejected_by_Manager\",\n \n    \n\"Approved_by_Director\",\n \n    \n\"Rejected_by_Director\",\n \n    \n\"Approved_by_CEO\",\n \n    \n\"Rejected_by_CEO\",\n \n    \n\"Approved_Final\",\n \n    \n\"End\"\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\":\n \n\"Draft\",\n \n\"to\":\n \n\"Submitted\",\n \n\"event\":\n \n\"submit\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Approved_by_Manager\",\n \n\"event\":\n \n\"manager\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Rejected_by_Manager\",\n \n\"event\":\n \n\"manager\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Manager\",\n \n\"to\":\n \n\"Approved_by_Director\",\n \n\"event\":\n \n\"director\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Manager\",\n \n\"to\":\n \n\"Rejected_by_Director\",\n \n\"event\":\n \n\"director\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Director\",\n \n\"to\":\n \n\"Approved_by_CEO\",\n \n\"event\":\n \n\"CEO\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Director\",\n \n\"to\":\n \n\"Rejected_by_CEO\",\n \n\"event\":\n \n\"CEO\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_CEO\",\n \n\"to\":\n \n\"Approved_Final\",\n \n\"event\":\n \n\"final\n \napproval\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected_by_Manager\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n\n{\n \n\"from\":\n \n\"Rejected_by_Director\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected_by_CEO\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_Final\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"complete\"\n \n}\n \n  \n],\n \n  \n\"initial_state\":\n \n\"Draft\",\n \n  \n\"final_state\":\n \n\"End\"\n \n}\n \n \n \n \nb)\n \nRole\n \nChange\n \nRequest\n \nState\n \nTransitions\n \n \n \n{\n \n  \n\"title\":\n \n\"Role\n \nChange\n \nRequest\n \nState\n \nTransition\",\n \n  \n\"states\":\n \n[\n \n    \n\"Submitted\",\n \n    \n\"Approved\",\n \n    \n\"Rejected\",\n \n    \n\"End\"\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Approved\",\n \n\"event\":\n \n\"admin\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Rejected\",\n \n\"event\":\n \n\"admin\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"finalize\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"finalize\"\n \n}\n \n  \n],\n \n  \n\"initial_state\":\n \n\"Submitted\",\n \n  \n\"final_state\":\n \n\"End\"\n\n}", "match_type": "exact", "matched_with": "2.4 State transition diagram of Request:"}, {"heading_number": "1.2", "title": "1.2.Manager", "Content": "1.2.1.\n \nUse\n \nCase:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \n \nDiagram:\n \n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n\n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nand\n \nmanage\n \nrequests\n \nassigned\n \nto\n \nthem.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nhistory\n \nof\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"UpdateStatusPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nor\n \nmodal\n \nfor\n \nManagers\n \nto\n \nupdate\n \nthe\n \nstatus\n \nof\n \na\n \nrequest.\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \n/\n \nManage\n \nrequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nhistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"UpdateStatusPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \n'Change\n \nrequest\n \nstatus'\n \naction\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nManager\n \nuser.\"\n \n}\n \n \n \nUse\n \ncase\n \ndescription\n \nof\n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR001\n\nUse\n \nCase\n \nName:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem,\n \nStaff\n \n(as\n \ninitiator),\n \nDirector\n \n(as\n \nnext\n \napprover)\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \nreviews\n \na\n \npending\n \ntraining\n \nrequest\n \nand\n \ntakes\n \nappropriate\n \naction\n \n(Approve\n \nor\n \nReject).\n \nThe\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nin\n \nthe\n \nsystem\n \nto\n \nreflect\n \nthe\n \ndecision\n \n(\nPending_Director\n \nor\n \nRejected_Manager\n),\n \nrelevant\n \nnotifications\n \nare\n \npotentially\n \nsent,\n \nand\n \nan\n \naudit\n \ntrail\n \nof\n \nthe\n \nmanager's\n \naction\n \n(including\n \nany\n \ncomments)\n \nis\n \nrecorded.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nreview\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nStaff\n \nthat\n \nare\n \nawaiting\n \ntheir\n \napproval.\n \nThe\n \nManager\n \ncan\n \napprove\n \n(escalating\n \nto\n \nDirector)\n \nor\n \nreject\n \nthe\n \nrequest.\n \nThey\n \ncan\n \nalso\n \nadd\n \ncomments.\n \nPreconditions:\n \n-\n \nThe\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n<br>\n \n-\n \nThe\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \nmanage\n \ntraining\n \nrequests\n \nassigned\n \nto\n \nthem.\n \n \n-\n \nThere\n \nare\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nassigned\n \nor\n \nvisible\n \nto\n \nthe\n \nManager.\n \nPostconditions:\n \n-\n \n**On\n \nApproval:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Pending_Director`,\n \nand\n \na\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \nassigned\n \nDirector.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue\n \nfor\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \n**On\n \nRejection:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Rejected_Manager`.\n \nA\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue.\n \n \n-\n \n**Comments\n \nAdded:**\n \nAny\n \ncomments\n \nmade\n \nby\n \nthe\n \nManager\n \nare\n \nsaved\n \nwith\n \nthe\n \nrequest.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \ninto\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard\n \nand\n \nselects\n \n'Manage\n \nTraining\n \nRequests'\n \nor\n \nis\n \nnotified\n \nof\n \npending\n \nrequests.\n \n \n3.\n \nSystem\n \ndisplays\n \na\n \nlist\n \nof\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nthat\n \nare\n \nassigned\n \nto\n \nor\n \nvisible\n \nto\n \nthis\n \nManager.\n \n \n4.\n \nManager\n \nselects\n \na\n \nspecific\n \ntraining\n \nrequest\n \nto\n \nreview.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \nfull\n \ndetails\n \nof\n \nthe\n \nselected\n \ntraining\n \nrequest\n \n(Category,\n \nReason,\n \nSubmitted\n \nby,\n \nSubmission\n \nDate,\n \nany\n \nattached\n \ndocuments,\n \nhistory\n \nof\n \ncomments).\n \n \n6.\n \nManager\n \nreviews\n \nthe\n \nrequest\n \ndetails.\n \n \n7.\n \nManager\n \ndecides\n \nto:\n\na.\n \n**Approve:**\n \nManager\n \nclicks\n \nthe\n \n'Approve'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments.\n \n \nb.\n \n**Reject:**\n \nManager\n \nclicks\n \nthe\n \n'Reject'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments\n \n(often\n \nmandatory\n \nfor\n \nrejection).\n \n \n8.\n \n**If\n \nApprove\n \n(7a):**\n \n \na.\n \nSystem\n \nvalidates\n \nany\n \ncomments\n \n(if\n \napplicable\n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Pending_Director`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \napproval,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \nrelevant\n \nDirector\n \nand/or\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \napproved\n \nand\n \nforwarded\n \nto\n \nDirector.\"\n \n \n9.\n \n**If\n \nReject\n \n(7b):**\n \n \na.\n \nSystem\n \nvalidates\n \ncomments\n \n(if\n \napplicable,\n \ne.g.,\n \nensuring\n \nreason\n \nfor\n \nrejection\n \nis\n \nprovided).\n \n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Rejected_Manager`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \nrejection,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \nrejected.\"\n \n \n10.\n \nManager\n \ncan\n \nreturn\n \nto\n \nthe\n \nlist\n \nof\n \npending\n \nrequests\n \n(back\n \nto\n \nstep\n \n3)\n \nor\n \ntheir\n \ndashboard.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nTraining\n \nRequests\n \nFound**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"You\n \nhave\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests\n \nyet.\"\n \nExceptions:\n \n \n**AF1:\n \nRequest\n \nAlready\n \nProcessed:**\n \n \n-\n \nAt\n \nstep\n \n4,\n \nif\n \nthe\n \nselected\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \n`Pending_Manager`\n \nstatus\n \n(e.g.,\n \nprocessed\n \nby\n \nanother\n \nmanager\n \nor\n \nsystem\n \nupdate):\n \n \n-\n \nSystem\n \ndisplays\n \na\n \nmessage\n \n\"This\n \nrequest\n \nhas\n \nalready\n \nbeen\n \nprocessed\n \nor\n \nis\n \nno\n \nlonger\n \navailable\n \nfor\n \naction.\"\n \n \n-\n \nManager\n \nreturns\n \nto\n \nthe\n \nupdated\n \nlist\n \nof\n \nrequests.\n \n<br>\n \n**AF2:\n \nProvide\n \nComments\n \nOnly\n \n(No\n \nStatus\n \nChange):**\n \n \n-\n \nSome\n \nsystems\n \nmight\n \nallow\n \na\n \nmanager\n \nto\n \nadd\n \ncomments\n \nwithout\n \nimmediately\n \napproving/rejecting,\n \nkeeping\n \nit\n \nin\n \n`Pending_Manager`.\n \nIf\n \nthis\n \nis\n \na\n \nfeature:\n \n \n-\n \nManager\n \nadds\n \ncomments\n \nand\n \nclicks\n \n'Save\n \nComments'.\n\n-\n \nSystem\n \nsaves\n \ncomments\n \nand\n \ntimestamp.\n \nRequest\n \nremains\n \n`Pending_Manager`.\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \nDaily\n \nto\n \nWeekly,\n \ndepending\n \non\n \nvolume\n \nof\n \nrequests.\n \nBusiness\n \nRules:\n \nBR-SR002-1,\n \nBR-SR002-2,\n \nBR-SR002-3,\n \nBR-SR002-4,\n \nBR-SR002-5\n \nRelated\n \nUse\n \nCases:\n \n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n \n(Next\n \nstep\n \nin\n \nworkflow),\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nManage\n \nTraining\n \nRequests\n \nList\n \n(Manager),\n \nTraining\n \nRequest\n \nDetail\n \nView\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nManager\n \ncan\n \nonly\n \nact\n \non\n \nrequests\n \ncurrently\n \nin\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \nThe\n \nsystem\n \ncorrectly\n \nroutes\n \nrequests\n \nto\n \nthe\n \nappropriate\n \nManager\n \nbased\n \non\n \norganizational\n \nstructure\n \nor\n \nassignment\n \nrules.\n \n \n-\n \nNotification\n \nmechanisms\n \nare\n \nin\n \nplace.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR001-\n1\n \nActionable\n \nStatus\n \nanagers\n \ncan\n \nonly\n \napprove\n \nor\n \nreject\n \ntraining\n \nrequests\n \nthat\n \nare\n \nin\n \n`Pending_Manager`\n \nstatus\n \nand\n \nassigned/visible\n \nto\n \nthem.\n \nBR-MR001-\n2\n \nComment\n \non\n \naction\n \nComments\n \nmay\n \nbe\n \noptional\n \nfor\n \napproval\n \nbut\n \nshould\n \nbe\n \nstrongly\n \nencouraged\n \nor\n \nmandatory\n \nfor\n \nrejection\n \nto\n \nprovide\n \nfeedback\n \nto\n \nthe\n \nStaff.\n \nBR-MR001-\n3\n \nStatus\n \nTransition\n \non\n \nApproval\n \nUpon\n \nManager\n \napproval,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Pending_Director`.\n \nBR-MR001-\n4\n \nStatus\n \nTransition\n \non\n \nRejection\n \nUpon\n \nManager\n \nrejection,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Rejected_Manager`.\n \nBR-MR001-\n5\n \n \nAudit\n \nTrail\n               \nAll\n \nactions\n \ntaken\n \nby\n \nthe\n \nManager\n \n(approval,\n \nrejection,\n \ncomments),\n \nalong\n \nwith\n \n`user_id`\n \nand\n \n`timestamp`,\n \nmust\n \nbe\n \nlogged\n \nin\n \nthe\n \nrequest's\n \nhistory\n \nfor\n \naudit\n \npurposes.\n \nBR-MR001-\n6\n  \n \nNotification\n \n(Workflow)\n       \n \nUpon\n \napproval,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \nrelevant\n \nDirector(s).\n \nUpon\n \nrejection,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nBR-MR001-\n7\n  \n \nView\n \nPermissions\n    \nManagers\n \nshould\n \nonly\n \nsee\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nteam/department\n \nor\n \nthose\n \nexplicitly\n \nassigned\n \nto\n \nthem\n \nfor\n \napproval,\n \nunless\n \nthey\n \nhave\n \nbroader\n \nadministrative\n \nviewing\n \nrights.\n\n1.2.2.\n \nUC-MR002:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR002\n \nUse\n \nCase\n \nName:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \naccesses\n \nand\n \nviews\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department).\n \nThe\n \ndisplayed\n \nhistory\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort\n \nthe\n \ndata\n \nfor\n \neasier\n \nreview .\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nview\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department,\n \nor\n \nall\n \nrequests\n \nif\n \nthey\n \nhave\n \nsufficient\n \npermissions).\n \nThis\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments.\n \nPreconditions:\n \n-\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \naccess\n \ntraining\n \nrequest\n \nhistorical\n \nrecords.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nManager\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nor\n \ndetailed\n \nview\n \nof\n \nhistorical\n \ntraining\n \nrequests,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort.\n \n \n-\n \n**Failure\n \n(No\n \nHistory):**\n \nIf\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests\n \nare\n \nfound,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \ninto\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n \n<br>\n \n3.\n \nManager\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nTraining\n \nRequest\n \nHistory'.\n \n \n4.\n \nSystem\n \nretrieves\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \nrelevant\n \nto\n \nthe\n \nManager's\n \nscope\n \n(e.g.,\n \nrequests\n \nsubmitted\n \nby\n \ntheir\n \nteam,\n \nrequests\n \nthey\n \nmanaged).\n \nThis\n \nincludes\n \nrequest\n \ndetails,\n \nstatus\n \nchange\n \nhistory,\n \napprover/rejecter\n \ndetails,\n \ntimestamps,\n \nand\n \ncomments.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \ntraining\n \nrequest\n \nhistory,\n \ntypically\n \nin\n \na\n \nlist\n \nformat,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \nhistorical\n \nentry\n \nor\n \nrequest.\n \n \n6.\n \nManager\n \ncan\n \napply\n \nfilters\n \n(e.g.,\n \nby\n \nStaff\n \nmember,\n \ndate\n \nrange,\n \nstatus,\n \ntraining\n \ncategory)\n \nor\n \nsort\n \nthe\n \nhistory.\n\n7.\n \nManager\n \ncan\n \nselect\n \na\n \nspecific\n \nhistorical\n \nrequest\n \nto\n \nview\n \nits\n \ncomplete\n \naudit\n \ntrail\n \nand\n \ndetails.\n \n \n8.\n \nManager\n \ncan\n \nclose\n \nthe\n \nhistory\n \nview\n \nand\n \nreturn\n \nto\n \nthe\n \ndashboard\n \nor\n \nprevious\n \nscreen.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nHistorical\n \nData\n \nFound:**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests:\n \n \n4a.i.\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \navailable\n \nfor\n \nthe\n \nselected\n \ncriteria.\"\n \n \n**AF2:\n \nFiltering/Sorting\n \nApplied:**\n \n \n6a.\n \nManager\n \napplies\n \nfilters\n \nor\n \nsorting\n \ncriteria.\n \n \n6b.\n \nSystem\n \nre-queries\n \nand\n \ndisplays\n \nthe\n \nhistorical\n \ndata\n \naccording\n \nto\n \nthe\n \napplied\n \ncriteria.\n \nExceptions:\n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nhistory,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nManager’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nPeriodically,\n \nfor\n \nreview,\n \naudit,\n \nor\n \ntracking\n \npurposes.\n \nBusiness\n \nRules:\n \nBR-MR002-1,\n \nBR-MR002-2,\n \nBR-MR002-3,\n \nBR-MR002-4\n \nRelated\n \nUse\n \nCases:\n \n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nTraining\n \nRequest\n \nHistory\n \nScreen\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nscope\n \nof\n \nhistorical\n \ndata\n \nvisible\n \nto\n \na\n \nManager\n \nis\n \ndefined\n \nby\n \ntheir\n \nrole\n \nand\n \npermissions.\n \n \n-\n \nHistorical\n \ndata\n \nis\n \naccurately\n \nlogged\n \nand\n \nmaintained\n \nby\n \nthe\n \nsystem.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR002-\n1\n  \n \nData\n \nIntegrity\n \n&\n \nRead-Only\n \nHistorical\n \ntraining\n \nrequest\n \ndata\n \nmust\n \nbe\n \npresented\n \nas\n \nread-only.\n \nNo\n \nmodifications\n \nto\n \nhistorical\n \nrecords\n \nare\n \npermitted\n \nthrough\n \nthis\n \nview.\n \nBR-MR002-\n2\n \n \nScope\n \nof\n \nView\n         \nManagers\n \nshould\n \nonly\n \nbe\n \nable\n \nto\n \nview\n \nthe\n \nhistory\n \nof\n \ntraining\n \nrequests\n \nthat\n \nfall\n \nwithin\n \ntheir\n \ndefined\n \nscope\n \n(e.g.,\n \ntheir\n \ndirect\n\nreports,\n \ntheir\n \ndepartment,", "match_type": "exact", "matched_with": "1.2.Manager"}, {"heading_number": "1.3", "title": "1.3 Screen: DashBoard", "Content": "container\n \nfor\n \nthe\n \nregistration\n \nform.\n \n \nRegistration\n \nForm\n \n(Dedicated)\n \nform#registerF\norm\n \nAllows\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount\n \n(defaults\n \nto\n \nSTAFF).\n \nFields:\n \nFull\n \nName,\n \nUsername,\n \nEmail,\n \nPassword,\n \nConfirm\n \nPassword.\n \nIncludes\n \npassword\n \nstrength\n \nindicator.\n \nRegister\n \nFull\n \nName\n \nInput\n \ninput#name\n \nField\n \nfor\n \nnew\n \nuser's\n \nfull\n \nname.\n \n \nRegister\n \nUsername\n \nInput\n \ninput#usernam\ne\n \nField\n \nfor\n \nnew\n \nuser's\n \nusername.\n \n \nRegister\n \nEmail\n \nInput\n \ninput#email\n \nField\n \nfor\n \nnew\n \nuser's\n \nemail.\n \n \nRegister\n \nPassword\n \nInput\n \ninput#password\n \nField\n \nfor\n \nnew\n \nuser's\n \npassword.\n \nMinlength\n \n8,\n \nwith\n \nstrength\n \nchecker.\n \nPassword\n \nStrength\n \nIndicator\n \ndiv#passwordS\ntrength\n \nVisual\n \nfeedback\n \non\n \nthe\n \nstrength\n \nof\n \nthe\n \nentered\n \npassword.\n \nChanges\n \nclass\n \nbased\n \non\n \npassword\n \ncomplexity.\n \nConfirm\n \nPassword\n \nInput\n \n(Register)\n \ninput#confirmP\nassword\n \nField\n \nto\n \nconfirm\n \nthe\n \nnew\n \npassword.\n \nValidated\n \nagainst\n \nthe\n \npassword\n \nfield.\n \nAgree\n \nto\n \nTerms\n \nCheckbox\n \ninput#agreeTer\nms\n \nCheckbox\n \nfor\n \nusers\n \nto\n \nagree\n \nto\n \nterms\n \nand\n \nconditions.\n \nRequired\n \nfor\n \nregistration.\n \nLogin\n \nLink\n \n(Register\n \nPage)\n \na\n \n(th:href=\"@{/lo\ngin}\")\n \nNavigates\n \nto\n \nthe\n \nlogin\n \npage.\n \n\"Đăng\n \nnhập\n \nngay\"\n \n \n \n \n1.3\n \nScreen:\n \nDashBoard\n \nUI\n \nDesign\n\n(\nStaffDashboardPage\n)\n \n \n(\nManagerDashboardPage\n)\n \n \n(\nDirectorDashboardPage\n)\n\n(\nCEODashboardPage\n)\n \n \n{\n \n  \n\"screenName\"\n:\n \n\"DashboardScreen\"\n,\n \n  \n\"title\"\n:\n \n\"Dashboard\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"AppLayoutWithSidebar\"\n,\n \n    \n\"header\"\n:\n \n{\n \n      \n\"appName\"\n:\n \n\"RetailOnboardPro\"\n,\n\n\"userProfile\"\n:\n \n{\n\"id\"\n:\n \n\"userProfileDropdown\"\n,\n \n\"userName\"\n:\n \n\"[User's\n \nName]\"\n,\n \n\"userRole\"\n:\n \n\"[User's\n \nRole]\"\n,\n \n\"actions\"\n:\n \n[\n\"View\n \nProfile\"\n,\n \n\"Logout\"\n]}\n \n    \n},\n \n    \n\"sidebar\"\n:\n \n{\n \n      \n\"id\"\n:\n \n\"appSidebar\"\n,\n \n      \n\"navLinks\"\n:\n \n[\n \n//\n \nConditional\n \nbased\n \non\n \nrole\n \n        \n{\n\"id\"\n:\n \n\"dashboardLink\"\n,\n \n\"text\"\n:\n \n\"Dashboard\"\n,\n \n\"icon\"\n:\n \n\"dashboard_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"DashboardScreen\"\n},\n \n        \n{\n\"id\"\n:\n \n\"trainingRequestsLink\"\n,\n \n\"text\"\n:\n \n\"Training\n \nRequests\"\n,\n \n\"icon\"\n:\n \n\"training_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestListScreen_Training\"\n},\n \n        \n{\n\"id\"\n:\n \n\"createTrainingRequestLink\"\n,\n \n\"text\"\n:\n \n\"Create\n \nNew\n \nRequest\"\n,\n \n\"icon\"\n:\n \n\"add_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"CreateTrainingRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n},\n \n        \n{\n\"id\"\n:\n \n\"roleChangeRequestsLink\"\n,\n \n\"text\"\n:\n \n\"Role\n \nChange\n \nRequests\"\n,\n \n\"icon\"\n:\n \n\"role_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"RoleChangeRequestScreen_StaffView\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n},\n \n        \n{\n\"id\"\n:\n \n\"approveRoleChangesLink\"\n,\n \n\"text\"\n:\n \n\"Approve\n \nRole\n \nChanges\"\n,\n \n\"icon\"\n:\n \n\"approve_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"ApproveRoleChangeRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==CEO_OR_DIRECTOR\"\n},\n \n        \n{\n\"id\"\n:\n \n\"userManagementLink\"\n,\n \n\"text\"\n:\n \n\"User\n \nManagement\"\n,\n \n\"icon\"\n:\n \n\"users_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"UserManagementScreen\"\n,\n \n\"condition\"\n:\n \n\"role==CEO_OR_ADMIN\"\n},\n \n        \n{\n\"id\"\n:\n \n\"profileLink\"\n,\n \n\"text\"\n:\n \n\"Profile\"\n,\n \n\"icon\"\n:\n \n\"profile_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"ProfileScreen\"\n},\n \n        \n{\n\"id\"\n:\n \n\"logoutLink\"\n,\n \n\"text\"\n:\n \n\"Logout\"\n,\n \n\"icon\"\n:\n \n\"logout_icon\"\n,\n \n\"action\"\n:\n \n\"LOGOUT\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC003_UserLogout\"\n}\n \n      \n]\n \n    \n},\n \n    \n\"mainContent\"\n:\n \n{\n \n      \n\"pageHeader\"\n:\n \n{\n\"title\"\n:\n \n\"Dashboard\"\n,\n \n\"roleBadge\"\n:\n \n\"[User's\n \nRole]\"\n,\n \n\"actions\"\n:\n \n[{\n\"id\"\n:\n \n\"createNewButton_Training\"\n,\n \n\"text\"\n:\n \n\"Create\n \nNew\"\n,\n \n\"navigatesTo\"\n:\n \n\"CreateTrainingRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n}]},\n \n      \n\"statsCards\"\n:\n \n[\n \n        \n{\n\"id\"\n:\n \n\"pendingStats\"\n,\n \n\"title\"\n:\n \n\"Pending\"\n,\n \n\"value\"\n:\n \n\"[stats.pending]\"\n,\n \n\"icon\"\n:\n \n\"pending_icon\"\n},\n \n        \n{\n\"id\"\n:\n \n\"approvedStats\"\n,\n \n\"title\"\n:\n \n\"Approved\"\n,\n \n\"value\"\n:\n \n\"[stats.approved]\"\n,\n \n\"icon\"\n:\n \n\"approved_icon\"\n},\n \n        \n{\n\"id\"\n:\n \n\"rejectedStats\"\n,\n \n\"title\"\n:\n \n\"Rejected\"\n,\n \n\"value\"\n:\n \n\"[stats.rejected]\"\n,\n \n\"icon\"\n:\n \n\"rejected_icon\"\n}\n\n],\n \n      \n\"infoPanel\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"systemInfoPanel\"\n,\n \n        \n\"title\"\n:\n \n\"V\nề\n \nh\nệ\n \nth\nố\nng\n \nRetailOnboardPro\"\n,\n \n        \n\"sections\"\n:\n \n[\n \n          \n{\n\"title\"\n:\n \n\"Ch\nứ\nc\n \nnăng\n \nchính\"\n,\n \n\"items\"\n:\n \n[\n\"Training\n \nRequests:\n \n...\"\n,\n \n\"Quy\n \ntrình\n \nphê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np:\n \n...\"\n]},\n \n          \n{\n\"title\"\n:\n \n\"Ch\nứ\nc\n \nnăng\n \nph\nụ\n\"\n,\n \n\"items\"\n:\n \n[\n\"Role\n \nRequests:\n \n...\"\n,\n \n\"Qu\nả\nn\n \nlý\n \nng\nườ\ni\n \ndùng:\n \n...\"\n]}\n \n        \n]\n \n      \n},\n \n      \n\"recentRequestsSection\"\n:\n \n{\n \n        \n\"title\"\n:\n \n\"Recent\n \nTraining\n \nRequests\n \n(Core\n \nFeature)\"\n,\n \n        \n\"actions\"\n:\n \n[{\n\"id\"\n:\n \n\"viewAllRequestsButton\"\n,\n \n\"text\"\n:\n \n\"View\n \nAll\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestListScreen_Training\"\n}],\n \n        \n\"list\"\n:\n \n{\n \n          \n\"id\"\n:\n \n\"recentRequestsList\"\n,\n \n          \n\"itemTemplate\"\n:\n \n{\n \n            \n\"title\"\n:\n \n\"[request.title]\n \n-\n \n[request.category]\"\n,\n \n            \n\"description\"\n:\n \n\"[request.shortDescriptionOrReason]\"\n,\n \n            \n\"metadata\"\n:\n \n\"[request.submissionDate]\n \n|\n \n[request.submitterRole]\"\n,\n \n            \n\"status\"\n:\n \n\"[request.status]\"\n,\n \n            \n\"viewAction\"\n:\n \n{\n\"id\"\n:\n \n\"viewRequestDetailButton\"\n,\n \n\"text\"\n:\n \n\"View\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestDetailScreen_Training\"\n,\n \n\"params\"\n:\n \n{\n\"requestId\"\n:\n \n\"[request.id]\"\n}}\n \n          \n},\n \n          \n\"dataSource\"\n:\n \n\"recentTrainingRequests\"\n \n        \n}\n \n      \n}\n \n    \n}\n \n  \n}\n \n}\n\nTable\n \n18:\n \nElement\n \ndescription\n \nof\n \nthe\n \nDashboar d\n \nscreen\n \nof\n \nStaff\n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nDashboard\n \nRole\n \nBadge\n \nspan.badge.bg-pr\nimary\n \nDisplays\n \nthe\n \ncurrent\n \nuser's\n \nrole.\n \nsec:authentication=\"principal.role\"\n \nPending\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(pending)\n \nShows\n \ncount\n \nof\n \npending\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.pending}\"),\n \nand\n \ntitle.\n \nApproved\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(approved)\n \nShows\n \ncount\n \nof\n \napproved\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.approved}\"),\n \nand\n \ntitle.\n \nRejected\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(rejected)\n \nShows\n \ncount\n \nof\n \nrejected\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.rejected}\"),\n \nand\n \ntitle.\n \nSystem\n \nFeatures\n \nInfo\n \nPanel\n \ndiv.alert.alert-inf\no\n \nProvides\n \noverview\n \nof\n \nsystem\n \nmain\n \nfunctionalities.\n \nMentions\n \nTraining\n \nRequests\n \nand\n \nRole\n \nRequests.\n \nRecent\n \nTraining\n \nRequests\n \nSection\n \ndiv.card\n \nLists\n \nrecent\n \ntraining\n \nrequests\n \nfor\n \nquick\n \naccess.\n \nIncludes\n \n\"Create\n \nNew\"\n \n(for\n \nStaff)\n \nand\n \n\"View\n \nAll\"\n \nbuttons.\n \nRecent\n \nRequest\n \nItem\n \n(Dashboard)\n \ndiv.card.border-st\nart-0\n \nDisplays\n \nsummary\n \nof\n \na\n \nsingle\n \nrecent\n \ntraining\n \nrequest.\n \nShows\n \ncreator,\n \ncategory,\n \nreason\n \nsnippet,\n \ndate,\n \nrole,\n \nstatus,\n \nand\n \na\n \nview\n \nbutton.\n \nLogin\n \nSuccess\n \nNotification\n \ndiv#loginNotifica\ntion.notification\n \nTemporary\n \npop-up\n \nshown\n \nafter\n \nsuccessful\n \nlogin.\n \nMessage:\n \n\"Login\n \nSuccessful.\n \nWelcome,\n \n[User]!\"\n \n \n \n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nMain\n \nNavigation\n \nBar\n \nnav.navbar\n \nProvides\n \ntop-level\n \nsite\n \nnavigation\n \nand\n \nuser\n \nactions.\n \nIncludes\n \nbrand\n \nlogo/name,\n \nnavigation\n \nlinks\n \n(if\n \nany\n \non\n \nthat\n \npage),\n \nand\n \na\n \nuser\n \ndropdown\n \nmenu.\n \nUser\n \nDropdown\n \n(Navbar)\n \nli.nav-item.dropd\nown\n \nAllows\n \nuser\n \nto\n \naccess\n \nprofile\n \nor\n \nlogout.\n \nTypically\n \ncontains\n \nlinks\n \nto\n \n'Profile'\n \nand\n \na\n \n'Logout'\n \nbutton/form.\n \nUser's\n \nname\n \nis\n \noften\n \ndisplayed.\n \nLogout\n \nButton/Link\n \nform\n \n(action=\"/logout\")\n \n/\n \na\n \nLogs\n \nthe\n \ncurrent\n \nuser\n \nout\n \nof\n \nthe\n \nsystem.\n \nCan\n \nbe\n \na\n \ndirect\n \nlink\n \nor\n \na\n \nform\n \nsubmission\n \ntriggering\n \nlogout.\n \nSidebar\n \nNavigation\n \ndiv.sidebar\n \nProvides\n \nprimary\n \nnavigation\n \nfor\n \nContains\n \na\n \nlist\n \nof\n \nlinks\n \n(ul.nav.nav-pills)\n \nto\n \ndifferent\n \napplication\n \nsections\n \nlike\n \nDashboard,\n \nRequests,\n\nauthenticated\n \nusers.\n \nProfile,\n \netc.\n \nConditional\n \nlinks\n \nbased\n \non\n \nrole.\n \nSidebar\n \nNavigation\n \nLink\n \na.nav-link\n \n(within\n \nsidebar)", "match_type": "exact", "matched_with": "1.3 Screen: DashBoard"}, {"heading_number": "1.7", "title": "1.7 Screen: Request List", "Content": "input#nam\ne\n \nField\n \nto\n \nupdate\n \nuser's\n \nfull\n \nname.\n \n \nProfile\n \nEmail\n \nInput\n \ninput#<PERSON><PERSON>\nl\n \n<PERSON>\n \nto\n \nupdate\n \nuser's\n \nemail.\n \n \nChange\n \nPassword\n \nForm\n \nform\n \n(action=\"\n@{/profile\n/update}\")\n \nAllows\n \nusers\n \nto\n \nchange\n \ntheir\n \npassword.\n \nFields:\n \nCurrent\n \nPassword,\n \nNew\n \nPassword,\n \nConfirm\n \nNew\n \nPassword.\n \nCurrent\n \nPassword\n \nInput\n \ninput#curr\nentPasswo\nrd\n \nField\n \nfor\n \ncurrent\n \npassword\n \n(for\n \nverification).\n \n \nNew\n \nPassword\n \nInput\n \ninput#new\nPassword\n \nField\n \nfor\n \nentering\n \nnew\n \npassword.\n \n \nConfirm\n \nNew\n \nPassword\n \nInput\n \ninput#conf\nirmPasswo\nrd\n \nField\n \nto\n \nconfirm\n \nthe\n \nnew\n \npassword.\n \nChecked\n \nagainst\n \nNew\n \nPassword\n \nvia\n \nJavaScript.\n \n \n \n1.7\n \nScreen:\n \nRequest\n \nList\n \n \n{\n\n\"screenName\":\n \n\"RequestListScreen_Training\",\n \n  \n\"title\":\n \n\"Request\n \nList\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\"title\":\n \n\"Request\n \nList\"},\n \n    \n\"infoPanel\":\n \n{\n \n      \n\"id\":\n \n\"requestListInfoPanel\",\n \n      \n\"title\":\n \n\"Thông\n \ntin\n \nvề\n \nhệ\n \nthống\n \nyêu\n \ncầu\",\n \n      \n\"text\":\n \n\"<PERSON><PERSON><PERSON>\n \nlà\n \nchức\n \nnăng\n \nchính\n \ncủa\n \nhệ\n \nthống...\",\n \n      \n\"roleSpecificInfo\":\n \n[\n \n        \n{\"role\":\n \n\"STAFF\",\n \n\"text\":\n \n\"<PERSON>h<PERSON>\n \nviên\n \n(STAFF):\n \n<PERSON><PERSON><PERSON>\n \nyêu\n \ncầu\n \nđào\n \ntạo\"},\n \n        \n{\"role\":\n \n\"MANAGER\",\n \n\"text\":\n \n\"Quản\n \nlý\n \n(MANAGER):\n \nXét\n \nduyệt\n \nbước\n \nđầu\"},\n \n        \n{\"role\":\n \n\"DIRECTOR\",\n \n\"text\":\n \n\"Giám\n \nđốc\n \n(DIRECTOR):\n \nXét\n \nduyệt\n \ncấp\n \ntrung\"},\n \n        \n{\"role\":\n \n\"CEO\",\n \n\"text\":\n \n\"CEO:\n \nPhê\n \nduyệt\n \ncuối\n \ncùng\"}\n \n      \n]\n \n    \n},\n \n    \n\"requestTable\":\n \n{\n \n      \n\"id\":\n \n\"trainingRequestTable\",\n \n      \n\"columns\":\n \n[\n \n        \n{\"header\":\n \n\"ID\",\n \n\"dataField\":\n \n\"id\"},\n \n        \n{\"header\":\n \n\"Category\",\n \n\"dataField\":\n \n\"category\"},\n \n        \n{\"header\":\n \n\"Creator\",\n \n\"dataField\":\n \n\"creatorName\"},\n \n        \n{\"header\":\n \n\"Created\n \nDate\",\n \n\"dataField\":\n \n\"createdDate\",\n \n\"format\":\n \n\"date\"},\n \n        \n{\"header\":\n \n\"Status\",\n \n\"dataField\":\n \n\"status\",\n \n\"displayType\":\n \n\"Badge\"},\n \n        \n{\"header\":\n \n\"Actions\",\n \n\"displayType\":\n \n\"ActionButtons\",\n \n\"actions\":\n \n[\n \n          \n{\"id\":\n \n\"viewDetailAction\",\n \n\"text\":\n \n\"View\",\n \n\"icon\":\n \n\"view_icon\",\n \n\"navigatesTo\":\n \n\"RequestDetailScreen_Training\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[row.id]\"}}\n \n        \n]}\n \n      \n],\n \n      \n\"dataSource\":\n \n\"trainingRequests_all_or_filtered\",\n \n//\n \nDepends\n \non\n \nuser\n \nrole\n \nand\n \nfilters\n\n\"pagination\":\n \ntrue,\n \n      \n\"searchable\":\n \ntrue,\n \n      \n\"sortable\":\n \ntrue\n \n    \n}\n \n  \n}\n \n}\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nCreateNewTr\nainingReques\ntPage\n \nButton\n \na\n \n(href=\"/requ\nests-view/cr\neate\")\n \nAllows\n \nStaff\n \nto\n \nnavigate\n \nto\n \nrequest\n \ncreation\n \npage.\n \nVisible\n \nonly\n \nto\n \nusers\n \nwith\n \nthe\n \n'STAFF'\n \nrole.\n \nTraining\n \nRequest\n \nInfo\n \nPanel\n \ndiv.alert.aler\nt-info\n \nExplains\n \ntraining\n \nrequest\n \nsystem\n \nand\n \napproval\n \nflow.\n \nDescribes\n \nroles\n \nin\n \napproval:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO.\n \nTraining\n \nRequests\n \nTable\n \ntable.table\n \nDisplays\n \ntraining\n \nrequests\n \nin\n \na\n \ntable.\n \nColumns\n \ninclude:\n \nID,\n \nCategory,\n \nCreator,\n \nCreated\n \nDate,\n \nStatus,\n \nActions.\n \nView\n \nTraining\n \nRequest\n \nButton\n \na.btn-primar\ny\n \n(in\n \ntable\n \nrow)\n \nNavigates\n \nto\n \nrequest\n \ndetail\n \npage.\n \nAllows\n \nusers\n \nto\n \nview\n \nmore\n \ndetailed\n \ninformation\n \nabout\n \na\n \nspecific\n \nrequest.", "match_type": "exact", "matched_with": "1.7 Screen: Request List"}, {"heading_number": "1.8", "title": "1.8 Screen: Request Details", "Content": "{\n \n  \n\"screenName\":\n \n\"RequestDetailScreen_Training\",\n \n  \n\"title\":\n \n\"Request\n \nDetails\n \n#[request.id]\n \n-\n \n[request.category]\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\n \n      \n\"title\":\n \n\"Request\n \nDetails\n \n#[request.id]\n \n-\n \n[request.category]\",\n \n      \n\"backButton\":\n \n{\"text\":\n \n\"←\n \nBack\",\n \n\"navigatesTo\":\n \n\"RequestListScreen_Training\"},\n \n      \n\"statusBadge\":\n \n{\"text\":\n \n\"[request.status]\",\n \n\"type\":\n \n\"[request.statusType]\"}\n \n    \n},\n \n    \n\"requestInformationSection\":\n \n{\n \n      \n\"id\":\n \n\"requestInfoCard\",\n \n      \n\"title\":\n \n\"Request\n \nInformation\",\n \n      \n\"fields\":\n \n[\n \n        \n{\"label\":\n \n\"Category:\",\n \n\"value\":\n \n\"[request.category]\"},\n \n        \n{\"label\":\n \n\"Creator:\",\n \n\"value\":\n \n\"[request.creatorName]\"},\n \n        \n{\"label\":\n \n\"Created\n \nDate:\",\n \n\"value\":\n \n\"[request.createdDate]\",\n \n\"format\":\n \n\"datetime\"},\n \n        \n{\"label\":\n \n\"Last\n \nUpdated:\",\n \n\"value\":\n \n\"[request.lastUpdatedDate]\",\n \n\"format\":\n \n\"datetime\"},\n \n        \n{\"label\":\n \n\"Description:\",\n \n\"value\":\n \n\"[request.description]\",\n \n\"displayType\":\n \n\"MultilineText\"}\n \n      \n]\n\n},\n \n    \n\"approvalActionsSection\":\n \n{\n \n//\n \nConditional\n \nbased\n \non\n \nrole\n \nand\n \nrequest\n \nstatus\n \n      \n\"id\":\n \n\"approvalActionForm\",\n \n      \n\"condition\":\n \n\"canCurrentUserActOnRequest\",\n \n      \n\"form\":\n \n{\n \n        \n\"fields\":\n \n[\n \n          \n{\"type\":\n \n\"TextArea\",\n \n\"id\":\n \n\"approverComments\",\n \n\"label\":\n \n\"Comments\n \n(Optional\n \nfor\n \nApprove,\n \nRequired\n \nfor\n \nReject)\"}\n \n        \n],\n \n        \n\"actions\":\n \n[\n \n          \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"approveRequestButton\",\n \n\"text\":\n \n\"Approve\",\n \n\"triggersUseCase\":\n \n\"[RelevantApproveUseCase]\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[request.id]\"}},\n \n          \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"rejectRequestButton\",\n \n\"text\":\n \n\"Reject\",\n \n\"triggersUseCase\":\n \n\"[RelevantRejectUseCase]\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[request.id]\"}}\n \n        \n]\n \n      \n}\n \n    \n},\n \n    \n\"approvalHistorySection\":\n \n{\n \n      \n\"id\":\n \n\"approvalHistoryTimeline\",\n \n      \n\"title\":\n \n\"Approval\n \nHistory\",\n \n      \n\"timelineItems\":\n \n[\n \n//\n \nLoop\n \nthrough\n \nhistory\n \n        \n{\n \n          \n\"actor\":\n \n\"[historyItem.actorName]\n \n([historyItem.actorRole])\",\n \n          \n\"action\":\n \n\"[historyItem.actionTaken]\",\n \n//\n \ne.g.,\n \nCreated,\n \nApproved,\n \nRejected\n \n          \n\"timestamp\":\n \n\"[historyItem.timestamp]\",\n \n          \n\"comments\":\n \n\"[historyItem.comments]\",\n \n\"condition\":\n \n\"historyItem.comments_exists\"\n \n        \n}\n \n      \n],\n \n      \n\"dataSource\":\n \n\"requestApprovalHistory\"\n \n    \n}\n \n  \n}\n\n}\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nRequest\n \nStatus\n \nBadge\n \n(Detail)\n \nspan.badge\n \nDisplays\n \nthe\n \ncurrent\n \nstatus\n \nof\n \nthe\n \ntraining\n \nrequest.\n \nE.g.,\n \nPending,\n \nApproved,\n \nRejected,\n \nwith\n \ncorresponding\n \ncolors.\n \nRequest\n \nInformation\n \nSection\n \ndiv.card-b\nody\n \nDisplays\n \ncore\n \ndetails\n \nof\n \nthe\n \nrequest\n \n(Category,\n \nCreator,\n \netc.)\n \n \nApprove\n \nRequest\n \nForm\n \nform\n \n(action=\"...\n/approve\")\n \nAllows\n \nauthorized\n \nusers\n \nto\n \napprove\n \nthe\n \ntraining\n \nrequest.\n \nIncludes\n \noptional\n \ncomments\n \ntextarea.\n \nVisibility\n \ndepends\n \non\n \nrole\n \nand\n \nrequest\n \nstatus.\n \nApprove\n \nComments\n \nTextarea\n \ntextarea#a\npprove-co\nmments\n \nFor\n \nadding\n \ncomments\n \nwhen\n \napproving\n \na\n \nrequest.\n \n \nApprove\n \nRequest\n \nButton\n \nbutton.btn-\nsuccess\n \nSubmits\n \nthe\n \napproval\n \nfor\n \nthe\n \ntraining\n \nrequest.\n \n \nReject\n \nRequest\n \nForm\n \nform\n \n(action=\"...\n/reject\")\n \nAllows\n \nauthorized\n \nusers\n \nto\n \nreject\n \nthe\n \ntraining\n \nrequest.\n \nIncludes\n \nrequired\n \ncomments/reason\n \ntextarea.\n \nVisibility\n \ndepends\n \non\n \nrole\n \nand\n \nrequest\n \nstatus.\n \nReject\n \nComments\n \nTextarea\n \ntextarea#re\nject-comm\nents\n \nFor\n \nproviding\n \na\n \nreason\n \nwhen\n \nrejecting\n \na\n \nrequest.\n \nRequired\n \nfield.\n \nReject\n \nRequest\n \nButton\n \nbutton.btn-\ndanger\n \nSubmits\n \nthe\n \nrejection\n \nfor\n \nthe\n \ntraining\n \nrequest.\n \n \nApproval\n \nHistory\n \nTimeline\n \nul.timeline\n \nDisplays\n \nstep-by-step\n \napproval\n \nhistory\n \nof\n \nthe\n \nrequest.\n \nEach\n \nli\n \nshows\n \naction\n \ntype\n \n(Create/Approve/Reject),\n \nuser,\n \nrole,\n \ntimestamp,\n \nand\n \nany\n \ncomments.", "match_type": "exact", "matched_with": "1.8 Screen: Request Details"}]