# Phoenix Pipeline Server

Real-time WebSocket server for Phoenix Pipeline execution with FastAPI.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd back_end/server
pip install -r requirements.txt
```

### 2. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional)
nano .env
```

### 3. Validate Configuration

```bash
python validate_config.py
```

### 4. Start Server

```bash
python run.py
```

Or directly with uvicorn:

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Test Connection

-   **Health Check**: http://localhost:8000/health
-   **WebSocket**: ws://localhost:8000/ws
-   **API Docs**: http://localhost:8000/docs

## 📡 API Endpoints

### Root & Health

-   `GET /` - API root with endpoint documentation
-   `GET /health` - Detailed health check for monitoring
-   `GET /health/simple` - Simple health check for load balancers

### REST API v1 (`/api/v1`)

-   `GET /api/v1/pipeline/status` - Get current pipeline status
-   `POST /api/v1/pipeline/start` - Start pipeline execution
-   `POST /api/v1/pipeline/stop` - Stop pipeline execution
-   `POST /api/v1/pipeline/pause` - Pause pipeline execution
-   `POST /api/v1/pipeline/resume` - Resume pipeline execution
-   `GET /api/v1/websocket/stats` - Get WebSocket connection statistics

### WebSocket

-   `ws://localhost:8000/ws` - Real-time communication

### Documentation

-   `GET /docs` - Interactive API documentation (Swagger UI)
-   `GET /redoc` - Alternative API documentation (ReDoc)

## 🔌 WebSocket Protocol

### Client → Server Messages

```json
// Ping server
{ "type": "ping" }

// Start pipeline
{
  "type": "pipeline_start",
  "data": { "document_id": "doc123" }
}

// Stop pipeline
{ "type": "pipeline_stop" }

// Get current status
{ "type": "get_status" }
```

### Server → Client Messages

```json
// Pong response
{
  "type": "pong",
  "data": { "timestamp": "2024-01-01T12:00:00" }
}

// Connection status
{
  "type": "connection_status",
  "data": {
    "status": "connected",
    "client_id": "uuid",
    "timestamp": "2024-01-01T12:00:00"
  }
}

// Pipeline status update
{
  "type": "pipeline_status",
  "data": {
    "pipeline_id": "uuid",
    "status": "running",
    "current_step": "document_processor",
    "steps": [...],
    "overall_progress": 45.5
  }
}

// Pipeline log message
{
  "type": "pipeline_log",
  "data": {
    "level": "info",
    "message": "Step completed successfully",
    "step_id": "document_processor",
    "timestamp": "2024-01-01T12:00:00"
  }
}

// Error message
{
  "type": "error",
  "data": { "message": "Error description" }
}
```

## 📊 Pipeline Steps

The server manages these pipeline steps:

1. **Document Processor** - Process uploaded documents
2. **Business Flow Detector** - Detect business flow patterns
3. **Extract and Process to JSON** - Convert to structured JSON
4. **Generate HTML Structure UIDL** - Create UI description language
5. **Generate Test Case** - Generate test scenarios
6. **Test Case Evaluator** - Evaluate test results

## 🎯 Connection Status Colors

-   🟢 **Green (Connected)** - WebSocket connected successfully
-   🟡 **Yellow (Connecting)** - Attempting to connect
-   🔴 **Red (Disconnected)** - Connection lost or failed

## 🔧 Configuration

### Environment Variables

```bash
# Server settings
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=info

# WebSocket settings
WS_HEARTBEAT_INTERVAL=30
MAX_CONNECTIONS=100

# CORS settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Logging

Logs are written to:

-   Console (stdout)
-   `server.log` file

## 🧪 Testing

### Test WebSocket Connection

```bash
# Using wscat (install: npm install -g wscat)
wscat -c ws://localhost:8000/ws

# Send test message
{"type": "ping"}
```

### Test REST API

```bash
# Health check
curl http://localhost:8000/health

# Get pipeline status
curl http://localhost:8000/api/v1/pipeline/status

# Start pipeline
curl -X POST http://localhost:8000/api/v1/pipeline/start \
  -H "Content-Type: application/json" \
  -d '{"document_id": "test123"}'

# Stop pipeline
curl -X POST http://localhost:8000/api/v1/pipeline/stop

# Get WebSocket stats
curl http://localhost:8000/api/v1/websocket/stats
```

## 📁 File Structure

```
back_end/server/
├── main.py              # FastAPI application
├── websocket_manager.py # WebSocket connection manager
├── pipeline_service.py  # Pipeline execution service
├── models.py           # Pydantic data models
├── start_server.py     # Server startup script
├── requirements.txt    # Python dependencies
├── README.md          # This file
└── server.log         # Log file (created at runtime)
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**

    ```bash
    # Kill process using port 8000
    lsof -ti:8000 | xargs kill -9
    ```

2. **WebSocket connection failed**

    - Check CORS settings
    - Verify server is running
    - Check firewall settings

3. **Module import errors**
    - Ensure all dependencies are installed
    - Check Python path

### Debug Mode

Start server with debug logging:

```bash
LOG_LEVEL=debug python start_server.py
```

## 🔄 Development

### Hot Reload

Server automatically reloads when code changes (with `--reload` flag).

### Adding New Endpoints

1. Add route to `main.py`
2. Update models in `models.py` if needed
3. Add business logic to appropriate service

### Adding New WebSocket Messages

1. Define message models in `models.py`
2. Add handler in `handle_client_message()` function
3. Update protocol documentation
