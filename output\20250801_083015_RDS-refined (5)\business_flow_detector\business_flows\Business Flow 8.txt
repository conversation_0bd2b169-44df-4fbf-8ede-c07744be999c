1. Manager navigates to ManagerDashboardPage and selects 'Manage Training Requests'
2. Manager selects a specific training request in ManageTrainingRequestsListPage
3. Manager reviews request details in TrainingRequestDetailsPage
4. Manager clicks 'Approve' button in TrainingRequestDetailsPage
5. Manager enters optional comments in TrainingRequestDetailsPage
6. Manager clicks 'Reject' button in TrainingRequestDetailsPage
7. Manager enters optional comments in TrainingRequestDetailsPage
8. Manager navigates back to ManageTrainingRequestsListPage or ManagerDashboardPage
Business Process Context: `{"heading_number": "1.2", "title": "1.2.Manager", "Content": "1.2.1.\n \nUse\n \nCase:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \n \nDiagram:\n \n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n\n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nand\n \nmanage\n \nrequests\n \nassigned\n \nto\n \nthem.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nhistory\n \nof\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"UpdateStatusPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nor\n \nmodal\n \nfor\n \nManagers\n \nto\n \nupdate\n \nthe\n \nstatus\n \nof\n \na\n \nrequest.\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \n/\n \nManage\n \nrequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nhistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"UpdateStatusPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \n'Change\n \nrequest\n \nstatus'\n \naction\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nManager\n \nuser.\"\n \n}\n \n \n \nUse\n \ncase\n \ndescription\n \nof\n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR001\n\nUse\n \nCase\n \nName:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem,\n \nStaff\n \n(as\n \ninitiator),\n \nDirector\n \n(as\n \nnext\n \napprover)\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \nreviews\n \na\n \npending\n \ntraining\n \nrequest\n \nand\n \ntakes\n \nappropriate\n \naction\n \n(Approve\n \nor\n \nReject).\n \nThe\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nin\n \nthe\n \nsystem\n \nto\n \nreflect\n \nthe\n \ndecision\n \n(\nPending_Director\n \nor\n \nRejected_Manager\n),\n \nrelevant\n \nnotifications\n \nare\n \npotentially\n \nsent,\n \nand\n \nan\n \naudit\n \ntrail\n \nof\n \nthe\n \nmanager's\n \naction\n \n(including\n \nany\n \ncomments)\n \nis\n \nrecorded.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nreview\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nStaff\n \nthat\n \nare\n \nawaiting\n \ntheir\n \napproval.\n \nThe\n \nManager\n \ncan\n \napprove\n \n(escalating\n \nto\n \nDirector)\n \nor\n \nreject\n \nthe\n \nrequest.\n \nThey\n \ncan\n \nalso\n \nadd\n \ncomments.\n \nPreconditions:\n \n-\n \nThe\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n<br>\n \n-\n \nThe\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \nmanage\n \ntraining\n \nrequests\n \nassigned\n \nto\n \nthem.\n \n \n-\n \nThere\n \nare\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nassigned\n \nor\n \nvisible\n \nto\n \nthe\n \nManager.\n \nPostconditions:\n \n-\n \n**On\n \nApproval:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Pending_Director`,\n \nand\n \na\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \nassigned\n \nDirector.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue\n \nfor\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \n**On\n \nRejection:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Rejected_Manager`.\n \nA\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue.\n \n \n-\n \n**Comments\n \nAdded:**\n \nAny\n \ncomments\n \nmade\n \nby\n \nthe\n \nManager\n \nare\n \nsaved\n \nwith\n \nthe\n \nrequest.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \nin\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard\n \nand\n \nselects\n \n'Manage\n \nTraining\n \nRequests'\n \nor\n \nis\n \nnotified\n \nof\n \npending\n \nrequests.\n \n \n3.\n \nSystem\n \ndisplays\n \na\n \nlist\n \nof\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nthat\n \nare\n \nassigned\n \nto\n \nor\n \nvisible\n \nto\n \nthis\n \nManager.\n \n \n4.\n \nManager\n \nselects\n \na\n \nspecific\n \ntraining\n \nrequest\n \nto\n \nreview.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \nfull\n \ndetails\n \nof\n \nthe\n \nselected\n \ntraining\n \nrequest\n \n(Category,\n \nReason,\n \nSubmitted\n \nby,\n \nSubmission\n \nDate,\n \nany\n \nattached\n \ndocuments,\n \nhistory\n \nof\n \ncomments).\n \n \n6.\n \nManager\n \nreviews\n \nthe\n \nrequest\n \ndetails.\n \n \n7.\n \nManager\n \ndecides\n \nto:\n\na.\n \n**Approve:**\n \nManager\n \nclicks\n \nthe\n \n'Approve'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments.\n \n \nb.\n \n**Reject:**\n \nManager\n \nclicks\n \nthe\n \n'Reject'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments\n \n(often\n \nmandatory\n \nfor\n \nrejection).\n \n \n8.\n \n**If\n \nApprove\n \n(7a):**\n \n \na.\n \nSystem\n \nvalidates\n \nany\n \ncomments\n \n(if\n \napplicable\n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Pending_Director`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \napproval,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \nrelevant\n \nDirector\n \nand/or\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \napproved\n \nand\n \nforwarded\n \nto\n \nDirector.\"\n \n \n9.\n \n**If\n \nReject\n \n(7b):**\n \n \na.\n \nSystem\n \nvalidates\n \ncomments\n \n(if\n \napplicable,\n \ne.g.,\n \nensuring\n \nreason\n \nfor\n \nrejection\n \nis\n \nprovided).\n \n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Rejected_Manager`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \nrejection,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \nrejected.\"\n \n \n10.\n \nManager\n \ncan\n \nreturn\n \nto\n \nthe\n \nlist\n \nof\n \npending\n \nrequests\n \n(back\n \nto\n \nstep\n \n3)\n \nor\n \ntheir\n \ndashboard.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nTraining\n \nRequests\n \nFound**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"You\n \nhave\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests\n \nyet.\"\n \nExceptions:\n \n \n**AF1:\n \nRequest\n \nAlready\n \nProcessed:**\n \n \n-\n \nAt\n \nstep\n \n4,\n \nif\n \nthe\n \nselected\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \n`Pending_Manager`\n \nstatus\n \n(e.g.,\n \nprocessed\n \nby\n \nanother\n \nmanager\n \nor\n \nsystem\n \nupdate):\n \n \n-\n \nSystem\n \ndisplays\n \na\n \nmessage\n \n\"This\n \nrequest\n \nhas\n \nalready\n \nbeen\n \nprocessed\n \nor\n \nis\n \nno\n \nlonger\n \navailable\n \nfor\n \naction.\"\n \n \n-\n \nManager\n \nreturns\n \nto\n \nthe\n \nupdated\n \nlist\n \nof\n \nrequests.\n \n<br>\n \n**AF2:\n \nProvide\n \nComments\n \nOnly\n \n(No\n \nStatus\n \nChange):**\n \n \n-\n \nSome\n \nsystems\n \nmight\n \nallow\n \na\n \nmanager\n \nto\n \nadd\n \ncomments\n \nwithout\n \nimmediately\n \napproving/rejecting,\n \nkeeping\n \nit\n \nin\n \n`Pending_Manager`.\n \nIf\n \nthis\n \nis\n \na\n \nfeature:\n \n \n-\n \nManager\n \nadds\n \ncomments\n \nand\n \nclicks\n \n'Save\n \nComments'.\n\n-\n \nSystem\n \nsaves\n \ncomments\n \nand\n \ntimestamp.\n \nRequest\n \nremains\n \n`Pending_Manager`.\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \nDaily\n \nto\n \nWeekly,\n \ndepending\n \non\n \nvolume\n \nof\n \nrequests.\n \nBusiness\n \nRules:\n \nBR-SR002-1,\n \nBR-SR002-2,\n \nBR-SR002-3,\n \nBR-SR002-4,\n \nBR-SR002-5\n \nRelated\n \nUse\n \nCases:\n \n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n \n(Next\n \nstep\n \nin\n \nworkflow),\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nManage\n \nTraining\n \nRequests\n \nList\n \n(Manager),\n \nTraining\n \nRequest\n \nDetail\n \nView\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nManager\n \ncan\n \nonly\n \nact\n \non\n \nrequests\n \ncurrently\n \nin\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \nThe\n \nsystem\n \ncorrectly\n \nroutes\n \nrequests\n \nto\n \nthe\n \nappropriate\n \nManager\n \nbased\n \non\n \norganizational\n \nstructure\n \nor\n \nassignment\n \nrules.\n \n \n-\n \nNotification\n \nmechanisms\n \nare\n \nin\n \nplace.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR001-\n1\n \nActionable\n \nStatus\n \nanagers\n \ncan\n \nonly\n \napprove\n \nor\n \nreject\n \ntraining\n \nrequests\n \nthat\n \nare\n \nin\n \n`Pending_Manager`\n \nstatus\n \nand\n \nassigned/visible\n \nto\n \nthem.\n \nBR-MR001-\n2\n \nComment\n \non\n \naction\n \nComments\n \nmay\n \nbe\n \noptional\n \nfor\n \napproval\n \nbut\n \nshould\n \nbe\n \nstrongly\n \nencouraged\n \nor\n \nmandatory\n \nfor\n \nrejection\n \nto\n \nprovide\n \nfeedback\n \nto\n \nthe\n \nStaff.\n \nBR-MR001-\n3\n \nStatus\n \nTransition\n \non\n \nApproval\n \nUpon\n \nManager\n \napproval,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Pending_Director`.\n \nBR-MR001-\n4\n \nStatus\n \nTransition\n \non\n \nRejection\n \nUpon\n \nManager\n \nrejection,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Rejected_Manager`.\n \nBR-MR001-\n5\n \n \nAudit\n \nTrail\n               \nAll\n \nactions\n \ntaken\n \nby\n \nthe\n \nManager\n \n(approval,\n \nrejection,\n \ncomments),\n \nalong\n \nwith\n \n`user_id`\n \nand\n \n`timestamp`,\n \nmust\n \nbe\n \nlogged\n \nin\n \nthe\n \nrequest's\n \nhistory\n \nfor\n \naudit\n \npurposes.\n \nBR-MR001-\n6\n  \n \nNotification\n \n(Workflow)\n       \n \nUpon\n \napproval,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \nrelevant\n \nDirector(s).\n \nUpon\n \nrejection,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nBR-MR001-\n7\n  \n \nView\n \nPermissions\n    \nManagers\n \nshould\n \nonly\n \nsee\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nteam/department\n \nor\n \nthose\n \nexplicitly\n \nassigned\n \nto\n \nthem\n \nfor\n \napproval,\n \nunless\n \nthey\n \nhave\n \nbroader\n \nadministrative\n \nviewing\n \nrights.\n\n1.2.2.\n \nUC-MR002:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR002\n \nUse\n \nCase\n \nName:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \naccesses\n \nand\n \nviews\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department).\n \nThe\n \ndisplayed\n \nhistory\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort\n \nthe\n \ndata\n \nfor\n \neasier\n \nreview .\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nview\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department,\n \nor\n \nall\n \nrequests\n \nif\n \nthey\n \nhave\n \nsufficient\n \npermissions).\n \nThis\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments.\n \nPreconditions:\n \n-\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \naccess\n \ntraining\n \nrequest\n \nhistorical\n \nrecords.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nManager\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nor\n \ndetailed\n \nview\n \nof\n \nhistorical\n \ntraining\n \nrequests,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort.\n \n \n-\n \n**Failure\n \n(No\n \nHistory):**\n \nIf\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests\n \nare\n \nfound,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \nin\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n \n<br>\n \n3.\n \nManager\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nTraining\n \nRequest\n \nHistory'.\n \n \n4.\n \nSystem\n \nretrieves\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \nrelevant\n \nto\n \nthe\n \nManager's\n \nscope\n \n(e.g.,\n \nrequests\n \nsubmitted\n \nby\n \ntheir\n \nteam,\n \nrequests\n \nthey\n \nmanaged).\n \nThis\n \nincludes\n \nrequest\n \ndetails,\n \nstatus\n \nchange\n \nhistory,\n \napprover/rejecter\n \ndetails,\n \ntimestamps,\n \nand\n \ncomments.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \ntraining\n \nrequest\n \nhistory,\n \ntypically\n \nin\n \na\n \nlist\n \nformat,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \nhistorical\n \nentry\n \nor\n \nrequest.\n \n \n6.\n \nManager\n \ncan\n \napply\n \nfilters\n \n(e.g.,\n \nby\n \nStaff\n \nmember,\n \ndate\n \nrànge,\n \nstatus,\n \ntraining\n \ncategory)\n \nor\n \nsort\n \nthe\n \nhistory.\n\n7.\n \nManager\n \ncan\n \nselect\n \na\n \nspecific\n \nhistorical\n \nrequest\n \nto\n \nview\n \nits\n \ncomplete\n \naudit\n \ntrail\n \nand\n \ndetails.\n \n \n8.\n \nManager\n \ncan\n \nclose\n \nthe\n \nhistory\n \nview\n \nand\n \nreturn\n \nto\n \nthe\n \ndashboard\n \nor\n \nprevious\n \nscreen.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nHistorical\n \nData\n \nFound:**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests:\n \n \n4a.i.\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \navailable\n \nfor\n \nthe\n \nselected\n \ncriteria.\"\n \n \n**AF2:\n \nFiltering/Sorting\n \nApplied:**\n \n \n6a.\n \nManager\n \napplies\n \nfilters\n \nor\n \nsorting\n \ncriteria.\n \n \n6b.\n \nSystem\n \nre-queries\n \nand\n \ndisplays\n \nthe\n \nhistorical\n \ndata\n \naccording\n \nto\n \nthe\n \napplied\n \ncriteria.\n \nExceptions:\n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nhistory,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nManager’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nPeriodically,\n \nfor\n \nreview,\n \naudit,\n \nor\n \ntracking\n \npurposes.\n \nBusiness\n \nRules:\n \nBR-MR002-1,\n \nBR-MR002-2,\n \nBR-MR002-3,\n \nBR-MR002-4\n \nRelated\n \nUse\n \nCases:\n \n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nTraining\n \nRequest\n \nHistory\n \nScreen\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nscope\n \nof\n \nhistorical\n \ndata\n \nvisible\n \nto\n \na\n \nManager\n \nis\n \ndefined\n \nby\n \ntheir\n \nrole\n \nand\n \npermissions.\n \n \n-\n \nHistorical\n \ndata\n \nis\n \naccurately\n \nlogged\n \nand\n \nmaintained\n \nby\n \nthe\n \nsystem.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR002-\n1\n  \n \nData\n \nIntegrity\n \n&\n \nRead-Only\n \nHistorical\n \ntraining\n \nrequest\n \ndata\n \nmust\n \nbe\n \npresented\n \nas\n \nread-only.\n \nNo\n \nmodifications\n \nto\n \nhistorical\n \nrecords\n \nare\n \npermitted\n \nthrough\n \nthis\n \nview.\n \nBR-MR002-\n2\n \n \nScope\n \nof\n \nView\n         \nManagers\n \nshould\n \nonly\n \nbe\n \nable\n \nto\n \nview\n \nthe\n \nhistory\n \nof\n \ntraining\n \nrequests\n \nthat\n \nfall\n \nwithin\n \ntheir\n \ndefined\n \nscope\n \n(e.g.,\n \ntheir\n \ndirect\n\nreports,\n \ntheir\n \ndepartment,"}`
