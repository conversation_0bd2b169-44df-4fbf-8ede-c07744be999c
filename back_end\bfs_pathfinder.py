import json
import logging
from collections import deque
from typing import List, Dict, Optional


class BFSPathfinder:
    """
    Utility class for finding the shortest path between screens using BFS algorithm.
    """
    
    @staticmethod
    def find_shortest_path(
        graph_json_path: str,
        start_screen: str,
        target_screen: str,
        login_action_filter: str = None,
        available_roles: List[str] = None
    ) -> Optional[List[Dict[str, str]]]:
        """
        Find the shortest path between two screens using BFS.
        
        Args:
            graph_json_path: Path to the screen graph JSON file
            start_screen: Name or ID of the starting screen
            target_screen: Name or ID of the target screen
            login_action_filter: Optional filter for specific login actions
            available_roles: Optional list of available roles to consider for login edges
            
        Returns:
            List of dicts with path details or None if no path found
        """
        try:
            with open(graph_json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract graph from data
            if "graph" in data:
                graph = data["graph"]
            else:
                logging.error(f"No 'graph' key found in {graph_json_path}")
                return None
            
            # Create normalized lookup maps (case-insensitive)
            node_map = {}  # Maps normalized names to actual node IDs
            node_data = {}  # Stores the actual node data
            
            for node in graph.get("nodes", []):
                if "id" in node and "name" in node:
                    node_id = node["id"]
                    node_name = node["name"]
                    
                    # Store the node with multiple keys for flexible lookup
                    node_map[node_id.lower()] = node_id
                    node_map[node_name.lower()] = node_id
                    node_map[node_name.replace(" ", "").lower()] = node_id
                    
                    node_data[node_id] = node
            
            # Check if start and target screens exist in our mapping
            start_screen_norm = start_screen.replace(" ", "").lower()
            target_screen_norm = target_screen.replace(" ", "").lower()
            
            # Debug logging
            print(f"Looking for start screen: '{start_screen}' (normalized: '{start_screen_norm}')")
            print(f"Looking for target screen: '{target_screen}' (normalized: '{target_screen_norm}')")
            
            if start_screen_norm not in node_map:
                # Try alternative normalization approaches
                alt_start_screen = start_screen.lower()
                if alt_start_screen in node_map:
                    start_screen_norm = alt_start_screen
                    print(f"Found start screen using alternative normalization: '{alt_start_screen}'")
                else:
                    print(f"Available nodes: {list(node_map.keys())}")
                    logging.error(f"Start screen '{start_screen}' not found in graph")
                    return None
                
            if target_screen_norm not in node_map:
                # Try alternative normalization approaches
                alt_target_screen = target_screen.lower()
                if alt_target_screen in node_map:
                    target_screen_norm = alt_target_screen
                    print(f"Found target screen using alternative normalization: '{alt_target_screen}'")
                else:
                    print(f"Available nodes: {list(node_map.keys())}")
                    logging.error(f"Target screen '{target_screen}' not found in graph")
                    return None
                
            # Get the actual node IDs
            start_node_id = node_map[start_screen_norm]
            target_node_id = node_map[target_screen_norm]
            
            print(f"Resolved start node ID: {start_node_id}")
            print(f"Resolved target node ID: {target_node_id}")
            
            # Build adjacency list from edges
            adj_list = {}
            for node_id in node_data:
                adj_list[node_id] = []
                
            # Process edges based on available roles
            for edge in graph.get("edges", []):
                if "source" in edge and "target" in edge:
                    source = edge["source"]
                    target = edge["target"]
                    action = edge.get("action", "Navigate")
                    
                    # Check if this is a login edge
                    is_login_edge = "Login Successfully" in action
                    
                    # If this is a login edge and we have available_roles, check if the role is available
                    if is_login_edge and available_roles:
                        # Extract role from action (format: "Login Successfully (as ROLE)")
                        role_in_action = None
                        if "(" in action and ")" in action:
                            role_part = action.split("(")[1].split(")")[0]
                            if role_part.startswith("as "):
                                role_in_action = role_part[3:]  # Remove "as " prefix
                        
                        # Skip this edge if the role is not in available_roles (case-insensitive comparison)
                        if role_in_action:
                            role_match = False
                            for available_role in available_roles:
                                if role_in_action.lower() == available_role.lower():
                                    role_match = True
                                    break
                            
                            if not role_match:
                                logging.info(f"Skipping login edge for role '{role_in_action}' as it's not in available roles: {available_roles}")
                                continue
                    
                    # If login_action_filter is specified, check if this login action matches
                    if login_action_filter and is_login_edge:
                        if login_action_filter.lower() not in action.lower():
                            continue
                    
                    # Add the edge to adjacency list
                    if source in adj_list:
                        adj_list[source].append({
                            "target": target,
                            "action": action,
                            "element": edge.get("element", "")
                        })
            
            # BFS algorithm
            visited = set()
            queue = deque([(start_node_id, [])])  # (node, path_so_far)
            
            while queue:
                current, path = queue.popleft()
                
                # Skip if already visited
                if current in visited:
                    continue
                    
                visited.add(current)
                
                # If we reached the target
                if current == target_node_id:
                    # Construct the result path
                    result_path = []
                    prev_node = start_node_id
                    
                    for step_num, node in enumerate(path + [current]):
                        if step_num > 0:  # Skip the first node as it's the start
                            # Find the edge between prev_node and node
                            edge_info = next((e for e in adj_list[prev_node] if e["target"] == node), 
                                            {"action": "Navigate", "element": ""})
                            
                            result_path.append({
                                "step_number": step_num,
                                "source_screen": node_data[prev_node]["name"],
                                "target_screen": node_data[node]["name"],
                                "action": edge_info["action"],
                                "element": edge_info["element"]
                            })
                            
                        prev_node = node
                    
                    # If the path is empty (start == target), create a self-loop step
                    if not result_path:
                        result_path.append({
                            "step_number": 1,
                            "source_screen": node_data[start_node_id]["name"],
                            "target_screen": node_data[target_node_id]["name"],
                            "action": "Start",
                            "element": "Initial screen"
                        })
                    
                    return result_path
                
                # Add neighbors to the queue
                for neighbor in adj_list[current]:
                    next_node = neighbor["target"]
                    if next_node not in visited:
                        queue.append((next_node, path + [current]))
            
            # If we get here, no path was found
            logging.warning(f"No path found from '{start_screen}' to '{target_screen}'")
            return None
            
        except Exception as e:
            logging.error(f"Error in find_shortest_path: {str(e)}")
            return None


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) != 4:
        print("Usage: python bfs_pathfinder.py <graph_json_path> <start_screen> <target_screen>")
        sys.exit(1)
    
    graph_path = sys.argv[1]
    start = sys.argv[2]
    target = sys.argv[3]
    
    path = BFSPathfinder.find_shortest_path(graph_path, start, target)
    
    if path:
        print(f"Shortest path from '{start}' to '{target}':")
        for step in path:
            print(f"Step {step['step_number']}: From '{step['source_screen']}' to '{step['target_screen']}' - {step['action']} using {step['element']}")
    else:
        print(f"No path found from '{start}' to '{target}'") 