1. Seller navigates to shop settings or profile section in User Profile Page
2. <PERSON>ller updates desired information and uploads images in Edit Profile Page
3. <PERSON><PERSON> saves changes in Edit Profile Page
4. <PERSON><PERSON> navigates to product management page in Seller Products Page
5. <PERSON>ller selects action (Add Product) in Seller Products Page
6. <PERSON>ller enters product information and uploads images in Edit Product Page
7. <PERSON><PERSON> sets pricing and inventory details in Edit Product Page
8. <PERSON><PERSON> saves product record in Edit Product Page
9. Anonymous User clicks "Sign Up" button in Homepage
10. Anonymous User enters email, password, confirms password, first name, last name, and optionally shop name, then clicks "Register" button in Customer Registration Page / Seller Registration Page
11. Registered User clicks "Login" button in Homepage
12. Registered User enters email and password, then clicks "Login" button in Login Page
13. Customer views product and selects options in Product Detail Page
14. Customer specifies quantity and clicks "Add to Cart" button in Product Detail Page
15. Customer clicks on cart icon or navigates to cart page in Shopping Cart Page
16. Customer updates quantities or removes items in Shopping Cart Page
17. Customer clicks "Checkout" button in Shopping Cart Page
18. Customer enters/confirms shipping information in Checkout Page
19. Customer selects payment method and reviews order in Checkout Page
20. Customer confirms order placement in Checkout Page
21. <PERSON><PERSON> navigates to order management page in Seller Orders Page
22. <PERSON><PERSON> selects order to process in Seller Orders Page
23. Seller updates order status and adds tracking information in Seller Order Details Page
24. Customer navigates to 'My Account' section in User Profile Page
25. Customer clicks on 'Order History' link in User Profile Page
26. Customer views order details by clicking on specific orders in User Orders Page
27. Customer navigates to 'Create Booking Page'
28. Customer selects service in Create Booking Page
29. Customer enters address information in Create Booking Page
30. Customer selects date and time in Create Booking Page
31. Customer enters special instructions in Create Booking Page
32. Customer clicks "Submit Booking" button in Create Booking Page
33. Customer navigates to 'My Account' section in User Profile Page
34. Customer clicks on 'Booking History' link in User Profile Page
35. Customer views booking details by clicking on specific bookings in Booking Details Page
36. Customer selects booking to cancel in Customer Bookings Page
37. Customer confirms cancellation in Booking Details Page
38. Seller navigates to Seller Bookings Page
39. Seller selects booking to review in Seller Bookings Page
40. Seller updates booking status in Seller Booking Details Page
41. Admin navigates to Admin Pending Products Page
42. Admin searches or filters products in Admin Products Page
43. Admin selects product and performs approval action in Admin Pending Products Page
Business Process Context: The interaction between a Customer and a Seller on the SASUCare platform represents a dynamic and continuous lifecycle, beginning long before a single purchase is made. Initially, a Seller establishes their digital presence by setting up a detailed shop pr ofile (UC -301) and meticulously populating their catalog with products, including images, descriptions, and accurate inventory levels (UC -302). A Customer then begins their journey by registering an account (UC -101) or logging in (UC -102), discovering the Seller's offerings, and adding a desired item to their shopping cart (UC -203). This culminates in the core transaction where the Customer completes the checkout process, providing payment and shipping details to finalize their order (UC -207). Immediately, the Seller is notified of the new sale and navigates their dashboard to manage the order (UC -305), updating its status from 'Processing' to 'Shipped' after dispatching the item and providing a tracking number, which the Customer can monitor through their o wn order history page (UC -208). Beyond this standard product flow, a different Customer might engage with the Seller's service offerings, viewing their availability and booking a specific appointment (UC -211). Should that Customer's plans change, they can then navigate back to their bookings to request a cancellation, which the Seller reviews and processes according to their stated policy (UC -212). Following these successful customer interactions, the Seller’s work continues as they might update their business information, refine product listings, or add entirely new items, which may require a brief period of pending approval before becoming visible to the public, e nsuring platform quality standards are met (reflecting UC -405). This entire ecosystem demonstrates a continuous loop of preparation, transaction, fulfillment, and ongoing management from both the Customer and Seller perspectives.
