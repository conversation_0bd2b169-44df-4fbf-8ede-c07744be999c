"""
Pipeline Modules API Routes
Endpoints for managing and querying pipeline modules
"""

import logging
from fastapi import APIRouter, HTTPException
from datetime import datetime
from typing import Dict, Any, List

from app.core.pipeline_registry import pipeline_registry
from app.models.schemas import PipelineResponse

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/list")
async def list_modules() -> Dict[str, Any]:
    """List all registered pipeline modules"""
    try:
        modules = pipeline_registry.list_modules()
        execution_order = pipeline_registry.get_execution_order()
        
        return {
            "success": True,
            "message": f"Found {len(modules)} registered modules",
            "data": {
                "modules": modules,
                "execution_order": execution_order,
                "total_count": len(modules)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error listing modules: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/module/{module_id}")
async def get_module_info(module_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific module"""
    try:
        config = pipeline_registry.get_module_config(module_id)
        if not config:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": f"Module {module_id} not found",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        module = pipeline_registry.get_module(module_id)
        status = await module.get_status() if module else {"status": "not_loaded"}
        
        return {
            "success": True,
            "message": f"Module {module_id} information",
            "data": {
                "id": config.id,
                "name": config.name,
                "description": config.description,
                "type": config.module_type,
                "version": config.version,
                "dependencies": config.dependencies,
                "input_types": config.input_types,
                "output_types": config.output_types,
                "metadata": config.metadata,
                "status": status
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting module info for {module_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/validate")
async def validate_pipeline() -> Dict[str, Any]:
    """Validate the current pipeline configuration"""
    try:
        validation_result = await pipeline_registry.validate_pipeline()
        
        return {
            "success": validation_result["valid"],
            "message": "Pipeline validation completed",
            "data": validation_result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error validating pipeline: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/execution-order")
async def get_execution_order() -> Dict[str, Any]:
    """Get the current module execution order"""
    try:
        execution_order = pipeline_registry.get_execution_order()
        modules_info = []
        
        for i, module_id in enumerate(execution_order, 1):
            config = pipeline_registry.get_module_config(module_id)
            if config:
                modules_info.append({
                    "order": i,
                    "id": config.id,
                    "name": config.name,
                    "type": config.module_type,
                    "dependencies": config.dependencies
                })
        
        return {
            "success": True,
            "message": f"Execution order for {len(modules_info)} modules",
            "data": {
                "execution_order": execution_order,
                "modules": modules_info,
                "total_count": len(modules_info)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting execution order: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@router.post("/reload")
async def reload_modules() -> Dict[str, Any]:
    """Reload all pipeline modules"""
    try:
        # This would trigger a reload of all modules
        # For now, we'll just return the current state
        modules = pipeline_registry.list_modules()
        
        return {
            "success": True,
            "message": f"Modules reloaded successfully",
            "data": {
                "modules_count": len(modules),
                "execution_order": pipeline_registry.get_execution_order()
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error reloading modules: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )
