"""
Main API Router
Combines all API routes
"""

from fastapi import APIRouter
from app.api.routes import health, pipeline, websocket, upload, modules

# Create main API router
api_router = APIRouter(prefix="/api/v1", tags=["API v1"])

# Include sub-routers
api_router.include_router(health.router, prefix="/health", tags=["Health"])
api_router.include_router(pipeline.router, prefix="/pipeline", tags=["Pipeline"])
api_router.include_router(websocket.router, prefix="/websocket", tags=["WebSocket"])
api_router.include_router(upload.router, prefix="/upload", tags=["Upload"])
api_router.include_router(modules.router, prefix="/modules", tags=["Modules"])
