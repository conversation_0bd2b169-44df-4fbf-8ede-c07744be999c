1. <PERSON> navigates to StaffDashboardPage
2. Staff selects 'Create Training Request' option in StaffDashboardPage
3. <PERSON> fills in information in CreateNewTrainingRequestPage
4. <PERSON> clicks 'Submit Request' button in CreateNewTrainingRequestPage
5. <PERSON> clicks 'Cancel' button in CreateNewTrainingRequestPage
Business Process Context: `{"heading_number": "1.1", "title": "1.1 Staff", "Content": "{\n \n\"source\":\n \n\"requests\",\n \n\"target\":\n \n\"request_history\",\n \n\"type\":\n \n\"one-to-many\"\n \n}\n \n]\n \n}\n \nII.\n \nRequirement\n \nSpecifications\n \n \n1.\n \nUse\n \nCase\n \nDescription\n \n1.1\n \nStaff\n \na.Diagram\n\n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \nview\n \ntheir\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"SendRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \ncreate\n \nnew\n \nrequests\n \n(e.g.,\n \nTraining\n \nor\n \nRole\n \nChange).\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \nif\n \ndirectly\n \naccessible\n \nand\n \ndistinct\n \nfor\n \nStaff\n \nflow\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nlist'\n \n(e.g.,\n \nUC006,\n \nUC009)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"SendRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Send\n \nnew\n \nrequest'\n \n(e.g.,\n \nUC005,\n \nUC-RC001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestListPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"SendRequestPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nor\n \nafter\n \nsuccessful\n \nsubmission\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \nlike\n \nto\n \nProfilePage\n \nif\n \napplicable\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nStaff\n \nuser.\"\n \n}\n \n \nb.\n \nDescriptions\n \n \n1.1.1\n \nUse\n \nCase:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \n \n \nTable\n \n12\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-TR001\n \nUse\n \nCase\n \nName:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nStaff\n \nSecondary\n \nActors:\n \nSýtem\n \nStaff,\n \nSystem\n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Create\n \nTraining\n \nRequest'\n \nsection\n \nfrom\n \ntheir\n \ndashboard.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \ncreate\n \nand\n \nsubmit\n \na\n \nnew\n \ntraining\n \nrequest.\n \nThe\n \nuser\n \nfills\n \nin\n \ndetails\n \nsuch\n \nas\n \nTraining\n \nCategory,\n \nReason\n \nfor\n \nTraining,\n \nand\n \nany\n \nsupporting\n \ndocuments.\n \nThe\n \nsystem\n \nvalidates\n \nthe\n \nrequest\n \nand,\n \nupon\n \nsuccessful\n \nsubmission,\n \nstores\n \nit\n \nwith\n \nan\n \ninitial\n \nstatus\n \nof\n \n`Pending_Manager`.\n \nThe\n \nuser\n \nis\n \nnotified\n \nof\n \nthe\n \nsuccessful\n \ncreation.\n \nPreconditions:\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \nThe\n \nStaff\n \nuser\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \ncreate\n \ntraining\n \nrequests.\n \nThe\n \nsystem\n \nhas\n \ndefined\n \ntraining\n \ncategories\n \navailable\n \nfor\n \nselection.\n \nPostconditions:\n \n-\n \n**Success**:\n \nA\n \nnew\n \ntraining\n \nrequest\n \nrecord\n \nis\n \ncreated\n \nin\n \nthe\n \n`requests`\n \ntable\n \nwith\n \na\n \nunique\n \n`request_id`,\n \n`user_id`\n \nof\n \nthe\n \ncreator,\n \n`request_type`\n \nset\n \nto\n \n'Training',\n \nand\n\n`status`\n \nset\n \nto\n \n`Pending_Manager`.\n \nThe\n \nuser\n \nis\n \nredirected\n \nto\n \na\n \nconfirmation\n \npage\n \nor\n \ntheir\n \nlist\n \nof\n \nrequests.\n \n-\n \n**Failure**:\n \nIf\n \nvalidation\n \nfails\n \nor\n \na\n \nsystem\n \nerror\n \noccurs,\n \nthe\n \nrequest\n \nis\n \nnot\n \nsaved,\n \nand\n \nthe\n \nuser\n \nis\n \ninformed\n \nof\n \nthe\n \nissue.\n \nExpected\n \nResults\n \nAfter\n \nthe\n \nManager\n \nclicks\n \n'Approve'\n \n(or\n \n'Reject')\n \nfor\n \na\n \nspecific\n \nrequest\n \non\n \nthe\n \n'Manage\n \nRequest'\n \nscreen\n \nand\n \nconfirms\n \nthe\n \naction\n \nvia\n \nthe\n \nalert\n \npopup,\n \nthe\n \nsystem\n \nsuccessfully updates\n \nthat\n \nrequest's\n \nstatus\n \nin\n \nthe\n \ndatabase\n \nto\n \n'Approved'\n \n(or\n \n'Rejected').\n \nThe\n \nstatus\n \nof\n \nthat\n \nrequest\n \nis\n \nchanged\n \nto\n \n`Approved`(or\n \nRejected)\n \nin\n \nHistory\n \nor\n \nStaff\n \nview.\n \nNormal\n \nFlow:\n \n1.\n  \nStaff\n \nuser\n \nlogs\n \nin\n \nthe\n \nsystem\n \n(references\n \nUC_1:\n \nLogin\n \nSystem).\n \n2.\n  \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \ndashboard.\n \n3.\n  \nStaff\n \nuser\n \nselects\n \nthe\n \noption\n \nto\n \n'Create\n \nTraining\n \nRequest'.\n \n4.\n  \nSystem\n \ndisplays\n \nthe\n \nTraining\n \nRequest\n \nform,\n \nincluding\n \nfields\n \nfor:\n \n    \n*\n   \nTraining\n \nCategory\n \n(e.g.,\n \ndropdown:\n \nTechnical,\n \nSoft\n \nSkills,\n \nCompliance)\n \n    \n*\n   \nReason\n \nfor\n \nTraining\n \n(e.g.,\n \ntext\n \narea)\n \n    \n*\n   \nPreferred\n \nDates\n \n(optional,\n \ne.g.,\n \ndate\n \npicker)\n \n    \n*\n   \nSupporting\n \nDocuments\n \n(optional,\n \ne.g.,\n \nfile\n \nupload)\n \n5.\n  \nStaff\n \nuser\n \nfills\n \nin\n \nthe\n \nrequired\n \ninformation.\n \n6.\n  \nStaff\n \nuser\n \nclicks\n \nthe\n \n'Submit\n \nRequest'\n \nbutton.\n \n7.\n  \nSystem\n \nvalidates\n \nthe\n \nsubmitted\n \ndata\n \nbased\n \non\n \ndefined\n \nbusiness\n \nrules\n \n(e.g.,\n \nBR-TR001-2,\n \nBR-TR001-3,\n \nBR-TR001-5,\n \nBR-TR001-6).\n \n8.\n  \nIf\n \nvalidation\n \nis\n \nsuccessful:\n \n    \na.\n  \nSystem\n \ngenerates\n \na\n \nunique\n \n`request_id`\n \n(BR-TR001-1).\n \n    \nb.\n  \nSystem\n \nsaves\n \nthe\n \ntraining\n \nrequest\n \ndetails\n \nto\n \nthe\n \n`requests`\n \ntable,\n \nincluding\n \n`user_id`,\n \n`request_type`\n \n('Training'),\n \n`category`,\n \n`reason`,\n \n`submission_date`\n \n(current\n \ndate/time),\n \nand\n \nsets\n \nthe\n \ninitial\n \n`status`\n \nto\n \n`Pending_Manager`\n \n(BR-TR001-4).\n \n    \nc.\n  \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage\n \nto\n \nthe\n \nStaff\n \nuser\n \n(e.g.,\n \n\"Training\n \nrequest\n \nsubmitted\n \nsuccessfully.\n \nYour\n \nRequest\n \nID\n \nis\n \n[request_id].\").\n \n    \nd.\n  \nSystem\n \nmay\n \nredirect\n \nthe\n \nuser\n \nto\n \ntheir\n \n'My\n \nTraining\n \nRequests'\n \nlist\n \n(references\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests).\n \n9.\n  \nIf\n \nthe\n \nstaff\n \nwant\n \nto\n \ncancel\n \nthe\n \nprocess,\n \nthey\n \nclick\n \n“Cancel”.\n \nAlternative\n \nFlows:\n \n-\n   \n**AF1:\n \nInvalid\n \nData\n \nSubmission**\n \n    \n-\n   \nAt\n \nstep\n \n7,\n \nif\n \nsystem\n \nvalidation\n \nfails:\n\n-\n   \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \nindicating\n \nthe\n \nspecific\n \nvalidation\n \nerrors\n \n(e.g.,\n \n\"Reason\n \nfor\n \nTraining\n \nis\n \nrequired.\").\n \n        \n-\n   \nThe\n \nTraining\n \nRequest\n \nform\n \nremains\n \npopulated\n \nwith\n \nthe\n \nuser's\n \nentered\n \ndata.\n \n        \n-\n   \nUser\n \ncorrects\n \nthe\n \ndata\n \nand\n \nre-submits\n \n(returns\n \nto\n \nstep\n \n5\n \nof\n \nNormal\n \nFlow).\n \n-\n   \n**AF2:\n \nCancel\n \nRequest\n \nCreation**\n \n    \n-\n   \nAt\n \nany\n \npoint\n \nbefore\n \nstep\n \n8,\n \nif\n \nthe\n \nStaff\n \nuser\n \nclicks\n \na\n \n'Cancel'\n \nbutton:\n \n        \n-\n   \nSystem\n \ndiscards\n \nany\n \nentered\n \ndata.\n \n        \n-\n   \nSystem\n \nredirects\n \nthe\n \nuser\n \nback\n \nto\n \ntheir\n \ndashboard\n \nor\n \nthe\n \nprevious\n \npage.\n \nExceptions:\n \n-\n   \n**E1:\n \nSystem\n \nError\n \nDuring\n \nSave**\n \n    \n-\n   \nAt\n \nstep\n \n8b,\n \nif\n \na\n \ndatabase\n \nerror\n \nor\n \nother\n \nsystem\n \nerror\n \noccurs\n \nwhile\n \nattempting\n \nto\n \nsave\n \nthe\n \nrequest:\n \n        \n-\n   \nSystem\n \nlogs\n \nthe\n \nerror.\n \n        \n-\n   \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser\n \n(e.g.,\n \n\"An\n \nunexpected\n \nerror\n \noccurred.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n        \n-\n   \nThe\n \nrequest\n \nis\n \nnot\n \nsaved.\n \nPriority:\n \nHigh\n \n \nFrequency\n \nof\n \nUse:\n \nModerate\n \nBusiness\n \nRules:\n \nBR-TR001-1,\n \nBR-TR001-2,\n \nBR-TR001-3,\n \nBR-TR001-4,\n \nBR-TR001-5,\n \nBR-TR001-6\n \nUse\n \nCase\n \nrelated:\n \n \nRelated\n \nUse\n \nCases:\n \n“View\n \nRequest\n \nHistory”\n \n(UC-MR002)\n \n,\n \n“View\n \nRequest\n \nList”\n \n(UC-SR002),\n \n“Send\n \nRequest”(\n \nUC-SR001)\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nCreateTrainingRequestPage\n \nAssumptions:\n \n \nThe\n \napproval\n \nworkflow\n \n(Manager,\n \nDirector,\n \nCEO)\n \nis\n \ndefined\n \nelsewhere.\n \n \n \nTable\n \n13.\n \nBusiness\n \nrule\n \nof\n \nuse\n \ncase:\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR1\n \nUnique\n \nRequest\n \nID\n \nEach\n \ntraining\n \nrequest\n \nmust\n \nhave\n \na\n \nunique,\n \nsystem-generated\n \n`request_id`.\n \nThis\n \nID\n \nshould\n \nnot\n \nbe\n \neditable\n \nby\n \nthe\n \nuser.\n                                                     \n \nBR2\n \n \nMandatory\n \nFields\n \n'Training\n \nCategory'\n \nand\n \n'Reason\n \nfor\n \nTraining'\n \nare\n \nmandatory\n \nfields.\n \nForm\n \nsubmission\n \nshould\n \nbe\n \nblocked\n \nif\n \nthese\n \nfields\n \nare\n \nempty."}`
