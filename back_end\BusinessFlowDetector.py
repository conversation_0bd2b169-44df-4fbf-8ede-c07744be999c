import json
import os
import re
import google.generativeai as genai
from prompt_storing import gen_business_flow_prompt_from_json  # Assuming this exists and has gen_business_flow_prompt_from_json
from rotate_api_key import APIKeyRotator  # Your APIKeyRotator
from count_gemini_token import GeminiCostCalculator
from google.api_core.exceptions import ResourceExhausted
import traceback
from typing import Dict, Any

class GenBusinessFlow:
    def __init__(self, input_json_file, business_flow_dir, config_file="back_end/gemini_config_flash.json", count_token=False):
        """
        Initialize GenBusinessFlow with token counting capability.
        
        Args:
            input_json_file: Path to input JSON file
            business_flow_dir: Directory to save business flows
            config_file: Path to Gemini config file
            count_token: Enable token counting and cost calculation
        """
        self.input_json_file = input_json_file
        self.business_flow_dir = business_flow_dir
        self.base_filename = os.path.splitext(os.path.basename(input_json_file))[0]
        self.count_token = count_token

        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config

        self.config = None
        self.rotator = None
        self.api_key = None
        self.model_name = None
        self.model = None
        self.initialization_ok = False

        try:
            self.config = self.load_config(config_file)
            if self.config:
                # Determine model type for token calculation
                if self.count_token:
                    self._determine_model_type()

                self.rotator = APIKeyRotator(config_path=config_file)
                if not self.rotator.api_keys:
                    print("Error: APIKeyRotator did not load any API keys.")
                    return

                self.api_key = self.rotator.get_api_key()
                gemini_model_config_section = self.config.get("config_gemini_model")
                if not gemini_model_config_section:
                    print("Error: 'config_gemini_model' section is missing in the config file.")
                    return

                self.model_name = gemini_model_config_section.get("model_name")
                if not self.model_name:
                    print("Error: Missing 'model_name' in 'config_gemini_model' section of the config.")
                    return

                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                self.initialization_ok = True
                print(f"Business Flow Generator Initialization successful. Using API key ending with: ...{self.api_key[-4:]}")
                print(f"Using model: {self.model_name} for text generation (business flows).")
            else:
                print("Failed to load configuration. Initialization aborted.")

        except Exception as e:
            print(f"An error occurred during GenBusinessFlow initialization: {e}")
            print(traceback.format_exc())

    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"
            
        if self.count_token:
            print(f"Token counting enabled for model type: {self.model_type}")

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)

    def get_token_summary(self) -> Dict[str, Any]:
        """
        Trả về tổng kết chi phí token.
        
        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}
            
        total_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)
        
        return {
            "token_counting_enabled": True,
            "model_type": self.model_type,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            **total_cost_info
        }

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost."""
        if not self.count_token or not self.cost_calculator:
            return
            
        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "BUSINESS FLOW")
        
        # Log billing information
        from .billing_logger import billing_logger
        billing_logger.log_module_cost("GenBusinessFlow", token_summary)

    def load_config(self, file_path="gemini_config.json"):
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Configuration file {file_path} not found.")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {file_path}: {e}")
        except Exception as e:
            print(f"An unexpected error occurred loading config {file_path}: {e}")
        return None

    def _rotate_and_reconfigure_api(self):
        print(f"Quota exceeded with key ending ...{self.api_key[-4:]}. Rotating API key for Business Flow Generator...")
        try:
            self.rotator.rotate_api_key()
            self.api_key = self.rotator.get_api_key()
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            print(f"Switched to new API key ending: ...{self.api_key[-4:]} for Business Flow Generator")
            return True
        except Exception as e:
            print(f"Error during API key rotation or reconfiguration for Business Flow Generator: {e}")
            print(traceback.format_exc())
            return False

    def _attempt_generate_content(self, prompt: str) -> str:
        if not self.initialization_ok or not self.rotator or not self.model:
            return "Error: Business Flow Generator not properly initialized."

        # Count input tokens if enabled
        input_tokens = 0
        if self.count_token:
            try:
                input_token_count = self.model.count_tokens(prompt)
                input_tokens = input_token_count.total_tokens
                print(f"Business Flow Gen - Input tokens: {input_tokens:,}")
            except Exception as e:
                print(f"Failed to count input tokens: {e}")

        max_attempts = len(self.rotator.api_keys)
        for attempt in range(max_attempts):
            try:
                current_key_for_attempt = self.rotator.get_api_key()
                print(f"Business Flow Gen - Attempt {attempt + 1}/{max_attempts} using key ending ...{current_key_for_attempt[-4:]}")
                generation_config_text = genai.types.GenerationConfig()
                response = self.model.generate_content(
                    prompt,
                    generation_config=generation_config_text
                )

                if response and hasattr(response, "text") and response.text:
                    # Count output tokens if enabled
                    output_tokens = 0
                    if self.count_token:
                        try:
                            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                                output_tokens = response.usage_metadata.candidates_token_count
                            else:
                                # Fallback: estimate tokens based on response text
                                output_token_count = self.model.count_tokens(response.text)
                                output_tokens = output_token_count.total_tokens
                        except Exception as e:
                            print(f"Failed to count output tokens: {e}")
                    
                    # Track tokens if enabled and successful
                    if self.count_token and input_tokens > 0:
                        self.total_input_tokens += input_tokens
                        self.total_output_tokens += output_tokens
                        
                        # Calculate cost for this request
                        cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                        
                        print(f"Business Flow Gen - Token usage - Input: {input_tokens:,}, Output: {output_tokens:,}")
                        if cost_info:
                            print(f"Business Flow Gen - Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                            cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                            print(f"Business Flow Gen - Cumulative cost: ${cumulative_cost:.6f}")
                    
                    return response.text
                elif response and hasattr(response, "prompt_feedback"):
                    print(f"Warning: Received response with feedback: {response.prompt_feedback}")
                    if response.text:
                        return response.text
                    return f"Error: Generation failed due to API feedback - {response.prompt_feedback}, and no text was generated."
                else:
                    print(f"Warning: Received unexpected response format or no text: {response}")
                    return "Error: No valid response text generated."

            except ResourceExhausted as e:
                print(f"ResourceExhausted error on attempt {attempt + 1} for Business Flow Gen.")
                if attempt < max_attempts - 1:
                    if not self._rotate_and_reconfigure_api():
                        return f"Error: Failed to rotate API key for Business Flow Gen - {e}"
                else:
                    print(f"Error: Exhausted all {max_attempts} API keys for Business Flow Gen.")
                    return f"Error: All API keys exhausted for Business Flow Gen - {e}"
            except Exception as e:
                print(f"Unexpected error during API call on attempt {attempt + 1} for Business Flow Gen: {e}")
                print(traceback.format_exc())
                return f"Error: An unexpected exception occurred for Business Flow Gen - {e}"
        return "Error: Business Flow Generation failed after all attempts."

    def generate_flows(self):
        if not self.initialization_ok:
            print("Cannot generate business flows: Initialization failed.")
            return "Error: Initialization failed."

        try:
            # Try multiple encodings to handle files from different environments
            relevant_info_content = None
            encodings_to_try = ['utf-8', 'cp1252', 'latin-1', 'utf-16']

            for encoding in encodings_to_try:
                try:
                    with open(self.input_json_file, 'r', encoding=encoding) as f:
                        relevant_info_content = json.load(f)
                        print(f"✅ Successfully read input file with {encoding} encoding")
                        break
                except (UnicodeDecodeError, json.JSONDecodeError):
                    continue

            if relevant_info_content is None:
                return f"Error: Could not decode input JSON file with any encoding: {self.input_json_file}"

            process_doc_str = json.dumps(relevant_info_content, indent=2, ensure_ascii=False)
        except FileNotFoundError:
            return f"Error: Input JSON file not found at {self.input_json_file}"
        except json.JSONDecodeError:
            return f"Error: Could not decode JSON from {self.input_json_file}"
        except Exception as e:
            print(f"Error reading input JSON file: {e}")
            print(traceback.format_exc())
            return f"Error reading input JSON file: {e}"

        print(f"\n--- Generating Business Flows from: {self.input_json_file} ---")

        try:
            # Check if gen_business_flow_prompt_from_json is available
            if not gen_business_flow_prompt_from_json:
                error_msg = "Error: 'gen_business_flow_prompt_from_json' is empty or None."
                print(error_msg)
                return error_msg
        except NameError:
            error_msg = "Error: 'gen_business_flow_prompt_from_json' not found in prompt_storing module."
            print(error_msg)
            return error_msg

        prompt =f"""
            {gen_business_flow_prompt_from_json}\n   
            ====Begin Input JSON Data====\n
            {process_doc_str}\n
            ====End Input JSON Data======
        """ 

        result_text = self._attempt_generate_content(prompt)

        if isinstance(result_text, str):
            cleaned_result_text = result_text.strip()
            if cleaned_result_text.startswith("```") and cleaned_result_text.endswith("```"):
                lines = cleaned_result_text.splitlines()
                if len(lines) > 1:
                    if lines[0].strip().startswith("```"):
                        lines.pop(0)
                    if lines and lines[-1].strip() == "```":
                        lines.pop(-1)
                    cleaned_result_text = "\n".join(lines).strip()
            result_text = cleaned_result_text

        print("\n--- Business Flow generation finished ---")
        return result_text

    def execute(self):
        if not self.initialization_ok:
            print("Execution skipped for Business Flow Generator: Initialization was not successful.")
            return None

        business_flows_text = self.generate_flows()

        if isinstance(business_flows_text, str) and business_flows_text.startswith("Error:"):
            print(f"Business Flow Generation Error: {business_flows_text}")
            error_file_path = os.path.join(self.business_flow_dir, f'{self.base_filename}_flows_error.txt')
            os.makedirs(self.business_flow_dir, exist_ok=True)
            try:
                with open(error_file_path, 'w', encoding='utf-8') as f:
                    f.write(business_flows_text)
                print(f"Business flow generation error saved to: {error_file_path}")
            except Exception as e:
                print(f"Error saving business flow generation error to {error_file_path}: {e}")
            return None
        elif business_flows_text:
            self.save_business_flows(business_flows_text, self.business_flow_dir)
            
            # Print token summary if enabled
            if self.count_token:
                self.print_token_summary()
            
            return business_flows_text
        else:
            print("No business flows were generated.")
            return None

    def save_business_flows(self, flows_text, directory):
        os.makedirs(directory, exist_ok=True)
        
        # Split flows by finding "Business Process \d+:" patterns
        lines = flows_text.splitlines()
        current_flow_number = None
        current_flow_steps = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check if this line starts a new business process
            match = re.match(r'Business Process (\d+):', line)
            if match:
                # Save the previous flow if it exists
                if current_flow_number is not None and current_flow_steps:
                    self._save_individual_flow(current_flow_number, current_flow_steps, directory)
                
                # Start new flow
                current_flow_number = match.group(1)
                current_flow_steps = []
            else:
                # This line is a step, add it to current flow
                if current_flow_number is not None and line:
                    current_flow_steps.append(line)
        
        # Save the last flow
        if current_flow_number is not None and current_flow_steps:
            self._save_individual_flow(current_flow_number, current_flow_steps, directory)
    
    def _save_individual_flow(self, flow_number, steps, directory):
        """Save individual business flow to a separate file"""
        flow_filename = os.path.join(directory, f'Business Flow {flow_number}.txt')
        
        try:
            with open(flow_filename, 'w', encoding='utf-8') as f:
                for step in steps:
                    f.write(f"{step}\n")
            print(f"Business Flow {flow_number} saved to: {flow_filename}")
        except Exception as e:
            print(f"Error saving Business Flow {flow_number} to {flow_filename}: {e}")
            print(traceback.format_exc())

if __name__ == "__main__":
    input_json_path = "back_end/output/RDS-refined_eee/document_processor/merged_output.json"
    output_flow_directory = "back_end/output/business_flows"
    config_path = "back_end/document/gemini_config_flash.json"

    if not os.path.exists(input_json_path):
        print(f"Error: Input file not found at {input_json_path}")
    else:
        flow_generator = GenBusinessFlow(
            input_json_file=input_json_path,
            business_flow_dir=output_flow_directory,
            config_file=config_path
        )
        generated_flows = flow_generator.execute()

        if generated_flows:
            print("\nSuccessfully generated and split business flows.")
        else:
            print("\nFailed to generate business flows or no flows were produced.")