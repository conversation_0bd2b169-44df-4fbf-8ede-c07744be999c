{"name": "python-interactive-execution-dashboard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/dagre": "^0.7.53", "ag-grid-community": "^31.0.3", "ag-grid-react": "^31.0.3", "axios": "^1.11.0", "clsx": "^2.0.0", "dagre": "^0.8.5", "lucide-react": "^0.294.0", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-resizable-panels": "^0.0.55", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.10.1", "tailwind-merge": "^2.0.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-web-links": "^0.9.0", "zustand": "^4.5.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}