{"login_screen": {"Username Input Field (true_staff: \"valid\"; true_manager: \"valid\"; false: \"invalid\", \"\")": ["staff_user", "manager_user", "user!@#"], "Password Input Field (true_staff: \"valid\"; true_manager: \"valid\"; false: \"invalid\", \"\")": ["123", "123456", "<PERSON><PERSON><PERSON><PERSON>"]}, "send_request_screen": {"Input receiver (true: \"exist\"; false: \"not exist\", \"\")": ["<EMAIL>", "<EMAIL>", ""], "Description Textarea (true: \"under_1000\", \"equal_1000\"; false: \"over_1000\", \"\")": ["This is a short request description.", "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum eget nulla vitae nulla auctor facilisis. Pellentesque ultricies, velit ut fermentum convallis, urna arcu auctor purus, nec fringilla nunc dolor eget justo. Duis vitae magna tincidunt, iaculis quam eu, malesuada libero. Nam auctor lectus eget orci pharetra, eget fermentum mauris vehicula. Ut fermentum erat et lectus venenatis, a tincidunt ligula feugiat. Mauris tristique nunc id nisi venenatis, sit amet faucibus magna convallis. Sed sed magna metus. Integer id ligula eget sapien condimentum malesuada non nec odio. Curabitur rhoncus, elit eu sodales rhoncus, justo libero bibendum felis, at porttitor sapien lectus ac sapien. Vestibulum at vehicula lacus. Donec in ultrices ligula. Nulla at risus orci. Duis consequat lectus sit amet varius feugiat. Donec vel ligula ut libero facilisis sagittis nec a orci.", "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum eget nulla vitae nulla auctor facilisis. Pellentesque ultricies, velit ut fermentum convallis, urna arcu auctor purus, nec fringilla nunc dolor eget justo. Duis vitae magna tincidunt, iaculis quam eu, malesuada libero. Nam auctor lectus eget orci pharetra, eget fermentum mauris vehicula. Ut fermentum erat et lectus venenatis, a tincidunt ligula feugiat. Mauris tristique nunc id nisi venenatis, sit amet faucibus magna convallis. Sed sed magna metus. Integer id ligula eget sapien condimentum malesuada non nec odio. Curabitur rhoncus, elit eu sodales rhoncus, justo libero bibendum felis, at porttitor sapien lectus ac sapien. Vestibulum at vehicula lacus. Donec in ultrices ligula. Nulla at risus orci. Duis consequat lectus sit amet varius feugiat. Donec vel ligula ut libero facilisis sagittis nec a orci. Etiam quis massa ac neque cursus suscipit eget et augue. Nulla facilisi. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Duis dapibus, lorem vel accumsan facilisis, orci ligula tempor ex, a suscipit lorem orci sit amet orci.", ""]}}