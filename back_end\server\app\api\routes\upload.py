"""
Upload Routes
File upload endpoints
"""

import os
import logging
from pathlib import Path
from fastapi import APIRouter, UploadFile, File, HTTPException
from datetime import datetime
from typing import Dict, Any

router = APIRouter()
logger = logging.getLogger(__name__)

# Create uploads directory if it doesn't exist
UPLOADS_DIR = Path("uploads")
UPLOADS_DIR.mkdir(exist_ok=True)

@router.post("/document")
async def upload_document(file: UploadFile = File(...)) -> Dict[str, Any]:
    """Upload a PDF document"""
    try:
        # Validate file type
        if not file.content_type == "application/pdf":
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are allowed"
            )
        
        # Validate file size (max 10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="File size must be less than 10MB"
            )
        
        # Generate unique filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = UPLOADS_DIR / filename
        
        # Save file
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        logger.info(f"File uploaded successfully: {filename}")
        
        return {
            "success": True,
            "message": "File uploaded successfully",
            "data": {
                "filename": filename,
                "original_name": file.filename,
                "file_path": str(file_path),
                "file_size": len(file_content),
                "upload_time": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload file: {str(e)}"
        )

@router.get("/list")
async def list_uploaded_files() -> Dict[str, Any]:
    """List all uploaded files"""
    try:
        files = []
        if UPLOADS_DIR.exists():
            for file_path in UPLOADS_DIR.glob("*.pdf"):
                stat = file_path.stat()
                files.append({
                    "filename": file_path.name,
                    "file_path": str(file_path),
                    "file_size": stat.st_size,
                    "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
        
        return {
            "success": True,
            "message": f"Found {len(files)} uploaded files",
            "data": {
                "files": files,
                "total_count": len(files)
            }
        }
        
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list files: {str(e)}"
        )

@router.delete("/document/{filename}")
async def delete_document(filename: str) -> Dict[str, Any]:
    """Delete an uploaded document"""
    try:
        file_path = UPLOADS_DIR / filename
        
        if not file_path.exists():
            raise HTTPException(
                status_code=404,
                detail="File not found"
            )
        
        file_path.unlink()
        logger.info(f"File deleted successfully: {filename}")
        
        return {
            "success": True,
            "message": "File deleted successfully",
            "data": {
                "filename": filename,
                "deleted_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete file: {str(e)}"
        )
