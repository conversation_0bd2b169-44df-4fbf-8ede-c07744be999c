{"screen_flows": {"Homepage": {"screen_name": "Homepage", "use_case_related": null, "next_screen": "LoginPage", "before_screen": null}, "LoginPage": {"screen_name": "LoginPage", "use_case_related": "UC002_UserLogin", "next_screen": "RegistrationPage", "before_screen": "Homepage"}, "RegistrationPage": {"screen_name": "RegistrationPage", "use_case_related": "UC001_RegisterNewAccount", "next_screen": "LoginPage", "before_screen": "Homepage"}, "StaffDashboardPage": {"screen_name": "StaffDashboardPage", "use_case_related": null, "next_screen": "RequestListScreen_Training", "before_screen": "LoginPage"}, "ManagerDashboardPage": {"screen_name": "ManagerDashboardPage", "use_case_related": null, "next_screen": "ManageTrainingRequestsListPage", "before_screen": "LoginPage"}, "DirectorDashboardPage": {"screen_name": "DirectorDashboardPage", "use_case_related": null, "next_screen": "RequestListScreen_Training", "before_screen": "LoginPage"}, "CEODashboardPage": {"screen_name": "CEODashboardPage", "use_case_related": null, "next_screen": "RequestListScreen_Training", "before_screen": "LoginPage"}, "RequestListScreen_Training": {"screen_name": "RequestListScreen_Training", "use_case_related": null, "next_screen": "RequestDetailScreen_Training", "before_screen": "StaffDashboardPage"}, "RequestDetailScreen_Training": {"screen_name": "RequestDetailScreen_Training", "use_case_related": null, "next_screen": "RequestListScreen_Training", "before_screen": "RequestListScreen_Training"}, "CreateTrainingRequestScreen": {"screen_name": "CreateTrainingRequestScreen", "use_case_related": null, "next_screen": "RequestListScreen_Training", "before_screen": "StaffDashboardPage"}, "RoleChangeRequestScreen_StaffView": {"screen_name": "RoleChangeRequestScreen_StaffView", "use_case_related": null, "next_screen": null, "before_screen": "StaffDashboardPage"}, "ApproveRoleChangeRequestScreen": {"screen_name": "ApproveRoleChangeRequestScreen", "use_case_related": null, "next_screen": "RoleChangeRequestDetailsModal", "before_screen": "CEODashboardPage"}, "ProfileScreen": {"screen_name": "ProfileScreen", "use_case_related": null, "next_screen": "StaffDashboardPage", "before_screen": "StaffDashboardPage"}, "RoleChangeRequestDetailsModal": {"screen_name": "RoleChangeRequestDetailsModal", "use_case_related": null, "next_screen": "ApproveRoleChangeRequestScreen", "before_screen": "ApproveRoleChangeRequestScreen"}, "ManageTrainingRequestsListPage": {"screen_name": "ManageTrainingRequestsListPage", "use_case_related": null, "next_screen": "RequestDetailScreen_Training", "before_screen": "ManagerDashboardPage"}, "TrainingRequestHistoryPage": {"screen_name": "TrainingRequestHistoryPage", "use_case_related": null, "next_screen": "ManagerDashboardPage", "before_screen": "ManagerDashboardPage"}}, "use_case_flows": [{"number": "1", "id": "UC001_RegisterNewAccount", "screen_belong": "RegistrationPage", "description": "An unregistered user submits the registration form to create a new account, which is then redirected to the LoginPage upon successful submission.", "Role": "Unregistered User"}, {"number": "2", "id": "UC002_UserLogin", "screen_belong": "LoginPage", "description": "A user provides valid credentials to log in successfully and is redirected to their role-specific dashboard (Staff, Manager, Director, or CEO).", "Role": "Unregistered User"}]}