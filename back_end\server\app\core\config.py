"""
Application Configuration
Centralized configuration management with environment variables support
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings with environment variables support"""

    # =============================================================================
    # APPLICATION SETTINGS
    # =============================================================================
    APP_NAME: str = Field(default="Phoenix Pipeline Server", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    APP_DESCRIPTION: str = Field(
        default="Real-time pipeline execution server with WebSocket support",
        description="Application description"
    )

    # =============================================================================
    # SERVER CONFIGURATION
    # =============================================================================
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    DEBUG: bool = Field(default=True, description="Debug mode")
    RELOAD: bool = Field(default=True, description="Auto reload on code changes")

    # =============================================================================
    # CORS SETTINGS
    # =============================================================================
    ALLOWED_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001"
        ],
        description="Allowed CORS origins"
    )

    # =============================================================================
    # WEBSOCKET SETTINGS
    # =============================================================================
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket heartbeat interval (seconds)")
    MAX_CONNECTIONS: int = Field(default=100, description="Maximum WebSocket connections")
    WS_PING_INTERVAL: int = Field(default=20, description="WebSocket ping interval (seconds)")
    WS_PING_TIMEOUT: int = Field(default=20, description="WebSocket ping timeout (seconds)")

    # =============================================================================
    # LOGGING SETTINGS
    # =============================================================================
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )

    # =============================================================================
    # PIPELINE SETTINGS
    # =============================================================================
    PIPELINE_MAX_DURATION: int = Field(default=3600, description="Max pipeline duration (seconds)")
    PIPELINE_STEP_TIMEOUT: int = Field(default=300, description="Pipeline step timeout (seconds)")

    # =============================================================================
    # DEVELOPMENT SETTINGS
    # =============================================================================
    ENABLE_DOCS: bool = Field(default=True, description="Enable API documentation")
    DETAILED_ERRORS: bool = Field(default=True, description="Enable detailed error responses")

    # =============================================================================
    # SECURITY SETTINGS (Optional)
    # =============================================================================
    SECRET_KEY: Optional[str] = Field(default=None, description="Secret key for JWT tokens")
    JWT_EXPIRATION_MINUTES: int = Field(default=60, description="JWT token expiration (minutes)")
    RATE_LIMIT_PER_MINUTE: int = Field(default=100, description="API rate limit per minute")

    # =============================================================================
    # DATABASE SETTINGS (Optional)
    # =============================================================================
    DATABASE_URL: Optional[str] = Field(default=None, description="Database connection URL")
    DB_POOL_SIZE: int = Field(default=10, description="Database connection pool size")
    DB_MAX_OVERFLOW: int = Field(default=20, description="Database max overflow connections")

    # =============================================================================
    # EXTERNAL SERVICES (Optional)
    # =============================================================================
    REDIS_URL: Optional[str] = Field(default=None, description="Redis connection URL")
    SMTP_HOST: Optional[str] = Field(default=None, description="SMTP server host")
    SMTP_PORT: int = Field(default=587, description="SMTP server port")
    SMTP_USERNAME: Optional[str] = Field(default=None, description="SMTP username")
    SMTP_PASSWORD: Optional[str] = Field(default=None, description="SMTP password")

    # =============================================================================
    # MONITORING & METRICS (Optional)
    # =============================================================================
    ENABLE_METRICS: bool = Field(default=False, description="Enable Prometheus metrics")
    METRICS_PATH: str = Field(default="/metrics", description="Metrics endpoint path")
    HEALTH_CHECK_TIMEOUT: int = Field(default=30, description="Health check timeout (seconds)")

    # =============================================================================
    # PERFORMANCE SETTINGS
    # =============================================================================
    WORKERS: int = Field(default=1, description="Number of worker processes")
    WORKER_CLASS: str = Field(default="uvicorn.workers.UvicornWorker", description="Worker class")
    KEEP_ALIVE: int = Field(default=2, description="Keep alive timeout")
    MAX_REQUEST_SIZE: int = Field(default=16777216, description="Maximum request size (bytes)")

    class Config:
        env_file = ["back_end/server/.env", ".env"]  # Try server dir first, then current dir
        case_sensitive = True
        env_file_encoding = 'utf-8'
        # Force reload environment variables
        env_ignore_empty = False

    def get_cors_origins(self) -> List[str]:
        """Get CORS origins as list (handles comma-separated string from env)"""
        if isinstance(self.ALLOWED_ORIGINS, str):
            return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",")]
        return self.ALLOWED_ORIGINS

    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.DEBUG

    def is_production(self) -> bool:
        """Check if running in production mode"""
        return not self.DEBUG

    def get_log_level_int(self) -> int:
        """Get log level as integer"""
        import logging
        return getattr(logging, self.LOG_LEVEL.upper(), logging.INFO)

# Create global settings instance
settings = Settings()

# Validate settings on import
if settings.is_production() and not settings.SECRET_KEY:
    import warnings
    warnings.warn("SECRET_KEY not set in production mode", UserWarning)
