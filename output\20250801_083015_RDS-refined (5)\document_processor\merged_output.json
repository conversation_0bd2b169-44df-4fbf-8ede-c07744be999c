[{"heading_number": "I", "title": "I. <PERSON>", "Content": "....................................................................................................................................................."}, {"heading_number": "1", "title": "1. User Requirements", "Content": ".............................................................................................................................."}, {"heading_number": "1.1", "title": "1.1 Context", "Content": "Requirements\n..............................................................................................................................\n1\n \n1.1\n \nContext\n........................................................................................................................................\n1\n \n1.2\n \nActors\n..........................................................................................................................................\n3\n \n2.\n \nOverall\n \nFunctionalities\n........................................................................................................................\n4\n \n2.1\n \nScreens\n \nFlow\n...............................................................................................................................\n4\n \n2.2\n \nScreen\n \nDescriptions\n...................................................................................................................\n12\n \n2.3\n \nScreen\n \nAuthorization\n.................................................................................................................\n12\n \n2.4\n \nState\n \ntransition\n \ndiagram\n \nof\n \nRequest:\n.........................................................................................\n13\n \n3.\n \nSystem\n \nHigh\n \nLevel\n \nDesign\n................................................................................................................\n18\n \n3.1\n \nDatabase\n \nSchema:\n.....................................................................................................................\n18\n \n3.2\n \nDescription:\n...............................................................................................................................\n19\n \nII.\n \nRequirement\n \nSpecifications\n.....................................................................................................................\n20\n \n1.\n \nUse\n \nCase\n \nDescription\n.........................................................................................................................\n20\n \n1.1\n \nStaff\n...........................................................................................................................................\n20\n \n1.2.Manager\n....................................................................................................................................\n31\n \n1.3.\n \nDirector\n....................................................................................................................................\n38\n \n1.4.\n \nCEO\n..........................................................................................................................................\n42\n \n2.\n \nCommon\n \nFunctions\n............................................................................................................................\n44\n \n2.1\n \nUse\n \nCase:\n \nLogin\n \nSystem\n...........................................................................................................\n44\n \n2.2.\n \nUse\n \nCase:\n \nManage\n \nUser\n \nProfile\n...............................................................................................\n47\n \n2.3\n \nUse\n \nCase:\n \nView\n \nDashboard\n.......................................................................................................\n49\n \nIII.\n \nDesign\n \nSpecifications\n.............................................................................................................................\n54\n \n1.\n \nScreen\n \ndesign:\n....................................................................................................................................\n54\n \n1.1\n \nScreen:\n \nHomepage\n....................................................................................................................\n54\n \n1.2\n \nScreen:\n \nLogin\n \n&\n \nRegister\n..........................................................................................................\n57\n \n1.3\n \nScreen:\n \nDashBoard\n....................................................................................................................\n63\n \n1.4\n \nScreen:\n \nRole\n \nChange\n \nRequest\n...................................................................................................\n69\n \n1.5\n \nScreen:\n \nApprove\n \nRole\n \nChange\n \nRequest\n....................................................................................\n72\n \n1.6\n \nScreen:\n \nProfile\n...........................................................................................................................\n76\n \n1.7\n \nScreen:\n \nRequest\n \nList\n..................................................................................................................\n78\n \n1.8\n \nScreen:\n \nRequest\n \nDetails\n............................................................................................................\n81\n \n1.9\n \nScreen:\n \nCreateNewTrainingRequestPage\n.................................................................................\n84\n \nI.\n \nOverview\n \n1.\n \nUser\n \nRequirements\n \n1.1\n \nContext\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nContext\n \nDiagram\",\n\n\"system\":\n \n{\n \n    \n\"name\":\n \n\"RetailOnboardPro\n \nSystem\",\n \n    \n\"description\":\n \n\"Web\n \napplication\n \nfor\n \nmanaging\n \ntraining\n \nand\n \nrole\n \nrequests.\"\n \n  \n},\n \n  \n\"externalEntities\":\n \n[\n \n    \n{\n \n      \n\"name\":\n \n\"Unregistered\n \nUser\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Accesses\n \nregistration\n \npage\",\n \n        \n\"Submits\n \nregistration\n \nform\"\n \n      \n]\n \n    \n},\n \n    \n{\n \n      \n\"name\":\n \n\"Registered\n \nUser\n \n(STAFF,\n \nMANAGER,\n \nDIRECTOR,\n \nCEO)\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Logs\n \ninto\n \nthe\n \nsystem\",\n \n        \n\"Accesses\n \nfunctionalities\n \nbased\n \non\n \nrole\",\n \n        \n\"Manages\n \nprofile\n \n(view)\",\n \n        \n\"Logs\n \nout\n \nof\n \nthe\n \nsystem\"\n \n      \n]\n \n    \n},\n \n    \n{\n \n      \n\"name\":\n \n\"STAFF\n \nUser\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Creates\n \nTraining\n \nRequests\",\n \n        \n\"Views\n \nown\n \nTraining\n \nRequests\",\n \n        \n\"Creates\n \nRole\n \nChange\n \nRequests\",\n \n        \n\"Views\n \nown\n \nRole\n \nChange\n \nRequests\"\n \n      \n]\n \n    \n},\n \n    \n{\n \n      \n\"name\":\n \n\"MANAGER\n \nUser\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Views\n \nTraining\n \nRequests\",\n \n        \n\"Reviews\n \n(Approves/Rejects)\n \nTraining\n \nRequests\"\n \n      \n]\n\n},\n \n    \n{\n \n      \n\"name\":\n \n\"DIRECTOR\n \nUser\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Views\n \nTraining\n \nRequests\",\n \n        \n\"Reviews\n \n(Approves/Rejects)\n \nTraining\n \nRequests\",\n \n        \n\"Views\n \npending\n \nRole\n \nChange\n \nRequests\",\n \n        \n\"Reviews\n \n(Approves/Rejects)\n \nRole\n \nChange\n \nRequests\"\n \n      \n]\n \n    \n},\n \n    \n{\n \n      \n\"name\":\n \n\"CEO\n \nUser\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Views\n \nTraining\n \nRequests\",\n \n        \n\"Reviews\n \n(Approves/Rejects)\n \nTraining\n \nRequests\",\n \n        \n\"Views\n \npending\n \nRole\n \nChange\n \nRequests\",\n \n        \n\"Reviews\n \n(Approves/Rejects)\n \nRole\n \nChange\n \nRequests\"\n \n      \n]\n \n    \n},\n \n    \n{\n \n      \n\"name\":\n \n\"Email\n \nSystem\n \n(Implicit/Future)\",\n \n      \n\"description\":\n \n\"For\n \nsen"}, {"heading_number": "1.2", "title": "1.2 Actors", "Content": "about\n \nrequest\n \nstatus\n \n(optional,\n \nnot\n \nexplicitly\n \nimplemented\n \nyet).\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Receives\n \nnotification\n \ntriggers\n \nfrom\n \nRetailOnboardPro\"\n \n      \n]\n \n    \n}\n \n  \n]\n \n}\n \n \n1.2\n \nActors\n \nTable\n \n1:\n \nActor\n \n#\n \nActor\n \n \nDescription\n \n1\n \nManager\n \n \nTake\n \nresponsible\n \nto\n \nmanage\n \nrequest,\n \nchange\n \nstatus\n \n2\n \nStaff\n \n \nCreate\n \nand\n \nsend\n \nrequest\n \nto\n \nmanager\n\n3\n \nDirector\n \n \nReviews\n \nand\n \napproves/rejects\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers\n \nor\n \nrequiring\n \n4\n \nCEO\n \n \nHighest\n \nlevel\n \nof\n \napproval\n \nfor\n \nspecific\n \nor\n \nhigh-impact\n \ntraining\n \nrequests.\n \n \n2.\n \nOverall\n \nFunctionalities\n \n2.1\n \nScreens\n \nFlow\n \n2.1.0)\n \n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \n-\n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"Homepage\"\n,\n \n\"description\"\n:\n \n\"Initial\n \nlanding\n \npage\n \nfor\n \nall\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RegistrationPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n}\n \n    \n//\n \nRole-specific\n \ndashboards\n \nare\n \nlisted\n \nin\n \ntheir\n \nrespective\n \nflows\n \nbut\n \nare\n \ntargets\n \nhere\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Login'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register\n \nhere'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RegistrationPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nregistration\n \nsubmission\n \n(UC001_RegisterNewAccount)\n \nOR\n \nClicks\n \n'Already\n \nhave\n \nan\n \naccount?\n \nLogin'\n \nlink\"\n \n},"}, {"heading_number": "section_0_", "title": "", "Content": "Director\n \n \nReviews\n \nand\n \napproves/rejects\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers\n \nor\n \nrequiring\n \n4\n \nCEO\n \n \nHighest\n \nlevel\n \nof\n \napproval\n \nfor\n \nspecific\n \nor\n \nhigh-impact"}, {"heading_number": "2", "title": "2. Overall Functionalities", "Content": ""}, {"heading_number": "2.1", "title": "2.1 Screens Flow", "Content": "2.\n \nOverall\n \nFunctionalities\n \n2.1\n \nScreens\n \nFlow\n \n2.1.0)\n \n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \n-\n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"Homepage\"\n,\n \n\"description\"\n:\n \n\"Initial\n \nlanding\n \npage\n \nfor\n \nall\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RegistrationPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n}\n \n    \n//\n \nRole-specific\n \ndashboards\n \nare\n \nlisted\n \nin\n \ntheir\n \nrespective\n \nflows\n \nbut\n \nare\n \ntargets\n \nhere\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Login'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register\n \nhere'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RegistrationPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nregistration\n \nsubmission\n \n(UC001_RegisterNewAccount)\n \nOR\n \nClicks\n \n'Already\n \nhave\n \nan\n \naccount?\n \nLogin'\n \nlink\"\n \n},\n\n//\n \nLogin\n \nsuccess\n \ntransitions\n \nare\n \nrole-specific,\n \ndetailed\n \nbelow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nSTAFF\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nMANAGER\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nDIRECTOR\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nCEO\n \n(UC002_UserLogin)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \ndescribes\n \nthe\n \ninitial\n \naccess\n \nand\n \nauthentication\n \npathways.\n \nPost-login\n \nflows\n \nare\n \ndetailed\n \nper\n \nrole.\"\n \n}\n \n2.1.1)\n \nStaff\n \nscreen\n \nflow\n \n \nFigur e\n \n1:\n \nStaff\n \nScreen\n \nFlow\n \ndiagram\n \n•\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nStaff\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nStaffDashboard,\n \nRequestList,\n \nSendRequest\n  \n \no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \nrequest\n \nlist,\n \nBack,\n \nSend\n \nnew\n \nrequest,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nStaffDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nStaffDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \nrequest\n \nlist\"\n \nor\n \n\"Send\n \nnew\n \nrequest\".\n  \n \n5.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nStaffDashboard\n \nfrom\n \neither\n \nRequestList\n \nor\n \nSendRequest.\n\n6.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nstaff\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \nStaff's\n \nown\n \ntraining\n \nand\n \nrole\n \nchange\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewTrainingRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest.\"\n \n},\n \n    \n//\n \nAdd\n \nRoleChangeRequestDetailsPage\n \nif\n \nit\n's\n \na\n \nseparate\n \nscreen\n \nfor\n \nviewing\n \nrole\n \nrequest\n \ndetails\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\",\n \n\"description\":\n \n\"Staff'\ns\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\n\"\n \n},\n \n    \n{\n \n\"\nname\n\":\n \n\"\nLoginPage\n\",\n \n\"\ndescription\n\":\n \n\"\nTarget\n \npage\n \nafter\n \nlogout\n.\n\"\n \n}\n \n  \n],\n \n  \n\"\ntransitions\n\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nMy\n \nRequests'\n \nor\n \nsimilar\n \nlink\n \n(UC006,\n \nUC009)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'CreateNewTrainingRequestPage'\n \n(UC005)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Create\n \nRole\n \nChange\n \nRequest'\n \n(UC-RC001)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nProfilePage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Profile'\n \nlink\n \n(UC012)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \ntraining\n \nrequest\n\"\n \n},\n \n    \n//\n \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nRoleChangeRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \ntraining\n \nrequest\n \n(UC005)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \nrole\n \nchange\n \nrequest\n \n(UC-RC001)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n \n    \n//\n \nLogout\n \nfrom\n \nvarious\n \nstaff\n \npages\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n}\n \n  \n],\n \n  \n\"\nnotes\n\":\n \n\"\nPrimary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nStaff\n \nuser.\n\"\n \n}\n \n \n2.1.2)\n \nManager\n \nscreen\n \nflow\n \n \nFigur e\n \n2:\n \nManager\n \nScreen\n \nFlow\n \ndiagram\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nManager\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nManagerDashboard,\n \nManageRequest,\n \nRequestHistory,\n \nUpdateStatus\n\no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \n/\n \nManage\n \nrequests,\n \nView\n \nrequest\n \nhistory,\n \nBack,\n \nChange\n \nrequest\n \nstatus,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nManagerDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nManagerDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \n/\n \nManage\n \nrequests\"\n \nor\n \n\"View\n \nrequest\n \nhistory\".\n  \n \n5.\n \nWithin\n \n\"Manage\n \nrequests\",\n \nthe\n \nuser\n \ncan\n \n\"Change\n \nrequest\n \nstatus\",\n \nleading\n \nto\n \nthe\n \nUpdateStatus\n \nscreen.\n  \n \n6.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nManagerDashboard\n \nfrom\n \neither\n \nManageRequest\n \nor\n \nRequestHistory.\n  \n \n7.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nManager\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \ntraining\n \nrequests\n \npending\n \nManager's\n \naction\n \nor\n \nrelevant\n \nto\n \ntheir\n \nteam.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest\n \nfor\n \nManager\n \nreview/action.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \nhistorical\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \nthe\n \nManager.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Manager's\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Target\n \npage\n \nafter\n \nlogout.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nTraining\n \nRequest\n \nHistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n//\n \nLogout\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003_UserLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nlogout\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"Primary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nManager\n \nuser.\"\n \n}\n \n2.1.3)\n \nDirector\n \nscreen\n \nflow\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nDIRECTOR\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nDIRECTOR,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-DR001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \nif\n \ndifferent\n \nfrom\n \nManager's\n \nlist\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nDIRECTOR's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\"\n:\n \n[\n\n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\"\n:\n \n\"The\n \nDIRECTOR\n \ncan\n \nview\n \nand\n \nreview\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers.\n \nThey\n \ntypically\n \ndo\n \nnot\n \nmanage\n \nrole\n \nchange\n \nrequests\n \ndirectly,\n \nwhich\n \nare\n \nhandled\n \nby\n \nthe\n \nCEO.\"\n \n}\n \n2.1.4)\n \nCEO\n \nScreen\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nCEO\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"CEODashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nCEO,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests,\n \nrole\n \nrequests,\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-CE001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n\n{\n \n\"name\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \npending\n \nrole\n \nchange\n \nrequests\n \n(UC-RC002).\"\n \n},\n \n//\n \nCorresponds\n \nto\n \nscreen\n \non\n \np64\n \n    \n{\n \n\"name\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"description\"\n:\n \n\"Modal\n \nwithin\n \nApproveRoleChangeRequestPage\n \nfor\n \nCEO\n \nto\n \naction\n \na\n \nspecific\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n//\n \nFrom\n \np66\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nCEO's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-CE001:\n \nCEO\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Approve\n \nRole\n \nChange\n \nRequests'\n \nlink\n \n(UC-RC002:\n \nCEO\n \nManages\n \nRole\n \nChange\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \ntraining\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Process'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Submits\n \ndecision\n \nor\n \nCancels\n \nmodal\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"The\n \nCEO\n \nhas\n \norganization-wide\n \nauthority\n \nfor\n \nfinal\n \napproval\n \nof\n \ntraining\n \nrequests\n \nand\n \nmanages"}, {"heading_number": "2.2", "title": "2.2 Screen Descriptions", "Content": "}\n \n2.2"}, {"heading_number": "2.3", "title": "2.3 Screen Authorization", "Content": "}\n \n2.2\n \nScreen\n \nDescriptions\n \n2.3\n \nScreen\n \nAuthorization\n \n \nTable\n \n4:\n \nScreen\n \nauthorization\n \nScreen\n \nStaff\n \nManager\n \nDirector\n \nCEO\n \n**Training\n \nRequests**\n \nCreateNewTrainingRequestPage\n \nX\n \n \n \n \nView\n \nOwn\n \nTraining\n \nRequests\n \nX\n \nX\n \nX\n \nX\n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nManager)\n \n \nX\n \n \n \nApprove/Reject\n \n(Manager\n \nLevel)\n \n \nX\n \n \n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nDirector)\n \n \n \nX\n \n \nApprove/Reject\n \n(Director\n \nLevel)\n \n \n \nX\n \n \nView\n \nTraining\n \nRequests\n \n(Pending\n \nCEO)\n \n \n \n \nX\n \nApprove/Reject\n \n(CEO\n \nLevel)\n \n \n \n \nX\n \nView\n \nAll/Detailed\n \nTraining\n \nRequests\n \n \n(Scope)\n \n(Scope)\n \nX\n \nView\n \nTraining\n \nRequest\n \nHistory\n \nX\n \nX\n \nX\n \nX\n \n**Role\n \nChange\n \nRequests**\n \nCreate\n \nNew\n \nRole\n \nChange\n \nRequest\n \nX\n \n \n \n \nView\n \nOwn\n \nRole\n \nChange\n \nRequests\n \nX\n \n \n \n \nView\n \nPending\n \nRole\n \nChange\n \nRequests\n \n \n \n \nX\n \nReview\n \n(Approve/Reject)\n \nRole\n \nChange\n \nRequest\n \n \n \n \nX\n \nView\n \nRole\n \nChange\n \nRequests\n \nby\n \nStatus\n \n \n \n \nX\n \n**General**\n \nLogin\n \nX\n \nX\n \nX\n \nX\n \nDashboard\n \nX\n \nX\n \nX\n \nX\n \nUser\n \nProfile\n \nManagement\n \nX\n \nX\n \nX\n \nX\n \n \n**Notes:**\n \n*\n   \n'X'\n \nindicates\n \nthe\n \nrole\n \nhas\n \naccess\n \nto\n \nthe\n \nfeature.\n \n*\n   \n'(Scope)'\n \nindicates\n \naccess\n \nmight\n \nbe\n \nlimited\n \nto\n \nrequests\n \nwithin\n \ntheir\n \ndirect\n \nteam\n \nor\n \ndepartment,\n \nor\n \nspecific\n \nstatuses\n \nrelevant\n \nto\n \ntheir\n \nrole,\n \nrather\n \nthan\n \nall\n \nrequests\n \nsystem-wide\n \n(except\n \nfor\n \nCEO\n \nwho\n \ngenerally\n \nhas\n \nfull\n \nvisibility)."}, {"heading_number": "2.4", "title": "2.4 State transition diagram of Request:", "Content": "Request:\n\nFigur e\n \n3:\n \nState\n \ntransition\n \ndiagram\n \nof\n \nRequest\n \n \nElements:\n \n \n•\n \nStates:\n \nPending,\n \nProcessing,\n \nApproved,\n \nRejected,\n \nClosed.\n \n \n•\n \nState\n \nTransitions:\n \n \no\n \nPending\n \n→\n \nProcessing:\n \nWhen\n \nthe\n \nmanager\n \nstarts\n \nreviewing\n \nthe\n \nrequest.\n \n \no\n \nProcessing\n \n→\n \nApproved:\n \nWhen\n \nthe\n \nmanager\n \napproves\n \nthe\n \nrequest.\n \n \no\n \nProcessing\n \n→\n \nRejected:\n \nWhen\n \nthe\n \nmanager\n \nrejects\n \nthe\n \nrequest.\n \n \no\n \nApproved\n \n→\n \nClosed:\n \nWhen\n \nthe\n \nrequest\n \nis\n \nfulfilled.\n \n \no\n \nRejected\n \n→\n \nClosed:\n \nWhen\n \nthe\n \nrequest\n \nis\n \nfinalized\n \nand\n \nno\n \nfurther\n \nprocessing\n \nis\n \nneeded.\n \n \n \nFlow:\n \n \n1.\n \nThe\n \nstaff\n \ncreates\n \na\n \nnew\n \nrequest\n \n→\n \nthe\n \nstate\n \ntransitions\n \nto\n \nPending.\n \n \n2.\n \nThe\n \nmanager\n \nreviews\n \nthe\n \nrequest\n \n→\n \nthe\n \nstate\n \ntransitions\n \nto\n \nProcessing.\n \n \n3.\n \nThe\n \nmanager\n \nmakes\n \na\n \ndecision:\n \n \no\n \nIf\n \napproved,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nApproved.\n \n \no\n \nIf\n \nrejected,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nRejected.\n \n \n4.\n \nWhen\n \nthe\n \nrequest\n \nis\n \ncompleted:\n \n \no\n \nIf\n \nApproved,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nClosed.\n \n \no\n \nIf\n \nRejected,\n \nthe\n \nstate\n \ntransitions\n \nto\n \nClosed.\n \n \n5.\n \nThe\n \nsystem\n \ncompletes\n \nthe\n \nrequest\n \nlifecycle\n \nwhen\n \nthe\n \nClosed\n \nstate\n \nis\n \nreached.\n \n \na)\n \nTraining\n \nRequest\n \nState\n \nTransitions\n\n{\n \n  \n\"title\":\n \n\"Training\n \nRequest\n \nState\n \nTransition\",\n \n  \n\"states\":\n \n[\n \n    \n\"Draft\",\n \n    \n\"Submitted\",\n \n    \n\"Approved_by_Manager\",\n \n    \n\"Rejected_by_Manager\",\n \n    \n\"Approved_by_Director\",\n \n    \n\"Rejected_by_Director\",\n \n    \n\"Approved_by_CEO\",\n \n    \n\"Rejected_by_CEO\",\n \n    \n\"Approved_Final\",\n \n    \n\"End\"\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\":\n \n\"Draft\",\n \n\"to\":\n \n\"Submitted\",\n \n\"event\":\n \n\"submit\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Approved_by_Manager\",\n \n\"event\":\n \n\"manager\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Rejected_by_Manager\",\n \n\"event\":\n \n\"manager\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Manager\",\n \n\"to\":\n \n\"Approved_by_Director\",\n \n\"event\":\n \n\"director\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Manager\",\n \n\"to\":\n \n\"Rejected_by_Director\",\n \n\"event\":\n \n\"director\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Director\",\n \n\"to\":\n \n\"Approved_by_CEO\",\n \n\"event\":\n \n\"CEO\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_Director\",\n \n\"to\":\n \n\"Rejected_by_CEO\",\n \n\"event\":\n \n\"CEO\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_by_CEO\",\n \n\"to\":\n \n\"Approved_Final\",\n \n\"event\":\n \n\"final\n \napproval\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected_by_Manager\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n\n{\n \n\"from\":\n \n\"Rejected_by_Director\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected_by_CEO\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"terminated\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved_Final\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"complete\"\n \n}\n \n  \n],\n \n  \n\"initial_state\":\n \n\"Draft\",\n \n  \n\"final_state\":\n \n\"End\"\n \n}\n \n \n \n \nb)\n \nRole\n \nChange\n \nRequest\n \nState\n \nTransitions\n \n \n \n{\n \n  \n\"title\":\n \n\"Role\n \nChange\n \nRequest\n \nState\n \nTransition\",\n \n  \n\"states\":\n \n[\n \n    \n\"Submitted\",\n \n    \n\"Approved\",\n \n    \n\"Rejected\",\n \n    \n\"End\"\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Approved\",\n \n\"event\":\n \n\"admin\n \napproves\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Submitted\",\n \n\"to\":\n \n\"Rejected\",\n \n\"event\":\n \n\"admin\n \nrejects\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Approved\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"finalize\"\n \n},\n \n    \n{\n \n\"from\":\n \n\"Rejected\",\n \n\"to\":\n \n\"End\",\n \n\"event\":\n \n\"finalize\"\n \n}\n \n  \n],\n \n  \n\"initial_state\":\n \n\"Submitted\",\n \n  \n\"final_state\":\n \n\"End\"\n\n}"}, {"heading_number": "3", "title": "3. System High Level Design", "Content": "Design"}, {"heading_number": "3.1", "title": "3.1 Database Schema:", "Content": "Level\n \nDesign\n \n3.1\n \nDatabase\n \nSchema:\n \n \n \nFigur e\n \n4:\n \nDatabase\n \nschema"}, {"heading_number": "3.2", "title": "3.2 Description:", "Content": "Table\n \n5:\n \nERD\n \ndiagram\n \ndescription\n \nNo\n \nTable\n \nDescription\n \n01\n \nusers\n \nid\n \n(INT,\n \nIDENTITY,\n \nPRIMARY\n \nKEY)\n \nname\n \n(VARCHAR(255),\n \nNOT\n \nNULL,\n \nUNIQUE)\n \nemail\n \n(VARCHAR(255),\n \nNOT\n \nNULL,\n \nUNIQUE)\n \npassword\n \n(VARCHAR(255),\n \nNOT\n \nNULL)\n \nrole\n \n(VARCHAR(50),\n \nNOT\n \nNULL)\n \ncreated_at\n \n(TIMESTAMP)\n \nupdated_at\n \n(TIMESTAMP)\n \n02\n \nrequests\n \nid\n \n(INT,\n \nIDENTITY,\n \nPRIMARY\n \nKEY)\n \nuser_id\n \n(INT,\n \nFK\n \nusers(id)\n \n)\n \ncategory\n \n(VARCHAR(255))\n \nreason\n \n(VARCHAR(MAX))\n \nstatus\n \n(VARCHAR(50),\n \nNOT\n \nNULL)\n \ncreated_at\n \n(TIMESTAMP)\n \nupdated_at\n \n(TIMESTAMP)\n \n03\n \nApproval_history\n \nid\n \n(INT,\n \nIDENTITY,\n \nPRIMARY\n \nKEY)\n \nrequest_id\n \n(INT,\n \nFK\n \nrequests(id))\n \napprover_id\n \n(INT,\n \nFK\n \nusers(id))\n \naction\n \n(VARCHAR(50),\n \nNOT\n \nNULL)\n \ncomments\n \n(VARCHAR(MAX))\n \naction_at\n \n(TIMESTAMP)\n \n04\n \nRole_requests\n \nId\n \n(INT,\n \nIDENTITY,\n \nPRIMARY\n \nKEY)\n \nuser_id\n \n(INT,\n \nFK\n \nusers(id)\n \n)\n \nrequested_role\n \n(VARCHAR(50),\n \nNOT\n \nNULL)\n \nreason\n \n(VARCHAR(MAX))\n\nstatus\n \n(VARCHAR(50))\n \nadmin_reviewer_id\n \n(INT,\n \nFK\n \nusers(id)\n \n)\n \nadmin_comments\n \n(VARCHAR(MAX))\n \ncreated_at\n \n(TIMESTAMP)\n \nupdated_at\n \n(TIMESTAMP)\n \n \nDescription\n \nof\n \nERD\n \nin\n \njson:\n \n{\n \n\"diagram_name\":\n \n\"ERD\n \nDiagram\",\n \n\"objects\":\n \n[\n \n{\n \n\"object_name\":\n \n\"Role\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"rid\",\n \n\"rname\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"rid\",\n \n\"fid\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"Feature\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"fid\",\n \n\"fname\",\n \n\"url\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"UserRole\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"username\",\n \n\"rid\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"users\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"username\",\n \n\"password\",\n \n\"role\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"requests\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"user_id\",\n \n\"status\",\n \n\"created_at\",\n \n\"updated_at\",\n \n\"description\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"request_history\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"request_id\",\n \n\"status\",\n \n\"changed_at\"]\n \n}\n \n],\n \n\"connections\":\n \n[\n \n{\n \n\"source\":\n \n\"Role\",\n \n\"target\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"Feature\",\n \n\"target\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"Role\",\n \n\"target\":\n \n\"UserRole\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"users\",\n \n\"target\":\n \n\"UserRole\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"users\",\n \n\"target\""}, {"heading_number": "II", "title": "II. Requirement Specifications", "Content": "},\n \n{\n \n\"source\":\n \n\"requests\",\n \n\"target\":\n \n\"request_history\",\n \n\"type\":\n \n\"one-to-many\"\n \n}\n \n]\n \n}\n \nII.\n \nRequirement\n \nSpe"}, {"heading_number": "1", "title": "1. Use Case Description", "Content": "of\n \nERD\n \nin\n \njson:\n \n{\n \n\"diagram_name\":\n \n\"ERD\n \nDiagram\",\n \n\"objects\":\n \n[\n \n{\n \n\"object_name\":\n \n\"Role\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"rid\",\n \n\"rname\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"rid\",\n \n\"fid\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"Feature\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"fid\",\n \n\"fname\",\n \n\"url\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"UserRole\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"username\",\n \n\"rid\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"users\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"username\",\n \n\"password\",\n \n\"role\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"requests\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"user_id\",\n \n\"status\",\n \n\"created_at\",\n \n\"updated_at\",\n \n\"description\"]\n \n},\n \n{\n \n\"object_name\":\n \n\"request_history\",\n \n\"type\":\n \n\"table\",\n \n\"columns\":\n \n[\"id\",\n \n\"request_id\",\n \n\"status\",\n \n\"changed_at\"]\n \n}\n \n],\n \n\"connections\":\n \n[\n \n{\n \n\"source\":\n \n\"Role\",\n \n\"target\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"Feature\",\n \n\"target\":\n \n\"RoleFeature\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"Role\",\n \n\"target\":\n \n\"UserRole\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"users\",\n \n\"target\":\n \n\"UserRole\",\n \n\"type\":\n \n\"one-to-many\"\n \n},\n \n{\n \n\"source\":\n \n\"users\",\n \n\"target\":\n \n\"requests\",\n \n\"type\":\n \n\"one-to-many\"\n \n},"}, {"heading_number": "1.1", "title": "1.1 Staff", "Content": "{\n \n\"source\":\n \n\"requests\",\n \n\"target\":\n \n\"request_history\",\n \n\"type\":\n \n\"one-to-many\"\n \n}\n \n]\n \n}\n \nII.\n \nRequirement\n \nSpecifications\n \n \n1.\n \nUse\n \nCase\n \nDescription\n \n1.1\n \nStaff\n \na.Diagram\n\n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \nview\n \ntheir\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"SendRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \ncreate\n \nnew\n \nrequests\n \n(e.g.,\n \nTraining\n \nor\n \nRole\n \nChange).\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \nif\n \ndirectly\n \naccessible\n \nand\n \ndistinct\n \nfor\n \nStaff\n \nflow\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nlist'\n \n(e.g.,\n \nUC006,\n \nUC009)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"SendRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Send\n \nnew\n \nrequest'\n \n(e.g.,\n \nUC005,\n \nUC-RC001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestListPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"SendRequestPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nor\n \nafter\n \nsuccessful\n \nsubmission\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \nlike\n \nto\n \nProfilePage\n \nif\n \napplicable\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nStaff\n \nuser.\"\n \n}\n \n \nb.\n \nDescriptions\n \n \n1.1.1\n \nUse\n \nCase:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \n \n \nTable\n \n12\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-TR001\n \nUse\n \nCase\n \nName:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nStaff\n \nSecondary\n \nActors:\n \nSýtem\n \nStaff,\n \nSystem\n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Create\n \nTraining\n \nRequest'\n \nsection\n \nfrom\n \ntheir\n \ndashboard.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \ncreate\n \nand\n \nsubmit\n \na\n \nnew\n \ntraining\n \nrequest.\n \nThe\n \nuser\n \nfills\n \nin\n \ndetails\n \nsuch\n \nas\n \nTraining\n \nCategory,\n \nReason\n \nfor\n \nTraining,\n \nand\n \nany\n \nsupporting\n \ndocuments.\n \nThe\n \nsystem\n \nvalidates\n \nthe\n \nrequest\n \nand,\n \nupon\n \nsuccessful\n \nsubmission,\n \nstores\n \nit\n \nwith\n \nan\n \ninitial\n \nstatus\n \nof\n \n`Pending_Manager`.\n \nThe\n \nuser\n \nis\n \nnotified\n \nof\n \nthe\n \nsuccessful\n \ncreation.\n \nPreconditions:\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \nThe\n \nStaff\n \nuser\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \ncreate\n \ntraining\n \nrequests.\n \nThe\n \nsystem\n \nhas\n \ndefined\n \ntraining\n \ncategories\n \navailable\n \nfor\n \nselection.\n \nPostconditions:\n \n-\n \n**Success**:\n \nA\n \nnew\n \ntraining\n \nrequest\n \nrecord\n \nis\n \ncreated\n \nin\n \nthe\n \n`requests`\n \ntable\n \nwith\n \na\n \nunique\n \n`request_id`,\n \n`user_id`\n \nof\n \nthe\n \ncreator,\n \n`request_type`\n \nset\n \nto\n \n'Training',\n \nand\n\n`status`\n \nset\n \nto\n \n`Pending_Manager`.\n \nThe\n \nuser\n \nis\n \nredirected\n \nto\n \na\n \nconfirmation\n \npage\n \nor\n \ntheir\n \nlist\n \nof\n \nrequests.\n \n-\n \n**Failure**:\n \nIf\n \nvalidation\n \nfails\n \nor\n \na\n \nsystem\n \nerror\n \noccurs,\n \nthe\n \nrequest\n \nis\n \nnot\n \nsaved,\n \nand\n \nthe\n \nuser\n \nis\n \ninformed\n \nof\n \nthe\n \nissue.\n \nExpected\n \nResults\n \nAfter\n \nthe\n \nManager\n \nclicks\n \n'Approve'\n \n(or\n \n'Reject')\n \nfor\n \na\n \nspecific\n \nrequest\n \non\n \nthe\n \n'Manage\n \nRequest'\n \nscreen\n \nand\n \nconfirms\n \nthe\n \naction\n \nvia\n \nthe\n \nalert\n \npopup,\n \nthe\n \nsystem\n \nsuccessfully updates\n \nthat\n \nrequest's\n \nstatus\n \nin\n \nthe\n \ndatabase\n \nto\n \n'Approved'\n \n(or\n \n'Rejected').\n \nThe\n \nstatus\n \nof\n \nthat\n \nrequest\n \nis\n \nchanged\n \nto\n \n`Approved`(or\n \nRejected)\n \nin\n \nHistory\n \nor\n \nStaff\n \nview.\n \nNormal\n \nFlow:\n \n1.\n  \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC_1:\n \nLogin\n \nSystem).\n \n2.\n  \nStaff\n \nuser\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n \n3.\n  \nStaff\n \nuser\n \nselects\n \nthe\n \noption\n \nto\n \n'Create\n \nTraining\n \nRequest'.\n \n4.\n  \nSystem\n \ndisplays\n \nthe\n \nTraining\n \nRequest\n \nform,\n \nincluding\n \nfields\n \nfor:\n \n    \n*\n   \nTraining\n \nCategory\n \n(e.g.,\n \ndropdown:\n \nTechnical,\n \nSoft\n \nSkills,\n \nCompliance)\n \n    \n*\n   \nReason\n \nfor\n \nTraining\n \n(e.g.,\n \ntext\n \narea)\n \n    \n*\n   \nPreferred\n \nDates\n \n(optional,\n \ne.g.,\n \ndate\n \npicker)\n \n    \n*\n   \nSupporting\n \nDocuments\n \n(optional,\n \ne.g.,\n \nfile\n \nupload)\n \n5.\n  \nStaff\n \nuser\n \nfills\n \nin\n \nthe\n \nrequired\n \ninformation.\n \n6.\n  \nStaff\n \nuser\n \nclicks\n \nthe\n \n'Submit\n \nRequest'\n \nbutton.\n \n7.\n  \nSystem\n \nvalidates\n \nthe\n \nsubmitted\n \ndata\n \nbased\n \non\n \ndefined\n \nbusiness\n \nrules\n \n(e.g.,\n \nBR-TR001-2,\n \nBR-TR001-3,\n \nBR-TR001-5,\n \nBR-TR001-6).\n \n8.\n  \nIf\n \nvalidation\n \nis\n \nsuccessful:\n \n    \na.\n  \nSystem\n \ngenerates\n \na\n \nunique\n \n`request_id`\n \n(BR-TR001-1).\n \n    \nb.\n  \nSystem\n \nsaves\n \nthe\n \ntraining\n \nrequest\n \ndetails\n \nto\n \nthe\n \n`requests`\n \ntable,\n \nincluding\n \n`user_id`,\n \n`request_type`\n \n('Training'),\n \n`category`,\n \n`reason`,\n \n`submission_date`\n \n(current\n \ndate/time),\n \nand\n \nsets\n \nthe\n \ninitial\n \n`status`\n \nto\n \n`Pending_Manager`\n \n(BR-TR001-4).\n \n    \nc.\n  \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage\n \nto\n \nthe\n \nStaff\n \nuser\n \n(e.g.,\n \n\"Training\n \nrequest\n \nsubmitted\n \nsuccessfully.\n \nYour\n \nRequest\n \nID\n \nis\n \n[request_id].\").\n \n    \nd.\n  \nSystem\n \nmay\n \nredirect\n \nthe\n \nuser\n \nto\n \ntheir\n \n'My\n \nTraining\n \nRequests'\n \nlist\n \n(references\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests).\n \n9.\n  \nIf\n \nthe\n \nstaff\n \nwant\n \nto\n \ncancel\n \nthe\n \nprocess,\n \nthey\n \nclick\n \n“Cancel”.\n \nAlternative\n \nFlows:\n \n-\n   \n**AF1:\n \nInvalid\n \nData\n \nSubmission**\n \n    \n-\n   \nAt\n \nstep\n \n7,\n \nif\n \nsystem\n \nvalidation\n \nfails:\n\n-\n   \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \nindicating\n \nthe\n \nspecific\n \nvalidation\n \nerrors\n \n(e.g.,\n \n\"Reason\n \nfor\n \nTraining\n \nis\n \nrequired.\").\n \n        \n-\n   \nThe\n \nTraining\n \nRequest\n \nform\n \nremains\n \npopulated\n \nwith\n \nthe\n \nuser's\n \nentered\n \ndata.\n \n        \n-\n   \nUser\n \ncorrects\n \nthe\n \ndata\n \nand\n \nre-submits\n \n(returns\n \nto\n \nstep\n \n5\n \nof\n \nNormal\n \nFlow).\n \n-\n   \n**AF2:\n \nCancel\n \nRequest\n \nCreation**\n \n    \n-\n   \nAt\n \nany\n \npoint\n \nbefore\n \nstep\n \n8,\n \nif\n \nthe\n \nStaff\n \nuser\n \nclicks\n \na\n \n'Cancel'\n \nbutton:\n \n        \n-\n   \nSystem\n \ndiscards\n \nany\n \nentered\n \ndata.\n \n        \n-\n   \nSystem\n \nredirects\n \nthe\n \nuser\n \nback\n \nto\n \ntheir\n \ndashboard\n \nor\n \nthe\n \nprevious\n \npage.\n \nExceptions:\n \n-\n   \n**E1:\n \nSystem\n \nError\n \nDuring\n \nSave**\n \n    \n-\n   \nAt\n \nstep\n \n8b,\n \nif\n \na\n \ndatabase\n \nerror\n \nor\n \nother\n \nsystem\n \nerror\n \noccurs\n \nwhile\n \nattempting\n \nto\n \nsave\n \nthe\n \nrequest:\n \n        \n-\n   \nSystem\n \nlogs\n \nthe\n \nerror.\n \n        \n-\n   \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser\n \n(e.g.,\n \n\"An\n \nunexpected\n \nerror\n \noccurred.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n        \n-\n   \nThe\n \nrequest\n \nis\n \nnot\n \nsaved.\n \nPriority:\n \nHigh\n \n \nFrequency\n \nof\n \nUse:\n \nModerate\n \nBusiness\n \nRules:\n \nBR-TR001-1,\n \nBR-TR001-2,\n \nBR-TR001-3,\n \nBR-TR001-4,\n \nBR-TR001-5,\n \nBR-TR001-6\n \nUse\n \nCase\n \nrelated:\n \n \nRelated\n \nUse\n \nCases:\n \n“View\n \nRequest\n \nHistory”\n \n(UC-MR002)\n \n,\n \n“View\n \nRequest\n \nList”\n \n(UC-SR002),\n \n“Send\n \nRequest”(\n \nUC-SR001)\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nCreateTrainingRequestPage\n \nAssumptions:\n \n \nThe\n \napproval\n \nworkflow\n \n(Manager,\n \nDirector,\n \nCEO)\n \nis\n \ndefined\n \nelsewhere.\n \n \n \nTable\n \n13.\n \nBusiness\n \nrule\n \nof\n \nuse\n \ncase:\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR1\n \nUnique\n \nRequest\n \nID\n \nEach\n \ntraining\n \nrequest\n \nmust\n \nhave\n \na\n \nunique,\n \nsystem-generated\n \n`request_id`.\n \nThis\n \nID\n \nshould\n \nnot\n \nbe\n \neditable\n \nby\n \nthe\n \nuser.\n                                                     \n \nBR2\n \n \nMandatory\n \nFields\n \n'Training\n \nCategory'\n \nand\n \n'Reason\n \nfor\n \nTraining'\n \nare\n \nmandatory\n \nfields.\n \nForm\n \nsubmission\n \nshould\n \nbe\n \nblocked\n \nif\n \nthese\n \nfields\n \nare\n \nempty."}, {"heading_number": "section_0_", "title": "", "Content": "-\n   \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \nindicating\n \nthe\n \nspecific\n \nvalidation\n \nerrors\n \n(e.g.,\n \n\"Reason\n \nfor\n \nTraining\n \nis\n \nrequired.\").\n \n        \n-\n   \nThe\n \nTraining\n \nRequest\n \nform\n \nremains\n \npopulated\n \nwith\n \nthe\n \nuser's\n \nentered\n \ndata.\n \n        \n-\n   \nUser\n \ncorrects\n \nthe\n \ndata\n \nand\n \nre-submits\n \n(returns\n \nto\n \nstep\n \n5\n \nof\n \nNormal\n \nFlow).\n \n-\n   \n**AF2:\n \nCancel\n \nRequest\n \nCreation**\n \n    \n-\n   \nAt\n \nany\n \npoint\n \nbefore\n \nstep\n \n8,\n \nif\n \nthe\n \nStaff\n \nuser\n \nclicks\n \na\n \n'Cancel'\n \nbutton:\n \n        \n-\n   \nSystem\n \ndiscards\n \nany\n \nentered\n \ndata.\n \n        \n-\n   \nSystem\n \nredirects\n \nthe\n \nuser\n \nback\n \nto\n \ntheir\n \ndashboard\n \nor\n \nthe\n \nprevious\n \npage.\n \nExceptions:\n \n-\n   \n**E1:\n \nSystem\n \nError\n \nDuring\n \nSave**\n \n    \n-\n   \nAt\n \nstep\n \n8b,\n \nif\n \na\n \ndatabase\n \nerror\n \nor\n \nother\n \nsystem\n \nerror\n \noccurs\n \nwhile\n \nattempting\n \nto\n \nsave\n \nthe\n \nrequest:\n \n        \n-\n   \nSystem\n \nlogs\n \nthe\n \nerror.\n \n        \n-\n   \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser\n \n(e.g.,\n \n\"An\n \nunexpected\n \nerror\n \noccurred.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n        \n-\n   \nThe\n \nrequest\n \nis\n \nnot\n \nsaved.\n \nPriority:\n \nHigh\n \n \nFrequency\n \nof\n \nUse:\n \nModerate\n \nBusiness\n \nRules:\n \nBR-TR001-1,\n \nBR-TR001-2,\n \nBR-TR001-3,\n \nBR-TR001-4,\n \nBR-TR001-5,\n \nBR-TR001-6\n \nUse\n \nCase\n \nrelated:\n \n \nRelated\n \nUse\n \nCases:\n \n“View\n \nRequest\n \nHistory”\n \n(UC-MR002)\n \n,\n \n“View\n \nRequest\n \nList”\n \n(UC-SR002),\n \n“Send\n \nRequest”(\n \nUC-SR001)\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nCreateTrainingRequestPage\n \nAssumptions:\n \n \nThe\n \napproval\n \nworkflow\n \n(Manager,\n \nDirector,\n \nCEO)\n \nis\n \ndefined\n \nelsewhere.\n \n \n \nTable\n \n13.\n \nBusiness\n \nrule\n \nof\n \nuse\n \ncase:\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR1\n \nUnique\n \nRequest\n \nID\n \nEach\n \ntraining\n \nrequest\n \nmust\n \nhave\n \na\n \nunique,\n \nsystem-generated\n \n`request_id`.\n \nThis\n \nID\n \nshould\n \nnot\n \nbe\n \neditable\n \nby\n \nthe\n \nuser.\n                                                     \n \nBR2\n \n \nMandatory\n \nFields\n \n'Training\n \nCategory'\n \nand\n \n'Reason\n \nfor\n \nTraining'\n \nare\n \nmandatory\n \nfields.\n \nForm\n \nsubmission\n \nshould\n \nbe\n \nblocked\n \nif\n \nthese\n \nfields\n \nare\n \nempty.\n\nBR3\n \nReason\n \nLength\n \nThe\n \n'Reason\n \nfor\n \nTraining'\n \nfield\n \nshould\n \nhave\n \na\n \ndefined\n \nmaximum\n \ncharacter\n \nlimit\n \n(e.g.,\n \n1000\n \ncharacters).\n \nBR4\n \nInitial\n \nStatus\n \nAll\n \nnew\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nStaff\n \nmust\n \ndefault\n \nto\n \na\n \n`Pending_Manager`\n \nstatus.\n \nBR5\n \nValid\n \nTraining\n \nCategory\n \nThe\n \n'Training\n \nCategory'\n \nselected\n \nmust\n \nbe\n \nfrom\n \na\n \npredefined\n \nlist\n \nof\n \nvalid\n \ncategories\n \nmanaged\n \nby\n \nthe\n \nsystem.\n \nBR6\n \nDocument\n \nUpload\n \n(Optional)\n \nIf\n \nsupporting\n \ndocuments\n \nare\n \nuploaded,\n \nthey\n \nmust\n \nadhere\n \nto\n \nspecified\n \nfile\n \ntype\n \nand\n \nsize\n \nlimits\n \n(e.g.,\n \nPDF,\n \nDOCX,\n \nmax\n \n5MB).\n \n \n1.1.2.\n \nUse\n \nCase:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \n \nTable\n \n14.\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-SR002\n \nUse\n \nCase\n \nName:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nPrimary\n \nActor:\n \n \nStaff\n \nSecondary\n \nActors:\n \nSystem\n \n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nis\n \nsuccessfully\n \npresented\n \nwith\n \nan\n \naccurate\n \nand\n \nup-to-date\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nsubmitted,\n \ndisplaying\n \nkey\n \ndetails\n \nsuch\n \nas\n \nRequest\n \nID,\n \nTraining\n \nCategory ,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \nIf\n \nno\n \nrequests\n \nhave\n \nbeen\n \nsubmitted,\n \nan\n \nappropriate\n \nmessage\n \nis\n \ndisplayed\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nview\n \na\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npreviously\n \nsubmitted,\n \nalong\n \nwith\n \ntheir\n \ncurrent\n \nstatus\n \nand\n \nkey\n \ndetails.\n \nPreconditions:\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \nthe\n \nrole/permission\n \nto\n \nview\n \ntheir\n \nown\n \ntraining\n \nrequests.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nStaff\n \nuser\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nof\n \ntheir\n \nsubmitted\n \ntraining\n \nrequests,\n \nincluding\n \ndetails\n \nlike\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \n \n-\n \n**Failure\n \n(No\n \nRequests):**\n \nIf\n \nthe\n \nuser\n \nhas\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequests\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n\"The\n \nStaff\n \nuser\n \nis\n \nsuccessfully\n \npresented\n \nwith\n \nan\n \naccurate\n \nand\n \nup-to-date\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nsubmitted,\n \ndisplaying\n \nkey\n \ndetails\n \nsuch\n \nas\n \nRequest\n \nID,\n \nTraining\n \nCategory ,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \nIf\n \nno\n \nrequests\n \nhave\n \nbeen\n \nsubmitted,\n \nan\n \nappropriate\n \nmessage\n \nis\n \ndisplayed.\n \nNormal\n \nFlow:\n \n1.\n \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC_1:\n \nLogin\n \nSystem).\n \n \n2.\n \nStaff\n \nuser\n \nnavigates\n \nto\n \ntheir\n \ndashboard."}, {"heading_number": "section_0_", "title": "", "Content": "Reason\n \nLength\n \nThe\n \n'Reason\n \nfor\n \nTraining'\n \nfield\n \nshould\n \nhave\n \na\n \ndefined\n \nmaximum\n \ncharacter\n \nlimit\n \n(e.g.,\n \n1000\n \ncharacters).\n \nBR4\n \nInitial\n \nStatus\n \nAll\n \nnew\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nStaff\n \nmust\n \ndefault\n \nto\n \na\n \n`Pending_Manager`\n \nstatus.\n \nBR5\n \nValid\n \nTraining\n \nCategory\n \nThe\n \n'Training\n \nCategory'\n \nselected\n \nmust\n \nbe\n \nfrom\n \na\n \npredefined\n \nlist\n \nof\n \nvalid\n \ncategories\n \nmanaged\n \nby\n \nthe\n \nsystem.\n \nBR6\n \nDocument\n \nUpload\n \n(Optional)\n \nIf\n \nsupporting\n \ndocuments\n \nare\n \nuploaded,\n \nthey\n \nmust\n \nadhere\n \nto\n \nspecified\n \nfile\n \ntype\n \nand\n \nsize\n \nlimits\n \n(e.g.,\n \nPDF,\n \nDOCX,\n \nmax\n \n5MB).\n \n \n1.1.2.\n \nUse\n \nCase:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \n \nTable\n \n14.\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-SR002\n \nUse\n \nCase\n \nName:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nPrimary\n \nActor:\n \n \nStaff\n \nSecondary\n \nActors:\n \nSystem\n \n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nis\n \nsuccessfully\n \npresented\n \nwith\n \nan\n \naccurate\n \nand\n \nup-to-date\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nsubmitted,\n \ndisplaying\n \nkey\n \ndetails\n \nsuch\n \nas\n \nRequest\n \nID,\n \nTraining\n \nCategory ,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \nIf\n \nno\n \nrequests\n \nhave\n \nbeen\n \nsubmitted,\n \nan\n \nappropriate\n \nmessage\n \nis\n \ndisplayed\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nview\n \na\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npreviously\n \nsubmitted,\n \nalong\n \nwith\n \ntheir\n \ncurrent\n \nstatus\n \nand\n \nkey\n \ndetails.\n \nPreconditions:\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \nthe\n \nrole/permission\n \nto\n \nview\n \ntheir\n \nown\n \ntraining\n \nrequests.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nStaff\n \nuser\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nof\n \ntheir\n \nsubmitted\n \ntraining\n \nrequests,\n \nincluding\n \ndetails\n \nlike\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \n \n-\n \n**Failure\n \n(No\n \nRequests):**\n \nIf\n \nthe\n \nuser\n \nhas\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequests\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n\"The\n \nStaff\n \nuser\n \nis\n \nsuccessfully\n \npresented\n \nwith\n \nan\n \naccurate\n \nand\n \nup-to-date\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nsubmitted,\n \ndisplaying\n \nkey\n \ndetails\n \nsuch\n \nas\n \nRequest\n \nID,\n \nTraining\n \nCategory ,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \nIf\n \nno\n \nrequests\n \nhave\n \nbeen\n \nsubmitted,\n \nan\n \nappropriate\n \nmessage\n \nis\n \ndisplayed.\n \nNormal\n \nFlow:\n \n1.\n \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC_1:\n \nLogin\n \nSystem).\n \n \n2.\n \nStaff\n \nuser\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n\n3.\n \nStaff\n \nuser\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nMy\n \nTraining\n \nRequests'\n \n(or\n \nsimilar).\n \n \n4.\n \nSystem\n \nretrieves\n \nall\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nlogged-in\n \nStaff\n \nuser\n \nfrom\n \nthe\n \n`requests`\n \ntable\n \nwhere\n \n`request_type`\n \nis\n \n'Training'.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \nlist\n \nof\n \ntraining\n \nrequests,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \n(e.g.,\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nReason\n \n(or\n \nsnippet),\n \nSubmission\n \nDate,\n \nCurrent\n \nStatus).\n \n \n6.\n \nStaff\n \nuser\n \ncan\n \nview\n \nthe\n \nfull\n \ndetails\n \nof\n \na\n \nspecific\n \nrequest\n \nby\n \nclicking\n \non\n \nit\n \n(this\n \nmight\n \nnavigate\n \nto\n \na\n \ndetailed\n \nview\n \nscreen,\n \npotentially\n \ncovered\n \nby\n \nanother\n \nuse\n \ncase\n \nor\n \nan\n \nextension\n \nof\n \nthis\n \none).\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nTraining\n \nRequests\n \nFound**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"You\n \nhave\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests\n \nyet.\"\n \nExceptions:\n \n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nrequests,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nStaff’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain\n \nbefore\n \nproceeding.\n \n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nFrequent,\n \nas\n \nusers\n \nwill\n \ncheck\n \nthe\n \nstatus\n \nof\n \ntheir\n \nsubmitted\n \ntraining\n \nrequests.\n \n(This\n \naction\n \ncan\n \noccur\n \nseveral\n \ntimes\n \nper\n \nday\n \nper\n \nStaff\n \nuser.)\n \nBusiness\n \nRules:\n \nBR-SR002-1,\n \nBR-SR002-2,\n \nBR-SR002-3,\n \nBR-SR002-4,\n \nBR-SR002-5\n \nRelated\n \nUse\n \nCases:\n \n \nUC_1:\n \nLogin\n \nSystem,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nMyTrainingRequestsListPage\n \n(Staff)\n \nAssumptions:\n \n \n-\n \nThe\n \nStaff\n \nuser\n \ncan\n \nonly\n \nview\n \ntheir\n \nown\n \ntraining\n \nrequests.\n \n \n-\n \nThe\n \ndetailed\n \napproval\n \nworkflow\n \nstatuses\n \n(e.g.,\n \n`Pending_Manager`,\n \n`Pending_Director`)\n \nare\n \ndefined\n \nand\n \nconsistently\n \nused.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-SR002-\n1\n \nData\n \nDisplay\n \nThe\n \nlist\n \nof\n \ntraining\n \nrequests\n \nmust\n \ndisplay\n \nat\n \nleast:\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus\n \nfor\n \neach\n \nrequest."}, {"heading_number": "section_0_", "title": "", "Content": "Staff\n \nuser\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nMy\n \nTraining\n \nRequests'\n \n(or\n \nsimilar).\n \n \n4.\n \nSystem\n \nretrieves\n \nall\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nlogged-in\n \nStaff\n \nuser\n \nfrom\n \nthe\n \n`requests`\n \ntable\n \nwhere\n \n`request_type`\n \nis\n \n'Training'.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \nlist\n \nof\n \ntraining\n \nrequests,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \n(e.g.,\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nReason\n \n(or\n \nsnippet),\n \nSubmission\n \nDate,\n \nCurrent\n \nStatus).\n \n \n6.\n \nStaff\n \nuser\n \ncan\n \nview\n \nthe\n \nfull\n \ndetails\n \nof\n \na\n \nspecific\n \nrequest\n \nby\n \nclicking\n \non\n \nit\n \n(this\n \nmight\n \nnavigate\n \nto\n \na\n \ndetailed\n \nview\n \nscreen,\n \npotentially\n \ncovered\n \nby\n \nanother\n \nuse\n \ncase\n \nor\n \nan\n \nextension\n \nof\n \nthis\n \none).\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nTraining\n \nRequests\n \nFound**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \n<PERSON>\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"You\n \nhave\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests\n \nyet.\"\n \nExceptions:\n \n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nrequests,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nStaff’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain\n \nbefore\n \nproceeding.\n \n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nFrequent,\n \nas\n \nusers\n \nwill\n \ncheck\n \nthe\n \nstatus\n \nof\n \ntheir\n \nsubmitted\n \ntraining\n \nrequests.\n \n(This\n \naction\n \ncan\n \noccur\n \nseveral\n \ntimes\n \nper\n \nday\n \nper\n \nStaff\n \nuser.)\n \nBusiness\n \nRules:\n \nBR-SR002-1,\n \nBR-SR002-2,\n \nBR-SR002-3,\n \nBR-SR002-4,\n \nBR-SR002-5\n \nRelated\n \nUse\n \nCases:\n \n \nUC_1:\n \nLogin\n \nSystem,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nMyTrainingRequestsListPage\n \n(Staff)\n \nAssumptions:\n \n \n-\n \nThe\n \nStaff\n \nuser\n \ncan\n \nonly\n \nview\n \ntheir\n \nown\n \ntraining\n \nrequests.\n \n \n-\n \nThe\n \ndetailed\n \napproval\n \nworkflow\n \nstatuses\n \n(e.g.,\n \n`Pending_Manager`,\n \n`Pending_Director`)\n \nare\n \ndefined\n \nand\n \nconsistently\n \nused.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-SR002-\n1\n \nData\n \nDisplay\n \nThe\n \nlist\n \nof\n \ntraining\n \nrequests\n \nmust\n \ndisplay\n \nat\n \nleast:\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus\n \nfor\n \neach\n \nrequest.\n\nBR-SR002-\n2\n \nOrder\n \nof\n \nDisplay\n \nTraining\n \nrequests\n \nshould\n \nbe\n \ndisplayed\n \nin\n \na\n \ndefault\n \norder,\n \ne.g.,\n \nby\n \nmost\n \nrecent\n \nsubmission\n \ndate\n \nfirst\n \n(descending).\n \nOptions\n \nfor\n \nsorting\n \n(e.g.,\n \nby\n \nstatus,\n \nby\n \nsubmission\n \ndate)\n \nmight\n \nbe\n \nprovided.\n \nBR-SR002-\n3\n \nStatus\n \nClarity\n \nThe\n \nstatus\n \nof\n \neach\n \ntraining\n \nrequest\n \n(e.g.,\n \n`Pending_Manager`,\n \n`Pending_Director`,\n \n`Pending_CEO`,\n \n`Approved_CEO`,\n \n`Rejected_Manager`,\n \n`Rejected_Director`,\n \n`Rejected_CEO`,\n \n`Closed`)\n \nshould\n \nbe\n \nclearly\n \nand\n \nunambiguously\n \ndisplayed.\n \nBR-SR002-\n4\n \nAccess\n \nControl\n \nStaff\n \nusers\n \ncan\n \nonly\n \nview\n \ntraining\n \nrequests\n \nthat\n \nthey\n \nhave\n \npersonally\n \nsubmitted.\n \nThey\n \ncannot\n \nview\n \nrequests\n \nsubmitted\n \nby\n \nother\n \nstaff\n \nmembers.\n \nBR-SR002-\n5\n \nRead-Only\n \nView\n \nThe\n \ntraining\n \nrequest\n \nlist\n \nprovides\n \na\n \nread-only\n \nview\n \nof\n \nthe\n \nrequest\n \nstatus\n \nand\n \ndetails.\n \nStaff\n \nusers\n \ncannot\n \ndirectly\n \nmodify\n \nthe\n \nstatus\n \nor\n \ncore\n \ndetails\n \nof\n \na\n \nrequest\n \nfrom\n \nthis\n \nlist\n \nview.\n \n \n1.1.3\n \nUsecase\n \nUC006:\n \nManage\n \nRole\n \nChange\n \nRequests\n \n \nUse\n \nCase\n \nID\n \nUC-RC001\n \nUse\n \nCase\n \nName\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nPrimary\n \nActor\n \nStaff\n \nSecondary\n \nActors\n \nSystem,\n \nCEO\n \n(as\n \neventual\n \napprover)\n \nTrigger\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Create\n \nRole\n \nChange\n \nRequest'\n \nsection\n \nfrom\n \ntheir\n \nprofile\n \nor\n \ndashboard.\n \nDescription\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nsubmit\n \na\n \nrequest\n \nto\n \nchange\n \ntheir\n \ncurrent\n \nsystem\n \nrole\n \nto\n \na\n \ndifferent\n \nrole\n \n(e.g.,\n \nfrom\n \nStaff\n \nto\n \nManager).\n \nThe\n \nuser\n \nmust\n \nselect\n \nthe\n \ndesired\n \nnew\n \nrole\n \nand\n \nprovide\n \na\n \nreason\n \nfor\n \nthe\n \nrequest.\n \nThe\n \nsystem\n \nvalidates\n \nthe\n \nrequest\n \nand\n \nstores\n \nit\n \nwith\n \nan\n \ninitial\n \nstatus\n \nof\n \nPending_Approval\n \n(or\n \nPending_CEO_Approval).\n \nPreconditions\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession\n \n(references\n \nUC002:\n \nUser\n \nLogin).\n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \npermissions\n \nto\n \ncreate\n \nrole\n \nchange\n \nrequests.\n \n-\n \nThe\n \nsystem\n \nhas\n \na\n \ndefined\n \nlist\n \nof\n \ntarget\n \nroles.\n \n-\n \n(Optional)\n \nThe\n \nStaff\n \nuser\n \ndoes\n \nnot\n \ncurrently\n \nhave\n \nanother\n \nrole\n \nchange\n \nrequest\n \npending.\n \nPostconditions\n \n-\n \nSuccess:\n \nNew\n \nrole\n \nchange\n \nrequest\n \ncreated\n \nin\n \nrole_requests\n \nwith\n \nunique\n \nrequest_id,\n \nuser\n \ndetails,\n \nrequested\n \nrole,\n \nreason,\n \nand\n \nstatus\n \nset\n \nto\n \nPending_Approval.\n \n-\n \nFailure:\n \nValidation\n \nfails\n \nor\n \nsystem\n \nerror;\n \nrequest\n \nnot\n \nsaved\n \nand\n \nuser\n \ninformed.\n \nExpected\n \nResults\n \nStaff\n \nuser\n \nsuccessfully\n \nsubmits\n \na\n \nrole\n \nchange\n \nrequest\n \nwhich\n \nbecomes\n \nvisible\n \nto\n \nthe\n \ndesignated\n \napprover\n \n(CEO).\n\nNormal\n \nFlow\n \n1.\n \nStaff\n \nlogs\n \nin\n \n(UC002).\n \n2.\n \nNavigates\n \nto\n \ndashboard\n \nor\n \nprofile.\n \n3.\n \nSelects\n \n'Create\n \nRole\n \nChange\n \nRequest'.\n \n4.\n \nSystem\n \ndisplays\n \nform\n \nwith\n \nRequested\n \nRole\n \ndropdown\n \nand\n \nReason\n \ntextarea.\n \n5.\n \nStaff\n \nselects\n \nrole\n \nand\n \nenters\n \nreason.\n \n6.\n \nStaff\n \nsubmits\n \nform.\n \n7.\n \nSystem\n \nvalidates\n \ninput.\n \n8.\n \nIf\n \nvalid,\n \ngenerates\n \nrequest_id\n \nand\n \nsaves\n \nrequest.\n \n9.\n \nShows\n \nsuccess\n \nmessage\n \nand\n \noptionally\n \nredirects.\n \nAlternative\n \nFlows\n \n-\n \nAF1:\n \nInvalid\n \nData\n \nSubmission:\n \nShows\n \nvalidation\n \nerrors;\n \nform\n \nretains\n \ndata.\n \n-\n \nAF2:\n \nCancel\n \nRequest\n \nCreation:\n \nUser\n \ncancels\n \nor\n \nnavigates\n \naway;\n \ndata\n \ndiscarded;\n \nredirected\n \nback.\n \n-\n \nAF3:\n \nExisting\n \nPending\n \nRequest:\n \nSystem\n \nshows\n \nmessage\n \nthat\n \nuser\n \ncannot\n \ncreate\n \nnew\n \nrequest\n \nuntil\n \ncurrent\n \nis\n \nprocessed;\n \ndisables\n \ncreate\n \noption.\n \nExceptions\n \nE1:\n \nSystem\n \nError\n \nDuring\n \nSave:\n \nLogs\n \nerror,\n \nshows\n \ngeneric\n \nerror\n \nmessage,\n \nrequest\n \nnot\n \nsaved.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nLow\n \nto\n \nModerate\n \n(depends\n \non\n \norganizational\n \nchurn/growth)\n \nBusiness\n \nRules\n \n-\n \nBR-RC001-1:\n \nRequested\n \nRole\n \nmust\n \nbe\n \nfrom\n \npredefined\n \nlist.\n \n-\n \nBR-RC001-2:\n \nReason\n \nis\n \nmandatory\n \nwith\n \nlength\n \nlimits.\n \n-\n \nBR-RC001-3:\n \nInitial\n \nstatus\n \nis\n \nPending_Approval.\n \n-\n \nBR-RC001-4:\n \nCannot\n \ncreate\n \nnew\n \nrequest\n \nif\n \none\n \nis\n \npending.\n \n-\n \nBR-RC001-5:\n \nStaff\n \ncannot\n \napprove\n \ntheir\n \nown\n \nrequests.\n \nRelated\n \nUse\n \nCases\n \nUC002:\n \nUser\n \nLogin,\n \nUC004:\n \nView\n \nDashboard,\n \nUC-RC002:\n \nCEO\n \nManages\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nRelated\n \nRoleChangeRequestScreen_StaffView\n \n(\n \n-\n \nCreate\n \nNew\n \nRole\n \nChange\n \nRequest\n \nform)\n \nAssumptions\n \nList\n \nof\n \navailable\n \nroles\n \nis\n \nmanaged\n \nand\n \nup-to-date.\n \n \n1.1.4\n \nUseCase\n \nUC009:\n \nStaff\n \nViews\n \nOwn\n \nRole\n \nRequests\n \n \n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC009\n \n<br>\n \nUse\n \nCase\n \nName:\n \nStaff\n \nViews\n \nOwn\n \nRole\n \nRequests\n \nPrimary\n \nActor:\n \nStaff\n \nSecondary\n \nActors:\n \nSystem\n\nTrigger:\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \nsection\n \nfor\n \nviewing\n \ntheir\n \nsubmitted\n \nrole\n \nchange\n \nrequests,\n \ntypically\n \nfrom\n \ntheir\n \ndashboard\n \nor\n \nprofile.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nview\n \na\n \nlist\n \nof\n \nall\n \nrole\n \nchange\n \nrequests\n \nthey\n \nhave\n \npreviously\n \nsubmitted,\n \nalong\n \nwith\n \nthe\n \ncurrent\n \nstatus\n \nand\n \nany\n \nreviewer\n \ncomments\n \nfor\n \neach\n \nrequest.\n \nPreconditions:\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession\n \n(references\n \nUC002:\n \nUser\n \nLogin).\n \n<br>\n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \nsubmitted\n \nat\n \nleast\n \none\n \nrole\n \nchange\n \nrequest.\n \nPostconditions:\n \n-\n \nSuccess:\n \nThe\n \nStaff\n \nuser\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nof\n \ntheir\n \nsubmitted\n \nrole\n \nchange\n \nrequests,\n \nincluding\n \ndetails\n \nlike\n \nRequested\n \nRole,\n \nReason,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \n<br>\n \n-\n \nFailure\n \n(No\n \nRequests):\n \nIf\n \nthe\n \nuser\n \nhas\n \nnot\n \nsubmitted\n \nany\n \nrole\n \nchange\n \nrequests,\n \na\n \nmessage\n \nindicating\n \n\"You\n \ndon't\n \nhave\n \nany\n \nrole\n \nchange\n \nrequests.\"\n \n(or\n \nsimilar)\n \nis\n \ndisplayed.\n \nExpected\n \nResults:\n \nThe\n \nStaff\n \nuser\n \ncan\n \nsuccessfully\n \nview\n \nthe\n \nstatus\n \nand\n \ndetails\n \nof\n \nall\n \nrole\n \nchange\n \nrequests\n \nthey\n \nhave\n \ninitiated.\n\nNormal\n \nFlow:\n \n1.\n \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC002:\n \nUser\n \nLogin)\n \nand\n \nis\n \non\n \nStaffDashboardPage.\n \n<br>\n \n2.\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Role\n \nChange\n \nRequests'\n \nsection\n \n(e.g.,\n \nvia\n \na\n \nsidebar\n \nlink\n \non\n \nStaffDashboardPage).\n \n<br>\n \n3.\n \nThe\n \nsystem\n \ndisplays\n \nStaffRoleChangeRequestViewPage.\n \n<br>\n \n4.\n \nSystem\n \nretrieves\n \nand\n \ndisplays\n \nall\n \nrole\n \nchange\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nlogged-in\n \nStaff\n \nuser\n \nfrom\n \nthe\n \nrole_requests\n \ntable.\n \n<br>\n \n5.\n \nThe\n \nlist\n \nshows\n \nkey\n \ndetails\n \nfor\n \neach\n \nrequest:\n \nRequested\n \nRole,\n \nReason,\n \nSubmission\n \nDate,\n \nCurrent\n \nStatus,\n \nand\n \nany\n \nReviewer\n \nComments\n \nif\n \navailable.\n \nAlternative\n \nFlows:\n \nAF1:\n \nNo\n \nRole\n \nChange\n \nRequests\n \nFound\n \n<br>\n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrole\n \nchange\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \nthe\n \nmessage\n \n\"You\n \ndon't\n \nhave\n \nany\n \nrole\n \nchange\n \nrequests.\"\n \nwithin\n \nthe\n \nuser\n \nrequests\n \nsection\n \nof\n \nStaffRoleChangeRequestViewPage.\n \nExceptions:\n \nE1:\n \nSystem\n \nError:\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nrequests,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \nto\n \nthe\n \nuser.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nOccasionally,\n \nwhen\n \nstaff\n \nwant\n \nto\n \ncheck\n \nthe\n \nprogress\n \nof\n \ntheir\n \nrequests.\n \nBusiness\n \nRules:\n \n-\n \nBR-RC009-1:\n \nStaff\n \ncan\n \nonly\n \nview\n \nrole\n \nchange\n \nrequests\n \nthey\n \npersonally\n \nsubmitted.\n \n<br>\n \n-\n \nBR-RC009-2:\n \nThe\n \ndisplayed\n \nstatus\n \nshould\n \nbe\n \nclear\n \nand\n \nreflect\n \nthe\n \nlatest\n \nstate\n \nin\n \nthe\n \napproval\n \nworkflow.\n\nRelated\n \nUse\n \nCases:\n \nUC002:\n \nUser\n \nLogin,\n \nUC-RC001:\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nrelated:\n \nStaffDashboardPage,\n \nStaffRoleChangeRequestViewPage\n \nAssumptions:\n \n-\n \nThe\n \napproval\n \nworkflow\n \nstatuses\n \nfor\n \nrole\n \nchanges\n \nare\n \ndefined\n \nand\n \nconsistently\n \nused."}, {"heading_number": "1.2", "title": "1.2.Manager", "Content": "1.2.1.\n \nUse\n \nCase:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \n \nDiagram:\n \n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n\n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nand\n \nmanage\n \nrequests\n \nassigned\n \nto\n \nthem.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nhistory\n \nof\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"UpdateStatusPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nor\n \nmodal\n \nfor\n \nManagers\n \nto\n \nupdate\n \nthe\n \nstatus\n \nof\n \na\n \nrequest.\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \n/\n \nManage\n \nrequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nhistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"UpdateStatusPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \n'Change\n \nrequest\n \nstatus'\n \naction\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nManager\n \nuser.\"\n \n}\n \n \n \nUse\n \ncase\n \ndescription\n \nof\n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR001\n\nUse\n \nCase\n \nName:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem,\n \nStaff\n \n(as\n \ninitiator),\n \nDirector\n \n(as\n \nnext\n \napprover)\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \nreviews\n \na\n \npending\n \ntraining\n \nrequest\n \nand\n \ntakes\n \nappropriate\n \naction\n \n(Approve\n \nor\n \nReject).\n \nThe\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nin\n \nthe\n \nsystem\n \nto\n \nreflect\n \nthe\n \ndecision\n \n(\nPending_Director\n \nor\n \nRejected_Manager\n),\n \nrelevant\n \nnotifications\n \nare\n \npotentially\n \nsent,\n \nand\n \nan\n \naudit\n \ntrail\n \nof\n \nthe\n \nmanager's\n \naction\n \n(including\n \nany\n \ncomments)\n \nis\n \nrecorded.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nreview\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nStaff\n \nthat\n \nare\n \nawaiting\n \ntheir\n \napproval.\n \nThe\n \nManager\n \ncan\n \napprove\n \n(escalating\n \nto\n \nDirector)\n \nor\n \nreject\n \nthe\n \nrequest.\n \nThey\n \ncan\n \nalso\n \nadd\n \ncomments.\n \nPreconditions:\n \n-\n \nThe\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n<br>\n \n-\n \nThe\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \nmanage\n \ntraining\n \nrequests\n \nassigned\n \nto\n \nthem.\n \n \n-\n \nThere\n \nare\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nassigned\n \nor\n \nvisible\n \nto\n \nthe\n \nManager.\n \nPostconditions:\n \n-\n \n**On\n \nApproval:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Pending_Director`,\n \nand\n \na\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \nassigned\n \nDirector.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue\n \nfor\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \n**On\n \nRejection:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Rejected_Manager`.\n \nA\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nManager's\n \nactive\n \nqueue.\n \n \n-\n \n**Comments\n \nAdded:**\n \nAny\n \ncomments\n \nmade\n \nby\n \nthe\n \nManager\n \nare\n \nsaved\n \nwith\n \nthe\n \nrequest.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \ninto\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard\n \nand\n \nselects\n \n'Manage\n \nTraining\n \nRequests'\n \nor\n \nis\n \nnotified\n \nof\n \npending\n \nrequests.\n \n \n3.\n \nSystem\n \ndisplays\n \na\n \nlist\n \nof\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Manager`\n \nthat\n \nare\n \nassigned\n \nto\n \nor\n \nvisible\n \nto\n \nthis\n \nManager.\n \n \n4.\n \nManager\n \nselects\n \na\n \nspecific\n \ntraining\n \nrequest\n \nto\n \nreview.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \nfull\n \ndetails\n \nof\n \nthe\n \nselected\n \ntraining\n \nrequest\n \n(Category,\n \nReason,\n \nSubmitted\n \nby,\n \nSubmission\n \nDate,\n \nany\n \nattached\n \ndocuments,\n \nhistory\n \nof\n \ncomments).\n \n \n6.\n \nManager\n \nreviews\n \nthe\n \nrequest\n \ndetails.\n \n \n7.\n \nManager\n \ndecides\n \nto:\n\na.\n \n**Approve:**\n \nManager\n \nclicks\n \nthe\n \n'Approve'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments.\n \n \nb.\n \n**Reject:**\n \nManager\n \nclicks\n \nthe\n \n'Reject'\n \nbutton.\n \nManager\n \nmay\n \nadd\n \noptional\n \ncomments\n \n(often\n \nmandatory\n \nfor\n \nrejection).\n \n \n8.\n \n**If\n \nApprove\n \n(7a):**\n \n \na.\n \nSystem\n \nvalidates\n \nany\n \ncomments\n \n(if\n \napplicable\n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Pending_Director`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \napproval,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \nrelevant\n \nDirector\n \nand/or\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \napproved\n \nand\n \nforwarded\n \nto\n \nDirector.\"\n \n \n9.\n \n**If\n \nReject\n \n(7b):**\n \n \na.\n \nSystem\n \nvalidates\n \ncomments\n \n(if\n \napplicable,\n \ne.g.,\n \nensuring\n \nreason\n \nfor\n \nrejection\n \nis\n \nprovided).\n \n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Rejected_Manager`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nManager's\n \nrejection,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \noriginating\n \nStaff.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \nrejected.\"\n \n \n10.\n \nManager\n \ncan\n \nreturn\n \nto\n \nthe\n \nlist\n \nof\n \npending\n \nrequests\n \n(back\n \nto\n \nstep\n \n3)\n \nor\n \ntheir\n \ndashboard.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nTraining\n \nRequests\n \nFound**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \ntraining\n \nrequests\n \nsubmitted\n \nby\n \nthe\n \nStaff\n \nuser:\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"You\n \nhave\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests\n \nyet.\"\n \nExceptions:\n \n \n**AF1:\n \nRequest\n \nAlready\n \nProcessed:**\n \n \n-\n \nAt\n \nstep\n \n4,\n \nif\n \nthe\n \nselected\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \n`Pending_Manager`\n \nstatus\n \n(e.g.,\n \nprocessed\n \nby\n \nanother\n \nmanager\n \nor\n \nsystem\n \nupdate):\n \n \n-\n \nSystem\n \ndisplays\n \na\n \nmessage\n \n\"This\n \nrequest\n \nhas\n \nalready\n \nbeen\n \nprocessed\n \nor\n \nis\n \nno\n \nlonger\n \navailable\n \nfor\n \naction.\"\n \n \n-\n \nManager\n \nreturns\n \nto\n \nthe\n \nupdated\n \nlist\n \nof\n \nrequests.\n \n<br>\n \n**AF2:\n \nProvide\n \nComments\n \nOnly\n \n(No\n \nStatus\n \nChange):**\n \n \n-\n \nSome\n \nsystems\n \nmight\n \nallow\n \na\n \nmanager\n \nto\n \nadd\n \ncomments\n \nwithout\n \nimmediately\n \napproving/rejecting,\n \nkeeping\n \nit\n \nin\n \n`Pending_Manager`.\n \nIf\n \nthis\n \nis\n \na\n \nfeature:\n \n \n-\n \nManager\n \nadds\n \ncomments\n \nand\n \nclicks\n \n'Save\n \nComments'.\n\n-\n \nSystem\n \nsaves\n \ncomments\n \nand\n \ntimestamp.\n \nRequest\n \nremains\n \n`Pending_Manager`.\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \nDaily\n \nto\n \nWeekly,\n \ndepending\n \non\n \nvolume\n \nof\n \nrequests.\n \nBusiness\n \nRules:\n \nBR-SR002-1,\n \nBR-SR002-2,\n \nBR-SR002-3,\n \nBR-SR002-4,\n \nBR-SR002-5\n \nRelated\n \nUse\n \nCases:\n \n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n \n(Next\n \nstep\n \nin\n \nworkflow),\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nManage\n \nTraining\n \nRequests\n \nList\n \n(Manager),\n \nTraining\n \nRequest\n \nDetail\n \nView\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nManager\n \ncan\n \nonly\n \nact\n \non\n \nrequests\n \ncurrently\n \nin\n \n`Pending_Manager`\n \nstatus.\n \n \n-\n \nThe\n \nsystem\n \ncorrectly\n \nroutes\n \nrequests\n \nto\n \nthe\n \nappropriate\n \nManager\n \nbased\n \non\n \norganizational\n \nstructure\n \nor\n \nassignment\n \nrules.\n \n \n-\n \nNotification\n \nmechanisms\n \nare\n \nin\n \nplace.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR001-\n1\n \nActionable\n \nStatus\n \nanagers\n \ncan\n \nonly\n \napprove\n \nor\n \nreject\n \ntraining\n \nrequests\n \nthat\n \nare\n \nin\n \n`Pending_Manager`\n \nstatus\n \nand\n \nassigned/visible\n \nto\n \nthem.\n \nBR-MR001-\n2\n \nComment\n \non\n \naction\n \nComments\n \nmay\n \nbe\n \noptional\n \nfor\n \napproval\n \nbut\n \nshould\n \nbe\n \nstrongly\n \nencouraged\n \nor\n \nmandatory\n \nfor\n \nrejection\n \nto\n \nprovide\n \nfeedback\n \nto\n \nthe\n \nStaff.\n \nBR-MR001-\n3\n \nStatus\n \nTransition\n \non\n \nApproval\n \nUpon\n \nManager\n \napproval,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Pending_Director`.\n \nBR-MR001-\n4\n \nStatus\n \nTransition\n \non\n \nRejection\n \nUpon\n \nManager\n \nrejection,\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nmust\n \ntransition\n \nto\n \n`Rejected_Manager`.\n \nBR-MR001-\n5\n \n \nAudit\n \nTrail\n               \nAll\n \nactions\n \ntaken\n \nby\n \nthe\n \nManager\n \n(approval,\n \nrejection,\n \ncomments),\n \nalong\n \nwith\n \n`user_id`\n \nand\n \n`timestamp`,\n \nmust\n \nbe\n \nlogged\n \nin\n \nthe\n \nrequest's\n \nhistory\n \nfor\n \naudit\n \npurposes.\n \nBR-MR001-\n6\n  \n \nNotification\n \n(Workflow)\n       \n \nUpon\n \napproval,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \nrelevant\n \nDirector(s).\n \nUpon\n \nrejection,\n \na\n \nnotification\n \nshould\n \nbe\n \ntriggered\n \nto\n \nthe\n \noriginating\n \nStaff\n \nmember.\n \nBR-MR001-\n7\n  \n \nView\n \nPermissions\n    \nManagers\n \nshould\n \nonly\n \nsee\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nteam/department\n \nor\n \nthose\n \nexplicitly\n \nassigned\n \nto\n \nthem\n \nfor\n \napproval,\n \nunless\n \nthey\n \nhave\n \nbroader\n \nadministrative\n \nviewing\n \nrights.\n\n1.2.2.\n \nUC-MR002:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR002\n \nUse\n \nCase\n \nName:\n \nManager\n \nViews\n \nTraining\n \nRequest\n \nHistory\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \naccesses\n \nand\n \nviews\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department).\n \nThe\n \ndisplayed\n \nhistory\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort\n \nthe\n \ndata\n \nfor\n \neasier\n \nreview .\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nview\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department,\n \nor\n \nall\n \nrequests\n \nif\n \nthey\n \nhave\n \nsufficient\n \npermissions).\n \nThis\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments.\n \nPreconditions:\n \n-\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \naccess\n \ntraining\n \nrequest\n \nhistorical\n \nrecords.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nManager\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nor\n \ndetailed\n \nview\n \nof\n \nhistorical\n \ntraining\n \nrequests,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort.\n \n \n-\n \n**Failure\n \n(No\n \nHistory):**\n \nIf\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests\n \nare\n \nfound,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \ninto\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n \n<br>\n \n3.\n \nManager\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nTraining\n \nRequest\n \nHistory'.\n \n \n4.\n \nSystem\n \nretrieves\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \nrelevant\n \nto\n \nthe\n \nManager's\n \nscope\n \n(e.g.,\n \nrequests\n \nsubmitted\n \nby\n \ntheir\n \nteam,\n \nrequests\n \nthey\n \nmanaged).\n \nThis\n \nincludes\n \nrequest\n \ndetails,\n \nstatus\n \nchange\n \nhistory,\n \napprover/rejecter\n \ndetails,\n \ntimestamps,\n \nand\n \ncomments.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \ntraining\n \nrequest\n \nhistory,\n \ntypically\n \nin\n \na\n \nlist\n \nformat,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \nhistorical\n \nentry\n \nor\n \nrequest.\n \n \n6.\n \nManager\n \ncan\n \napply\n \nfilters\n \n(e.g.,\n \nby\n \nStaff\n \nmember,\n \ndate\n \nrange,\n \nstatus,\n \ntraining\n \ncategory)\n \nor\n \nsort\n \nthe\n \nhistory.\n\n7.\n \nManager\n \ncan\n \nselect\n \na\n \nspecific\n \nhistorical\n \nrequest\n \nto\n \nview\n \nits\n \ncomplete\n \naudit\n \ntrail\n \nand\n \ndetails.\n \n \n8.\n \nManager\n \ncan\n \nclose\n \nthe\n \nhistory\n \nview\n \nand\n \nreturn\n \nto\n \nthe\n \ndashboard\n \nor\n \nprevious\n \nscreen.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nHistorical\n \nData\n \nFound:**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests:\n \n \n4a.i.\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \navailable\n \nfor\n \nthe\n \nselected\n \ncriteria.\"\n \n \n**AF2:\n \nFiltering/Sorting\n \nApplied:**\n \n \n6a.\n \nManager\n \napplies\n \nfilters\n \nor\n \nsorting\n \ncriteria.\n \n \n6b.\n \nSystem\n \nre-queries\n \nand\n \ndisplays\n \nthe\n \nhistorical\n \ndata\n \naccording\n \nto\n \nthe\n \napplied\n \ncriteria.\n \nExceptions:\n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nhistory,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nManager’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nPeriodically,\n \nfor\n \nreview,\n \naudit,\n \nor\n \ntracking\n \npurposes.\n \nBusiness\n \nRules:\n \nBR-MR002-1,\n \nBR-MR002-2,\n \nBR-MR002-3,\n \nBR-MR002-4\n \nRelated\n \nUse\n \nCases:\n \n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nTraining\n \nRequest\n \nHistory\n \nScreen\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nscope\n \nof\n \nhistorical\n \ndata\n \nvisible\n \nto\n \na\n \nManager\n \nis\n \ndefined\n \nby\n \ntheir\n \nrole\n \nand\n \npermissions.\n \n \n-\n \nHistorical\n \ndata\n \nis\n \naccurately\n \nlogged\n \nand\n \nmaintained\n \nby\n \nthe\n \nsystem.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR002-\n1\n  \n \nData\n \nIntegrity\n \n&\n \nRead-Only\n \nHistorical\n \ntraining\n \nrequest\n \ndata\n \nmust\n \nbe\n \npresented\n \nas\n \nread-only.\n \nNo\n \nmodifications\n \nto\n \nhistorical\n \nrecords\n \nare\n \npermitted\n \nthrough\n \nthis\n \nview.\n \nBR-MR002-\n2\n \n \nScope\n \nof\n \nView\n         \nManagers\n \nshould\n \nonly\n \nbe\n \nable\n \nto\n \nview\n \nthe\n \nhistory\n \nof\n \ntraining\n \nrequests\n \nthat\n \nfall\n \nwithin\n \ntheir\n \ndefined\n \nscope\n \n(e.g.,\n \ntheir\n \ndirect\n\nreports,\n \ntheir\n \ndepartment,"}, {"heading_number": "1.3", "title": "1.3. Director", "Content": "they\n \nhave\n \npersonally\n \nactioned).\n \nBR-MR002-\n3\n \n \nComprehensive\n \nAudit\n \nTrail\n  \n \nFor\n \neach\n \nhistorical\n \ntraining\n \nrequest,\n \nthe\n \nview\n \nshould\n \nprovide\n \na\n \nclear\n \naudit\n \ntrail,\n \nincluding\n \nall\n \nstatus\n \nchanges,\n \nwho\n \nperformed\n \nthe\n \naction\n \n(Staff,\n \nManager,\n \nDirector,\n \nCEO),\n \ntimestamps,\n \nand\n \nany\n \ncomments\n \nmade.\n \nBR-MR002-\n4\n  \n \nFiltering\n \nand\n \nSorting\n \nThe\n \nsystem\n \nshould\n \nprovide\n \ncapabilities\n \nto\n \nfilter\n \nand\n \nsort\n \nthe\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \n(e.g.,\n \nby\n \ndate,\n \nstatus,\n \nemployee,\n \ntraining\n \ncategory)\n \nto\n \nfacilitate\n \neasier\n \nreview.\n \n \n1.3.\n \nDirector\n \n1.3.1.\n \nUse\n \nCase:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n \n \nDiagram:\n \n \nUse\n \ncase\n \ndescription\n \nof\n \nUC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n\nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-DR001\n \nUse\n \nCase\n \nName:\n \nDirector\n \nManages\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nDirector\n \nSecondary\n \nActors:\n \nSystem,\n \nManager\n \n(as\n \nprevious\n \napprover),\n \nStaff\n \n(as\n \ninitiator),\n \nCEO\n \n(as\n \nnext\n \napprover)\n \n \nTrigger:\n \nThe\n \nDirector\n \nsuccessfully\n \nreviews\n \na\n \ntraining\n \nrequest\n \npreviously\n \napproved\n \nby\n \na\n \nManager\n \n(status\n \nPending_Director\n)\n \nand\n \ntakes\n \nappropriate\n \naction\n \n(Approve\n \nfor\n \nCEO\n \nescalation\n \nor\n \nReject).\n \nThe\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nin\n \nthe\n \nsystem\n \n(\nPending_CEO\n \nor\n \nRejected_Director\n),\n \nnotifications\n \nare\n \npotentiall y\n \nsent,\n \nand\n \nan\n \naudit\n \ntrail\n \nof\n \nthe\n \nDirector's\n \naction\n \nis\n \nrecorded.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nDirector\n \nto\n \nreview\n \ntraining\n \nrequests\n \napproved\n \nby\n \na\n \nManager\n \nand\n \nawaiting\n \nDirector-level\n \napproval.\n \nThe\n \nDirector\n \ncan\n \napprove\n \n(escalating\n \nto\n \nCEO)\n \nor\n \nreject\n \nthe\n \nrequest.\n \nThey\n \ncan\n \nalso\n \nadd\n \ncomments.\n \nPreconditions:\n \n-\n \nThe\n \nDirector\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nThe\n \nDirector\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \nmanage\n \ntraining\n \nrequests\n \nat\n \ntheir\n \napproval\n \nlevel.\n \n \n-\n \nThere\n \nare\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Director`\n \nassigned\n \nor\n \nvisible\n \nto\n \nthe\n \nDirector.\n \nPostconditions:\n \n-\n \n**On\n \nApproval:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Pending_CEO`,\n \nand\n \na\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \nassigned\n \nCEO.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nDirector's\n \nactive\n \nqueue\n \nfor\n \n`Pending_Director`\n \nstatus.\n \n \n-\n \n**On\n \nRejection:**\n \nThe\n \ntraining\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nto\n \n`Rejected_Director`.\n \nA\n \nnotification\n \nmay\n \nbe\n \nsent\n \nto\n \nthe\n \noriginating\n \nStaff\n \nand/or\n \nthe\n \napproving\n \nManager.\n \nThe\n \nrequest\n \nis\n \nno\n \nlonger\n \nin\n \nthe\n \nDirector's\n \nactive\n \nqueue.\n \n \n-\n \n**Comments\n \nAdded:**\n \nAny\n \ncomments\n \nmade\n \nby\n \nthe\n \nDirector\n \nare\n \nsaved\n \nwith\n \nthe\n \nrequest.\n \nExpected\n \nResults\n \nThe\n \nregistered\n \nuser\n \n(Staf f,\n \nManager ,\n \nDirector ,\n \nor\n \nCEO)\n \nsuccessfully\n \nauthenticates\n \nwith\n \nvalid\n \ncredentials\n \nand\n \nis\n \nsecurely\n \nlogged\n \ninto\n \nthe\n \nRetailOnboardPro\n \nsystem.\n \nThe\n \nuser\n \nis\n \nthen\n \nredirected\n \nto\n \ntheir\n \nrole-specific\n \ndashboard,\n \ngaining\n \naccess\n \nto\n \nauthorized\n \nfunctionalities.\n \nNormal\n \nFlow:\n \n1.\n \nDirector\n \nlogs\n \ninto\n \nthe\n \nsystem.\n \n \n \n2.\n \nDirector\n \nnavigates\n \nto\n \ntheir\n \ndashboard\n \nand\n \nselects\n \n'Manage\n \nTraining\n \nRequests'\n \nor\n \nis\n \nnotified\n \nof\n \npending\n \nrequests.\n \n \n3.\n \nSystem\n \ndisplays\n \na\n \nlist\n \nof\n \ntraining\n \nrequests\n \nwith\n \nstatus\n \n`Pending_Director`\n \nthat\n \nare\n \nassigned\n \nto\n \nor\n \nvisible\n \nto\n \nthis\n \nDirector.\n \n \n4.\n \nDirector\n \nselects\n \na\n \nspecific\n \ntraining\n \nrequest\n \nto\n \nreview.\n\n5.\n \nSystem\n \ndisplays\n \nthe\n \nfull\n \ndetails\n \nof\n \nthe\n \nselected\n \ntraining\n \nrequest\n \n(Category,\n \nReason,\n \nSubmitted\n \nby,\n \nSubmission\n \nDate,\n \napproval\n \nhistory\n \nincluding\n \nManager's\n \ncomments,\n \nany\n \nattached\n \ndocuments).\n \n \n6.\n \nDirector\n \nreviews\n \nthe\n \nrequest\n \ndetails\n \nand\n \nprior\n \napprovals/comments.\n \n<br>\n \n7.\n \nDirector\n \ndecides\n \nto:\n \n \na.\n \n**Approve:**\n \nDirector\n \nclicks\n \nthe\n \n'Approve'\n \nbutton.\n \nDirector\n \nmay\n \nadd\n \noptional\n \ncomments.\n \n \nb.\n \n**Reject:**\n \nDirector\n \nclicks\n \nthe\n \n'Reject'\n \nbutton.\n \nDirector\n \nmay\n \nadd\n \noptional\n \ncomments\n \n(often\n \nmandatory\n \nfor\n \nrejection).\n \n \n8.\n \n**If\n \nApprove\n \n(7a):**\n \n \na.\n \nSystem\n \nvalidates\n \nany\n \ncomments\n \n(if\n \napplicable).\n \n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Pending_CEO`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nDirector's\n \napproval,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \nrelevant\n \nCEO\n \nand/or\n \nthe\n \noriginating\n \nStaff/Manager.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \napproved\n \nand\n \nforwarded\n \nto\n \nCEO.\"\n \n \n \n9.\n \n**If\n \nReject\n \n(7b):**\n \n \n    \na.\n \nSystem\n \nvalidates\n \ncomments\n \n(if\n \napplicable).\n \n \nb.\n \nSystem\n \nupdates\n \nthe\n \ntraining\n \nrequest\n \nstatus\n \nto\n \n`Rejected_Director`.\n \n \nc.\n \nSystem\n \nrecords\n \nthe\n \nDirector's\n \nrejection,\n \ntimestamp,\n \nand\n \nany\n \ncomments.\n \n \nd.\n \nSystem\n \npotentially\n \nnotifies\n \nthe\n \noriginating\n \nStaff\n \nand/or\n \nthe\n \napproving\n \nManager.\n \n \ne.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage:\n \n\"Training\n \nrequest\n \n[request_id]\n \nrejected.\"\n \n \n10.\n \nDirector\n \ncan\n \nreturn\n \nto\n \nthe\n \nlist\n \nof\n \npending\n \nrequests\n \n(back\n \nto\n \nstep\n \n3)\n \nor\n \ntheir\n \ndashboard.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nHistorical\n \nData\n \nFound:**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests:\n \n \n4a.i.\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \navailable\n \nfor\n \nthe\n \nselected\n \ncriteria.\"\n \n \n**AF2:\n \nFiltering/Sorting\n \nApplied:**\n \n \n6a.\n \nManager\n \napplies\n \nfilters\n \nor\n \nsorting\n \ncriteria.\n \n \n6b.\n \nSystem\n \nre-queries\n \nand\n \ndisplays\n \nthe\n \nhistorical\n \ndata\n \naccording\n \nto\n \nthe\n \napplied\n \ncriteria.\n\nExceptions:\n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nhistory,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nManager’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nPeriodically,\n \nfor\n \nreview,\n \naudit,\n \nor\n \ntracking\n \npurposes.\n \nBusiness\n \nRules:\n \nBR-MR002-1,\n \nBR-MR002-2,\n \nBR-MR002-3,\n \nBR-MR002-4\n \nRelated\n \nUse\n \nCases:\n \n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nTraining\n \nRequest\n \nHistory\n \nScreen\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nscope\n \nof\n \nhistorical\n \ndata\n \nvisible\n \nto\n \na\n \nManager\n \nis\n \ndefined\n \nby\n \ntheir\n \nrole\n \nand\n \npermissions.\n \n \n-\n \nHistorical\n \ndata\n \nis\n \naccurately\n \nlogged\n \nand\n \nmaintained\n \nby"}, {"heading_number": "1.4", "title": "1.4. CEO", "Content": "system.\n \n \n*Business\n \nRules\n \nof\n \nManager\n \nfeature:\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-MR002-\n1\n  \n \nData\n \nIntegrity\n \n&\n \nRead-Only\n \nHistorical\n \ntraining\n \nrequest\n \ndata\n \nmust\n \nbe\n \npresented\n \nas\n \nread-only.\n \nNo\n \nmodifications\n \nto\n \nhistorical\n \nrecords\n \nare\n \npermitted\n \nthrough\n \nthis\n \nview.\n \nBR-MR002-\n2\n \n \nScope\n \nof\n \nView\n         \nManagers\n \nshould\n \nonly\n \nbe\n \nable\n \nto\n \nview\n \nthe\n \nhistory\n \nof\n \ntraining\n \nrequests\n \nthat\n \nfall\n \nwithin\n \ntheir\n \ndefined\n \nscope\n \n(e.g.,\n \ntheir\n \ndirect\n \nreports,\n \ntheir\n \ndepartment,\n \nor\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nactioned).\n \nBR-MR002-\n3\n \n \nComprehensive\n \nAudit\n \nTrail\n  \n \nFor\n \neach\n \nhistorical\n \ntraining\n \nrequest,\n \nthe\n \nview\n \nshould\n \nprovide\n \na\n \nclear\n \naudit\n \ntrail,\n \nincluding\n \nall\n \nstatus\n \nchanges,\n \nwho\n \nperformed\n \nthe\n \naction\n \n(Staff,\n \nManager,\n \nDirector,\n \nCEO),\n \ntimestamps,\n \nand\n \nany\n \ncomments\n \nmade.\n \nBR-MR002-\n4\n  \n \nFiltering\n \nand\n \nSorting\n \nThe\n \nsystem\n \nshould\n \nprovide\n \ncapabilities\n \nto\n \nfilter\n \nand\n \nsort\n \nthe\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \n(e.g.,\n \nby\n \ndate,\n \nstatus,\n \nemployee,\n \ntraining\n \ncategory)\n \nto\n \nfacilitate\n \neasier\n \nreview.\n \n \n1.4.\n \nCEO\n \n \nDiagram:\n\n1.4.1\n \nUseCase:\n \nUC011:\n \nManage\n \nUser\n \nRoles\n \n \nUse\n \nCase\n \nID\n \nUC-RC002\n \nUse\n \nCase\n \nName\n \nCEO\n \nManages\n \n(Approves/Rejects)\n \nRole\n \nChange\n \nRequest\n \nPrimary\n \nActor\n \nCEO\n \nSecondary\n \nActors\n \nSystem,\n \nStaff\n \n(initiator)\n \nTrigger\n \n-\n \nA\n \nrole\n \nchange\n \nrequest\n \nwith\n \nstatus\n \nPending_Approval\n \n(or\n \nPending_CEO_Approval)\n \nappears\n \nin\n \nCEO's\n \nqueue.\n \n-\n \nCEO\n \nnavigates\n \nto\n \n'Approve\n \nRole\n \nChange\n \nRequests'\n \nsection\n \nfrom\n \ndashboard.\n \nDescription\n \nAllows\n \nCEO\n \nto\n \nreview\n \nrole\n \nchange\n \nrequests\n \nsubmitted\n \nby\n \nStaff,\n \nview\n \ndetails,\n \napprove\n \nor\n \nreject\n \nwith\n \noptional\n \ncomments,\n \nand\n \nupdate\n \nuser's\n \nrole\n \naccordingly.\n \nPreconditions\n \n-\n \nCEO\n \nis\n \nauthenticated\n \nwith\n \nactive\n \nsession\n \n(UC002).\n \n-\n \nCEO\n \nhas\n \npermission\n \nto\n \nmanage\n \nrole\n \nchange\n \nrequests.\n \n-\n \nThere\n \nare\n \npending\n \nrole\n \nchange\n \nrequests\n \nassigned\n \nor\n \nvisible\n \nto\n \nCEO.\n \nPostconditions\n \n-\n \nSuccess\n \n(Approval):\n \nUpdates\n \nrequest\n \nstatus\n \nto\n \nApproved,\n \nlogs\n \nCEO\n \nID,\n \ntimestamp,\n \ncomments;\n \nupdates\n \nuser's\n \nrole;\n \nnotifies\n \nstaff.\n \n-\n \nSuccess\n \n(Rejection):\n \nUpdates\n \nstatus\n \nto\n \nRejected,\n \nlogs\n \ndetails;\n \nnotifies\n \nstaff;\n \nrole\n \nremains\n \nunchanged.\n\n-\n \nFailure:\n \nValidation\n \nor\n \nsystem\n \nerrors,\n \nno\n \nchanges\n \nmade;\n \nCEO\n \ninformed.\n \nExpected\n \nResults\n \nThe\n \nCEO\n \nsuccessfully\n \nassigns,\n \nmodifies,\n \nor\n \nrevokes\n \nuser\n \nroles\n \nwithin\n \nthe\n \nsystem.\n \nThese\n \nchanges\n \nare\n \nimmediately\n \nreflected\n \nin\n \nthe\n \ntargeted\n \nusers'\n \npermissions\n \nand\n \naccess\n \ncapabilities,\n \nand\n \na\n \ncomprehensive\n \naudit\n \nlog\n \nof\n \nthese\n \nadministrative\n \nactions\n \nis\n \nmaintain"}, {"heading_number": "2", "title": "2. Common Functions", "Content": "1.\n \nCEO\n \nlog"}, {"heading_number": "2.1", "title": "2.1 Use Case: Login System", "Content": "in\n \nthe\n \ntargeted\n \nusers'\n \npermissions\n \nand\n \naccess\n \ncapabilities,\n \nand\n \na\n \ncomprehensive\n \naudit\n \nlog\n \nof\n \nthese\n \nadministrative\n \nactions\n \nis\n \nmaintained.\n \nNormal\n \nFlow\n \n1.\n \nCEO\n \nlogs\n \nin\n \n(UC002).\n \n2.\n \nNavigates\n \nto\n \ndashboard\n \nor\n \napproval\n \npage.\n \n3.\n \nViews\n \nlist\n \nof\n \npending\n \nrequests.\n \n4.\n \nSelects\n \nrequest\n \nto\n \nreview.\n \n5.\n \nReviews\n \ndetails.\n \n6a.\n \nApproves:\n \nvalidates\n \ncomments,\n \nupdates\n \nstatus,\n \nupdates\n \nrole,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n6b.\n \nRejects:\n \nrequires\n \ncomments,\n \nupdates\n \nstatus,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n7.\n \nRequest\n \nremoved\n \nfrom\n \npending\n \nlist.\n \n8.\n \nCEO\n \nreturns\n \nto\n \nlist\n \nor\n \ndashboard.\n \nAlternative\n \nFlows\n \n-\n \nAF1:\n \nNo\n \npending\n \nrequests:\n \nSystem\n \nshows\n \n\"No\n \nrole\n \nchange\n \nrequests\n \nawaiting\n \napproval.\"\n \n-\n \nAF2:\n \nRequest\n \nalready\n \nprocessed:\n \nSystem\n \ninforms\n \nCEO\n \nand\n \nrefreshes\n \nlist.\n \nExceptions\n \nE1:\n \nSystem\n \nError\n \nDuring\n \nUpdate:\n \nLogs\n \nerror,\n \nrollbacks\n \npartial\n \nchanges,\n \nshows\n \nerror\n \nmessage,\n \nno\n \nchanges\n \npersisted.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nLow\n \nto\n \nModerate\n \nBusiness\n \nRules\n \n-\n \nBR-RC002-1:\n \nCEO\n \nacts\n \nonly\n \non\n \npending\n \nrequests\n \nassigned/visible\n \nto\n \nthem.\n \n-\n \nBR-RC002-2:\n \nComments\n \nmandatory\n \nwhen\n \nrejecting.\n \n-\n \nBR-RC002-3:\n \nSystem\n \nmust\n \nupdate\n \nuser's\n \nrole\n \non\n \napproval.\n \n-\n \nBR-RC002-4:\n \nAll\n \nactions\n \nlogged\n \nwith\n \nactor,\n \ntimestamp,\n \ncomments.\n \n-\n \nBR-RC002-5:\n \nMulti-level\n \napproval\n \nworkflows\n \napply\n \n(CEO\n \nstep\n \nspecific).\n \nRelated\n \nUse\n \nCases\n \nUC002:\n \nUser\n \nLogin,\n \nUC004:\n \nView\n \nDashboard,\n \nUC-RC001:\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nRelated\n \nApprove\n \nRoleChangeRequestScreen_StaffView\n \n \nAssumptions\n \nCEO\n \nunderstands\n \nrole\n \nchange\n \nimplications;\n \nnotification\n \nmechanisms\n \nexist.\n \n \n \n \n2.\n \nCommon\n \nFunctions\n \n2.1\n \nUse\n \nCase:\n \nLogin\n \nSystem\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nUse\n \nCase\n \nDiagram\n \n-\n \nUnregistered\n \nUser\",\n \n  \n\"actors\":\n \n[\n \n    \n{\"name\":\n \n\"Unregistered\n \nUser\"}\n \n  \n],\n \n  \n\"useCases\":\n \n[\n\n{\"id\":\n \n\"UC001\",\n \n\"name\":\n \n\"Register\n \nNew\n \nAccount\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]},\n \n    \n{\"id\":\n \n\"UC002\",\n \n\"name\":\n \n\"User\n \nLogin\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]}\n \n  \n],\n \n  \n\"relationships\":\n \n[\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC001\"},\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC002\"}\n \n  \n]\n \n}\n \n \na.\n \nFunctional\n \nDescription\n \nTable\n \n15Use\n \ncase\n \ndescription\n \nof\n \nUC_1:\n \nLogin.\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC_1\n \nUse\n \nCase\n \nName:\n \nLogin\n \nPrimary\n \nActor:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO\n \n \nSecondary\n \nActors:\n \nSystem\n \nNone\n \nTrigger:\n \nA\n \nuser\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO)\n \ninitiates\n \nthe\n \nlogin\n \nprocess\n \nby\n \nnavigating\n \nto\n \nthe\n \nlogin\n \npage\n \nand\n \nentering\n \ntheir\n \ncredentials..\n \nDescription:\n \nThis\n \nuse\n \ncase\n \ndescribes\n \nthe\n \nprocess\n \nof\n \na\n \nregistered\n \nuser\n \nlogging\n \ninto\n \nthe\n \nRetailOnboardPro\n \nsystem\n \nusing\n \nvalid\n \ncredentials.\n \nThe\n \nsystem\n \nverifies\n \nthe\n \ncredentials\n \nand\n \ngrants\n \naccess\n \nto\n \nthe\n \nappropriate\n \ndashboard\n \nbased\n \non\n \nthe\n \nuser's\n \nrole.\n \n.\n \nPreconditions:\n \n-\n \nThe\n \nuser\n \nmust\n \nbe\n \na\n \nregistered\n \nand\n \nactive\n \nsystem\n \nuser\n \nwith\n \na\n \ndefined\n \nrole.\n \n \n-\n \nThe\n \nsystem\n \nmust\n \nbe\n \nonline\n \nand\n \nable\n \nto\n \nprocess\n \nauthentication\n \nrequests.\n \n \n-\n \nThe\n \nuser\n \nmust\n \nhave\n \nvalid\n \nlogin\n \ncredentials\n \n(username/email\n \nand\n \npassword).\n \n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nuser\n \nis\n \nauthenticated,\n \ntheir\n \nsession\n \nis\n \ninitiated,\n \nand\n \nthey\n \nare\n \nredirected\n \nto\n \ntheir\n \nrespective\n \ndashboard\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO).\n \n \n-\n \n**Failure:**\n \nIf\n \nauthentication\n \nfails,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nfailed\n \nattempt,\n \nnotifies\n \nthe\n \nuser,\n \nand\n \nthe\n \nuser\n \nremains\n \non\n \nthe\n \nlogin\n \npage.\n \nAccess\n \nis\n \ndenied.\n \n \nExpected\n \nResults\n\nNormal\n \nFlow\n \n1.\n \nUser\n \nnavigates\n \nto\n \nthe\n \nLoginPage.\n \n \n2.\n \nUser\n \nenters\n \ntheir\n \nusername\n \n(or\n \nemail)\n \nand\n \npassword\n \ninto\n \nthe\n \ndesignated\n \nfields.\n \n \n3.\n \nUser\n \nclicks\n \nthe\n \n'Login'\n \nbutton.\n \n \n4.\n \nSystem\n \nvalidates\n \nthe\n \nformat\n \nof\n \nthe\n \nentered\n \ncredentials\n \n(e.g.,\n \nnon-empty).\n \n \n5.\n \nSystem\n \nsecurely\n \nverifies\n \nthe\n \nsubmitted\n \ncredentials\n \nagainst\n \nthe\n \n`users`\n \ntable\n \nin\n \nthe\n \ndatabase.\n \n<br>\n \n6.\n \nIf\n \ncredentials\n \nare\n \nvalid:\n \n \n \na.\n \nSystem\n \nestablishes\n \nan\n \nauthenticated\n \nsession\n \nfor\n \nthe\n \nuser.\n \n \n \nb.\n \nSystem\n \nlogs\n \nthe\n \nsuccessful\n \nlogin\n \nattempt,\n \nincluding\n \nuser\n \nID\n \nand\n \ntimestamp.\n \n \n \nc.\n \nSystem\n \nredirects\n \nthe\n \nuser\n \nto\n \ntheir\n \nrole-specific\n \ndashboard.\n \n \nAlternative\n \nFlows:\n \n**AF1:\n \nInvalid\n \nCredentials**\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \ndo\n \nnot\n \nmatch\n \na\n \nregistered\n \nand\n \nactive\n \nuser:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nlogin\n \nattempt\n \n(e.g.,\n \nusername,\n \nIP\n \naddress,\n \ntimestamp).\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \non\n \nthe\n \nLoginPage\n \n(e.g.,\n \n\"Invalid\n \nusername\n \nor\n \npassword.\n \nPlease\n \ntry\n \nagain.\").\n \n \nc.\n \nUser\n \nremains\n \non\n \nthe\n \nLoginPage\n \n(returns\n \nto\n \nstep\n \n2).\n \n \n**AF2:\n \nAccount\n \nLocked\n \n(Optional\n \n-\n \nif\n \nimplemented)**\n \n-\n \nIf\n \nthe\n \nsystem\n \nimplements\n \naccount\n \nlocking\n \nafter\n \nmultiple\n \nfailed\n \nattempts:\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \nare\n \nvalid\n \nbut\n \nthe\n \naccount\n \nis\n \nlocked:\n \n \na.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Your\n \naccount\n \nis\n \ntemporarily\n \nlocked.\n \nPlease\n \ntry\n \nagain\n \nlater\n \nor\n \ncontact\n \nsupport.\").\n \n \n-\n \nIf\n \ncredentials\n \nare\n \ninvalid\n \nand\n \nthis\n \nattempt\n \ntriggers\n \nan\n \naccount\n \nlock:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nattempt\n \nand\n \nlocks\n \nthe\n \naccount.\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Invalid\n \ncredentials.\n \nYour\n \naccount\n \nhas\n \nbeen\n \ntemporarily\n \nlocked\n \ndue\n \nto\n \nmultiple\n \nfailed\n \nattempts.\").\n \n \nExceptions:\n \n \n**E1:\n \nSystem\n \nUnavailable**\n \n \n-\n \nIf\n \nthe\n \nauthentication\n \nservice\n \nor\n \ndatabase\n \nis\n \nunavailable\n \nat\n \nstep\n \n5:\n \n<br>\n        \na.\n \nSystem\n \nlogs\n \nthe\n \ncritical\n \nerror.\n \n \nb.\n \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Login\n \nservice\n \nis\n \ntemporarily\n \nunavailable.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \n \nMultiple\n \ntimes\n \nper\n \nday\n \nper\n \nuser.\n \n \nBusiness\n \nRules:\n \nBR-LOGIN-1,\n \nBR-LOGIN-2\n \n \nUse\n \ncase\n \nrelated\n \nUC-TR001,\n \nUC-SR002,\n \nUC-MR001,\n \nUC-MR002,\n \nUC-DR001,\n \nUC-DR002,\n \nUC-CR001,\n \nUC-CR002,\n \nUC_2\n \n(Manage\n \nUser\n \nProfile)\n \n \nScreen\n \nrelated\n \nLoginPage,\n \nStaffDashboardPage,\n \nManagerDashboardPage,\n \nDirectorDashboardPage,\n \nCEODashboardPage\n\nAssumptions:\n \n \n-\n \nPassword\n \nencryption\n \nand\n \nsecurity\n \npolicies\n \n(e.g.,\n \ncomplexity,\n \nexpiry)\n \nare\n \nenforced\n \nby\n \nthe\n \nsystem\n \nduring\n \nregistration\n \nand\n \nlogin.\n \n \n-\n \nSession\n \nmanagement\n \n(creation,\n \ntimeout,\n \ntermination)\n \nis\n \nhandled\n \nsecurely.\n \n \n \n \nb.\n \nBusiness"}, {"heading_number": "2.2", "title": "2.2. Use Case: Manage User Profile", "Content": "of\n \nuse\n \ncase\n \nUC_1:\n \nLogin\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-LOGIN-1\n \n \n \nValid\n \nCredentials\n \nRequired\n \nUsers\n \nmust\n \nenter\n \nvalid\n \nlogin\n \ncredentials\n \n(username/email\n \nand\n \npassword)\n \nto\n \naccess\n \nthe\n \nsystem\n \nBR-LOGIN-2\n \n \n \nRole-Based\n \nAccess\n \nControl\n \nUpon\n \nsuccessful\n \nlogin,\n \nthe\n \nsystem\n \ngrants\n \naccess\n \nto\n \nthe\n \nuser's\n \nrole-specific\n \ndashboard,\n \nensuring\n \nthat\n \nusers\n \ncan\n \nonly\n \naccess\n \nfeatures\n \nand\n \ndata\n \nauthorized\n \nfor\n \ntheir\n \nrole.\n \n \n \n \n2.2.\n \nUse\n \nCase:\n \nManage\n \nUser\n \nProfile\n \na.\n \nFunctional\n \nDescription\n \nTable\n \n15Use\n \ncase\n \ndescription\n \nof\n \nUC_2:\n \nManage\n \nuser\n \nprofile\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC_2\n \nUse\n \nCase\n \nName:\n \nManage\n \nUser\n \nProfile\n \nPrimary\n \nActor:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO\n \n(Authenticated\n \nUser)\n \n \n \nSecondary\n \nActors:\n \nSystem\n \nNone\n \nTrigger:\n \nAn\n \nauthenticated\n \nuser\n \nnavigates\n \nto\n \ntheir\n \n'Profile'\n \nor\n \n'Account\n \nSettings'\n \n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \nan\n \nauthenticated\n \nuser\n \nto\n \nview\n \nand\n \nupdate\n \ntheir\n \nown\n \nprofile\n \ninformation,\n \nsuch\n \nas\n \ncontact\n \ndetails\n \nand\n \npassword.\n \n \nPreconditions:\n \n-\n \nThe\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession\n \n(UC_1:\n \nLogin).\n \n \n \nPostconditions\n \n-\n \n**Success:**\n \nUser's\n \nprofile\n \ninformation\n \nis\n \nupdated\n \nin\n \nthe\n \n`users`\n \ntable.\n \nUser\n \nreceives\n \nconfirmation.\n \n \n-\n \n**Failure:**\n \nIf\n \nvalidation\n \nfails\n \nor\n \na\n \nsystem\n \nerror\n \noccurs,\n \nthe\n \nprofile\n \nis\n \nnot\n \nupdated,\n \nand\n \nthe\n \nuser\n \nis\n \ninformed\n \nof\n \nthe\n \nissue.\n\nExpected\n \nResults\n \nThe\n \nauthenticated\n \nuser\n \nsuccessfully\n \nviews\n \nand\n \nupdates\n \ntheir\n \neditable\n \nprofile\n \ninformation,\n \nsuch\n \nas\n \ncontact\n \ndetails\n \nor\n \npassword.\n \nThe\n \nchanges\n \nare\n \nvalidated\n \nand\n \nsecurely\n \nsaved\n \nto\n \nthe\n \nsystem,\n \nand\n \nthe\n \nuser\n \nreceives\n \nconfirmation\n \nof\n \nthe\n \nsuccessful\n \nupdate.\n \nNormal\n \nFlow\n \n1.\n \nUser\n \nnavigates\n \nto\n \ntheir\n \nprofile/account\n \nsettings\n \npage.\n \n \n2.\n \nSystem\n \ndisplays\n \nthe\n \nuser's\n \ncurrent\n \nprofile\n \ninformation\n \n(e.g.,\n \nname,\n \nemail,\n \nphone\n \n-\n \nsome\n \nfields\n \nmay\n \nbe\n \nread-only).\n \n \n3.\n \nUser\n \nmodifies\n \neditable\n \nfields\n \n(e.g.,\n \nphone\n \nnumber,\n \npassword).\n \n \n4.\n \nUser\n \nclicks\n \n'Save\n \nChanges'\n \nor\n \n'Update\n \nProfile'.\n \n \n5.\n \nSystem\n \nvalidates\n \nthe\n \nsubmitted\n \ndata\n \n(e.g.,\n \npassword\n \ncomplexity,\n \nemail\n \nformat).\n \n6.\n \nIf\n \nvalidation\n \nis\n \nsuccessful:\n \n \na.\n \nSystem\n \nupdates\n \nthe\n \nuser's\n \nrecord\n \nin\n \nthe\n \n`users`\n \ntable.\n \n \nb.\n \nSystem\n \nlogs\n \nthe\n \nprofile\n \nupdate\n \naction.\n \n \nc.\n \nSystem\n \ndisplays\n \na\n \nsuccess\n \nmessage\n \n(e.g.,\n \n\"Profile\n \nupdated\n \nsuccessfully.\").\n \n \n7.\n \nIf\n \nchanging\n \npassword,\n \nuser\n \nmight\n \nbe\n \nlogged\n \nout\n \nor\n \nasked\n \nto\n \nre-login.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nValidation\n \nError**\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \nvalidation\n \nfails:\n \n \na.\n \nSystem\n \ndisplays\n \nspecific\n \nerror\n \nmessages.\n \n \nb.\n \nUser\n \ncorrects\n \ndata\n \nand\n \nresubmits\n \n(returns\n \nto\n \nstep\n \n3).\n \n \n**AF2:\n \nCancel\n \nUpdate**\n \n \n-\n \nAt\n \nany\n \npoint\n \nbefore\n \nstep\n \n6,\n \nuser\n \nclicks\n \n'Cancel'.\n \n \na.\n \nSystem\n \ndiscards\n \nchanges.\n \nUser\n \nis\n \nreturned\n \nto\n \ndashboard\n \nor\n \nprevious\n \npage.\n \n \n \nExceptions:\n \n**E1:\n \nSystem\n \nError\n \nDuring\n \nSave**\n \n \n-\n \nAt\n \nstep\n \n6a,\n \nif\n \na\n \ndatabase\n \nerror\n \noccurs:\n \n \na.\n \nSystem\n \nlogs\n \nerror.\n \n \nb.\n \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nLow\n \n \nBusiness\n \nRules:\n \nBR-PROF-1,\n \nBR-PROF-2,\n \nBR-PROF-3\n \n(Detailed\n \nin\n \nTable\n \n27)\n \n \n \nUse\n \ncase\n \nrelated\n \nUC_1\n \nScreen\n \nrelated\n \nUser\n \nProfile\n \nScreen,\n \nAccount\n \nSettings\n \nScreen\n \n \n \nAssumptions:\n \n-\n \nCertain\n \nprofile\n \nfields\n \n(e.g.,\n \nusername,\n \nrole,\n \nemployee\n \nID)\n \nmay\n \nbe\n \nnon-editable\n \nby\n \nthe\n \nuser.\n\nb.\n \nBusiness\n \nRules\n \nTable\n \n16Business\n \nrule\n \nof\n \nuse\n \ncase\n \nUC_2:\n \nManage\n \nUser\n \nProfile\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-PROF-1\n \n \nOwn\n \nProfile\n \nModification\n \nUsers\n \ncan\n \nonly\n \nmodify\n \ntheir\n \nown\n \nprofile\n \ninformation.\n \nThey\n \ncannot\n \naccess\n \nor\n \nmodify\n \nother\n \nusers'\n \nprofiles\n \nunless\n \nthey\n \nhave\n \nspecific\n \nadministrative\n \nprivileges\n \n(out\n \nof\n \nscope\n \nfor\n \nthis\n \nUC).\n \n \n \nBR-PROF-2\n \n \nPassword\n \nChange\n \nSecurity\n \nIf\n \nchanging\n \nthe\n \npassword,\n \nthe\n \nuser\n \nmight\n \nbe\n \nrequired\n \nto\n \nenter\n \ntheir\n \ncurrent\n \npassword.\n \nNew\n \npasswords\n \nmust\n \nmeet\n \ndefined\n \ncompl"}, {"heading_number": "2.3", "title": "2.3 Use Case: View Dashboard", "Content": "Successful\n \npassword\n \nchange\n \nmight\n \ntrigger\n \na\n \nnotification\n \nemail.\n \n \nBR-PROF-3\n \nData\n \nValidation\n \nAll\n \neditable\n \nfields\n \nmust\n \nbe\n \nvalidated\n \naccording\n \nto\n \nsystem\n \nrules\n \n(e.g.,\n \nemail\n \nformat,\n \nphone\n \nnumber\n \nformat).\n \n \n \n2.3\n \nUse\n \nCase:\n \nView\n \nDashboard\n \n \nUC\n \nID\n \nand\n \nName\n \nUC004:\n \nView\n \nDashboard\n \nPrimary\n \nActor\n \nRegistered\n \nUser\n \n(<PERSON><PERSON><PERSON>,\n \nMANAGER,\n \nDIRECTOR,\n \nCEO)\n \nTrigger\n \nThe\n \nuser\n \nlogs\n \nin\n \nsuccessfully\n \nor\n \nnavigates\n \nto\n \nthe\n \ndashboard\n \nURL.\n \nDescription\n \nDisplays\n \nan\n \noverview\n \npage\n \nsummarizing\n \nrelevant\n \ndata\n \nbased\n \non\n \nthe\n \nuser's\n \nrole,\n \nincluding\n \ntraining\n \nrequest\n \nstats.\n \nPreconditions\n \nThe\n \nuser\n \nis\n \nauthenticated\n \nand\n \nauthorized.\n \nPostconditions\n \nThe\n \n<RoleSpecificDashboardPage>\n \nis\n \nrendered\n \nwith\n \nup-to-date\n \ninformation.\n \nExpected\n \nResults\n \n \nThe\n \nauthenticated\n \nuser\n \nsuccessfully\n \naccesses\n \ntheir\n \npersonalized\n \ndashboard,\n \nwhich\n \ndisplays\n \naccurate,\n \nup-to-date\n \nsummary\n \ninformation\n \nrelevant\n \nto\n \ntheir\n \nrole,\n \nincluding\n \nkey\n \nstatistics\n \n(e.g.,\n \ncounts\n \nof\n \npending,\n \napproved,\n \nand\n \nrejected\n \nrequests)\n \nand\n \nconvenient\n \nquick\n \nlinks\n \nto\n \ncore\n \nfunctionalities.\n \nNormal\n \nFlow\n \n1.\n \nUser\n \nlogs\n \nin\n \nor\n \nnavigates\n \nto\n \ndashboard.\n \n2.\n \nSystem\n \nfetches\n \nuser-specific\n \ndata.\n \n3.\n \nDashboard\n \nis\n \ndisplayed.\n \nAlternative\n \nFlows\n \nNone.\n \nExceptions\n \n-\n \nDatabase\n \nfailure\n \nmay\n \ncause\n \nincomplete\n \nor\n \nno\n \ndata\n \nto\n \nbe\n \ndisplayed.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nHigh"}, {"heading_number": "section_0_", "title": "", "Content": "b.\n \nBusiness\n \nRules\n \nTable\n \n16Business\n \nrule\n \nof\n \nuse\n \ncase\n \nUC_2:\n \nManage\n \nUser\n \nProfile\n \nID\n \nBusiness\n \nRule\n \nBusiness\n \nRule\n \nDescription\n \nBR-PROF-1\n \n \nOwn\n \nProfile\n \nModification\n \nUsers\n \ncan\n \nonly\n \nmodify\n \ntheir\n \nown\n \nprofile\n \ninformation.\n \nThey\n \ncannot\n \naccess\n \nor\n \nmodify\n \nother\n \nusers'\n \nprofiles\n \nunless\n \nthey\n \nhave\n \nspecific\n \nadministrative\n \nprivileges\n \n(out\n \nof\n \nscope\n \nfor\n \nthis\n \nUC).\n \n \n \nBR-PROF-2\n \n \nPassword\n \nChange\n \nSecurity\n \nIf\n \nchanging\n \nthe\n \npassword,\n \nthe\n \nuser\n \nmight\n \nbe\n \nrequired\n \nto\n \nenter\n \ntheir\n \ncurrent\n \npassword.\n \nNew\n \npasswords\n \nmust\n \nmeet\n \ndefined\n \ncomplexity\n \nand\n \nlength\n \nrequirements.\n \nSuccessful\n \npassword\n \nchange\n \nmight\n \ntrigger\n \na\n \nnotification\n \nemail.\n \n \nBR-PROF-3\n \nData\n \nValidation\n \nAll\n \neditable\n \nfields\n \nmust\n \nbe\n \nvalidated\n \naccording\n \nto\n \nsystem\n \nrules\n \n(e.g.,\n \nemail\n \nformat,\n \nphone\n \nnumber\n \nformat).\n \n \n \n2.3\n \nUse\n \nCase:\n \nView\n \nDashboard\n \n \nUC\n \nID\n \nand\n \nName\n \nUC004:\n \nView\n \nDashboard\n \nPrimary\n \nActor\n \nRegistered\n \nUser\n \n(<PERSON>AF<PERSON>,\n \nMANAGER,\n \nDIRECTOR,\n \nCEO)\n \nTrigger\n \nThe\n \nuser\n \nlogs\n \nin\n \nsuccessfully\n \nor\n \nnavigates\n \nto\n \nthe\n \ndashboard\n \nURL.\n \nDescription\n \nDisplays\n \nan\n \noverview\n \npage\n \nsummarizing\n \nrelevant\n \ndata\n \nbased\n \non\n \nthe\n \nuser's\n \nrole,\n \nincluding\n \ntraining\n \nrequest\n \nstats.\n \nPreconditions\n \nThe\n \nuser\n \nis\n \nauthenticated\n \nand\n \nauthorized.\n \nPostconditions\n \nThe\n \n<RoleSpecificDashboardPage>\n \nis\n \nrendered\n \nwith\n \nup-to-date\n \ninformation.\n \nExpected\n \nResults\n \n \nThe\n \nauthenticated\n \nuser\n \nsuccessfully\n \naccesses\n \ntheir\n \npersonalized\n \ndashboard,\n \nwhich\n \ndisplays\n \naccurate,\n \nup-to-date\n \nsummary\n \ninformation\n \nrelevant\n \nto\n \ntheir\n \nrole,\n \nincluding\n \nkey\n \nstatistics\n \n(e.g.,\n \ncounts\n \nof\n \npending,\n \napproved,\n \nand\n \nrejected\n \nrequests)\n \nand\n \nconvenient\n \nquick\n \nlinks\n \nto\n \ncore\n \nfunctionalities.\n \nNormal\n \nFlow\n \n1.\n \nUser\n \nlogs\n \nin\n \nor\n \nnavigates\n \nto\n \ndashboard.\n \n2.\n \nSystem\n \nfetches\n \nuser-specific\n \ndata.\n \n3.\n \nDashboard\n \nis\n \ndisplayed.\n \nAlternative\n \nFlows\n \nNone.\n \nExceptions\n \n-\n \nDatabase\n \nfailure\n \nmay\n \ncause\n \nincomplete\n \nor\n \nno\n \ndata\n \nto\n \nbe\n \ndisplayed.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nHigh\n\nBusiness\n \nRules\n \n-\n \nBR1:\n \nDashboard\n \ncontent\n \nvaries\n \nby\n \nuser\n \nrole.\n \n-\n \nBR2:\n \nOnly\n \ndata\n \nthe\n \nuser\n \nis\n \nauthorized\n \nto\n \nsee\n \nis\n \nshown.\n \nRelated\n \nUse\n \nCases\n \nUC_1:\n \nUser\n \nLogin,\n \nUC005:\n \nManage\n \nTraining\n \nRequests\n \nScreen\n \nRelated\n \n<RoleSpecificDashboardPage>\n \n(dashboard.html)\n \n \nAssumptions\n \nThe\n \nsystem\n \naccurately\n \ntracks\n \nrequest\n \nstatuses.\n \n \n2.4\n \nUseCase:\n \nLogout\n \n \n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC003\n \n<br>\n \nUse\n \nCase\n \nName:\n \nUser\n \nLogout\n \nPrimary\n \nActor:\n \nRegistered\n \nUser\n \n(Staff,\n \nManager,\n \nDirector,\n \nCEO)\n \nSecondary\n \nActors:\n \nSystem\n \nTrigger:\n \nAn\n \nauthenticated\n \nuser\n \nclicks\n \na\n \n'Logout'\n \nbutton\n \nor\n \nlink\n \navailable\n \non\n \nan\n \nauthenticated\n \npage\n \n(e.g.,\n \ndashboard,\n \nprofile\n \npage).\n \nDescription:\n \nThis\n \nuse\n \ncase\n \ndescribes\n \nthe\n \nprocess\n \nof\n \nan\n \nauthenticated\n \nuser\n \nsecurely\n \nlogging\n \nout\n \nof\n \nthe\n \nRetailOnboardPro\n \nsystem.\n \nThis\n \nterminates\n \ntheir\n \ncurrent\n \nsession\n \nand\n \nrevokes\n \naccess\n \nto\n \nauthenticated\n \nfunctionalities.\n\nPreconditions:\n \n-\n \nThe\n \nuser\n \nis\n \ncurrently\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession\n \nin\n \nthe\n \nsystem.\n \nPostconditions:\n \n-\n \nSuccess:\n \nThe\n \nuser's\n \nsession\n \nis\n \ninvalidated.\n \nThe\n \nuser\n \nis\n \nredirected\n \nto\n \nthe\n \nLoginPage.\n \nThe\n \nuser\n \nno\n \nlonger\n \nhas\n \naccess\n \nto\n \nrole-specific\n \nfunctionalities\n \nuntil\n \nthey\n \nlog\n \nin\n \nagain.\n \n<br>\n \n-\n \nFailure:\n \n(Typically,\n \nlogout\n \nfailures\n \nare\n \nrare\n \nunless\n \nthere's\n \na\n \nsevere\n \nsystem/network\n \nissue).\n \nIf\n \nsession\n \ninvalidation\n \nfails\n \non\n \nthe\n \nserver-side,\n \nthe\n \nuser\n \nmight\n \nstill\n \nappear\n \nlogged\n \nout\n \non\n \nthe\n \nclient\n \nbut\n \nthe\n \nsession\n \nmight\n \nnot\n \nbe\n \nfully\n \nterminated\n \n(a\n \nsecurity\n \nrisk).\n \nExpected\n \nResults:\n \nThe\n \nuser\n \nis\n \nsuccessfully\n \nlogged\n \nout,\n \ntheir\n \nsession\n \nis\n \nterminated,\n \nand\n \nthey\n \nare\n \nreturned\n \nto\n \nthe\n \nLoginPage.\n\nNormal\n \nFlow:\n \n1.\n \nUser\n \nis\n \non\n \nan\n \nAnyAuthenticatedPage\n \n(e.g.,\n \nStaffDashboardPage,\n \nProfilePage).\n \n<br>\n \n2.\n \nUser\n \nclicks\n \nthe\n \n'Logout'\n \nbutton/link.\n \n<br>\n \n3.\n \nSystem\n \nreceives\n \nthe\n \nlogout\n \nrequest.\n \n<br>\n \n4.\n \nSystem\n \ninvalidates\n \nthe\n \nuser's\n \ncurrent\n \nsession\n \non\n \nthe\n \nserver.\n \n<br>\n \n5.\n \nSystem\n \nclears\n \nany\n \nclient-side\n \nsession\n \nidentifiers\n \n(e.g.,\n \ncookies).\n \n<br>\n \n6.\n \nSystem\n \nredirects\n \nthe\n \nuser\n \nto\n \nthe\n \nLoginPage.\n \nAlternative\n \nFlows:\n \nNone\n \ntypically\n \napplicable\n \nfor\n \na\n \nstandard\n \nlogout.\n \nExceptions:\n \nE1:\n \nSystem\n \nError\n \nDuring\n \nLogout:\n \nIf\n \na\n \nsystem\n \nerror\n \noccurs\n \nwhile\n \ntrying\n \nto\n \ninvalidate\n \nthe\n \nsession,\n \nthe\n \nsystem\n \nshould\n \nlog\n \nthe\n \nerror.\n \nThe\n \nuser\n \nmight\n \nstill\n \nbe\n \nredirected\n \nto\n \nthe\n \nLoginPage\n \nas\n \na\n \nbest\n \neffort.\n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \nFrequent,\n \nat\n \nthe\n \nend\n \nof\n \nuser\n \nactivity\n \nsessions.\n\nBusiness\n \nRules:\n \n-\n \nBR-LOGOUT-1:\n \nUpon\n \nlogout,\n \nthe\n \nuser's\n \nsession\n \nmust\n \nbe\n \nsecurely\n \nand\n \ncompletely\n \ninvalidated.\n \n<br>\n \n-\n \nBR-LOGOUT-2:\n \nUser\n \nmust\n \nbe\n \nredirected\n \nto\n \na\n \nnon-authenticated\n \npage\n \n(typically\n \nLoginPage)\n \nafter\n \nlogout.\n \nRelated\n \nUse\n \nCases:\n \nUC002:\n \nUser\n \nLogin\n \nScreen\n \nrelated:\n \nAnyAuthenticatedPage\n \n→\n \nLoginPage\n \nAssumptions:\n \n-\n \nSecure\n \nsession\n \nmanagement\n \nmechanisms\n \nare\n \nin\n \nplace."}, {"heading_number": "III", "title": "III. Design Specifications", "Content": ""}, {"heading_number": "1", "title": "1. Screen design:", "Content": ""}, {"heading_number": "1.1", "title": "1.1 Screen: Homepage", "Content": "1.1\n \nScreen:\n \nHomepage\n \n \n{\n \n  \n\"screenName\":\n \n\"Homepage\"\n,\n\n\"title\"\n:\n \n\"RetailOnboardPro\n \n-\n \nH\nệ\n \nth\nố\nng\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"SingleColumnWithHeaderFooter\"\n,\n \n    \n\"header\"\n:\n \n{\n \n      \n\"logo\"\n:\n \n\"RetailOnboardPro\"\n,\n \n      \n\"navLinks\"\n:\n \n[\n\"T<PERSON>h\n \nnăng\"\n,\n \n\"Quy\n \ntrình\"\n,\n \n\"V\nề\n \nchúng\n \ntôi\"\n,\n \n\"H\nỗ\n \ntr\nợ\n\"\n],\n \n      \n\"actions\"\n:\n \n[\n \n        \n{\n\"id\"\n:\n \n\"loginButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n},\n \n        \n{\"id\":\n \n\"registerButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n \n      \n]\n \n    \n},\n \n    \n\"mainContent\":\n \n{\n \n      \n\"heroSection\"\n:\n \n{\n \n        \n\"title\"\n:\n \n\"H\nệ\n \nth\nố\nng\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n        \n\"subtitle\"\n:\n \n\"Qu\nả\nn\n \nlý\n \nvà\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\n<PERSON>\nu\n \nđào\n \nt\nạ\no\n \nnhân\n \nviên\n \nm\nộ\nt\n \ncách\n \nhi\nệ\nu\n \nqu\nả\n...\"\n,\n \n        \n\"ctaButtons\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"heroLoginButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n},\n \n          \n{\"id\":\n \n\"heroRegisterButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n \n        \n],\n \n        \n\"image\":\n \n\"decorative_image_of_flowchart\"\n \n      \n},\n \n      \n\"featuresSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"Tính\n \nnăng\n \nchính\"\n,\n \n        \n\"items\"\n:\n \n[\n \n          \n{\n\"icon\"\n:\n \n\"approval_process_icon\"\n,\n \n\"title\"\n:\n \n\"Quy\n \ntrình\n \nphê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"request_management_icon\"\n,\n \n\"title\"\n:\n \n\"Qu\nả\nn\n \nlý\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"role_upgrade_icon\"\n,\n \n\"title\"\n:\n \n\"Yêu\n \nc\nầ\nu\n \nnâng\n \nc\nấ\np\n \nvai\n \ntrò\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n\n{\"\nicon\n\":\n \n\"permissions_icon\"\n,\n \n\"title\"\n:\n \n\"Phân\n \nquy\nề\nn\n \nrõ\n \nràng\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"dashboard_icon\"\n,\n \n\"title\"\n:\n \n\"Dashboard\n \nth\nố\nng\n \nkê\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"history_icon\"\n,\n \n\"title\"\n:\n \n\"L\nị\nch\n \ns\nử\n \nphê\n \nduy\nệ\nt\"\n,\n \n\"description\"\n:\n \n\"...\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"workflowSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"Quy\n \ntrình\n \nlàm\n \nvi\nệ\nc\"\n,\n \n        \n\"steps\"\n:\n \n[\n \n          \n{\n\"stepNumber\"\n:\n \n1\n,\n \n\"title\"\n:\n \n\"T\nạ\no\n \nyêu\n \nc\nầ\nu\"\n,\n \n\"actor\"\n:\n \n\"Nhân\n \nviên\n \n(Staff)\"\n},\n \n          \n{\"stepNumber\":\n \n2\n,\n \n\"title\"\n:\n \n\"Manager\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"Qu\nả\nn\n \nlý\n \n(Manager)\"\n},\n \n          \n{\"stepNumber\":\n \n3\n,\n \n\"title\"\n:\n \n\"Director\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"Giám\n \nđ\nố\nc\n \n(Director)\"\n},\n \n          \n{\"stepNumber\":\n \n4\n,\n \n\"title\"\n:\n \n\"CEO\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"CEO\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"aboutSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"V\nề\n \nRetailOnboardPro\"\n,\n \n        \n\"text\"\n:\n \n\"Chúng\n \ntôi\n \nhi\nể\nu\n \nr\nằ\nng\n \nvi\nệ\nc\n \nqu\nả\nn\n \nlý\n \nvà\n \nphê\n \nduy\nệ\nt...\"\n \n      \n}\n \n    \n},\n \n    \n\"\nfooter\n\":\n \n{\n \n      \n\"logo\"\n:\n \n\"RetailOnboardPro\"\n,\n \n      \n\"links\"\n:\n \n{\n \n        \n\"features\"\n:\n \n[\n\"Qu\nả\nn\n \nlý\n \nyêu\n \nc\nầ\nu\"\n,\n \n\"Phê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np\"\n,\n \n\"Thông\n \nkê\n \nbáo\n \ncáo\"\n],\n \n        \n\"support\"\n:\n \n[\n\"H\nướ\nng\n \nd\nẫ\nn\n \ns\nử\n \nd\nụ\nng\"\n,\n \n\"FAQ\"\n,\n \n\"Liên\n \nh\nệ\n\"\n]\n \n      \n},\n \n      \n\"copyright\":\n \n\"©\n \n2024\n \nRetailOnboardPro.\n \nAll\n \nrights\n \nreserved.\"\n,\n \n      \n\"cta\"\n:\n \n{\n\"id\"\n:\n \n\"footerRegisterButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n\n}\n \n  \n}\n \n}\n \n \n \nElement"}, {"heading_number": "1.2", "title": "1.2 Screen: LoginPage & RegisterPage", "Content": "Selector\n \nPurpose\n \nDescription\n \nWelcome\n \nHeading\n \nh1\n \nWelcomes\n \nthe\n \nuser\n \nto\n \nthe\n \nplatform.\n \n\"Welcome\n \nto\n \nRetailOnboardPro\"\n \nGo\n \nto\n \nDashboard\n \nButton\n \na\n \n(href=\"/dashboar\nd\")\n \nProvides\n \na\n \nquick\n \nlink\n \nto\n \nthe\n \nmain\n \ndashboard.\n \nStyled\n \nas\n \na\n \nprominent\n \ncall-to-action\n \nbutton.\n \nFeature\n \nCard\n \n(Training\n \nRequests)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nTraining\n \nRequests\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nFeature\n \nCard\n \n(Role\n \nManagement)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nRole\n \nManagement\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nFeature\n \nCard\n \n(User\n \nAdmin)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nUser\n \nAdministration\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nMain\n \nFooter\n \nfooter.bg-gray-80\n0\n \nDisplays\n \ncopyright\n \nand\n \nsecondary\n \nlinks.\n \nContains\n \ncopyright\n \ninfo,\n \nlinks\n \nto\n \nPrivacy\n \nPolicy,\n \nTerms\n \nof\n \nService,\n \nContact.\n \n \n1.2\n \nScreen:\n \nLoginPage\n \n&\n \nRegisterPage\n \nThis\n \nscreen\n \nallows\n \nusers\n \nto\n \nbe\n \nauthenticated\n \nto\n \nthe\n \nsystem\n \nscreens/functionalities.\n \nRelated\n \nuse\n \ncases:\n \n· \n \n \n \n \n \n \n \nUI\n \nDesign\n\n{\n \n  \n\"screenName\"\n:\n \n\"LoginPage\"\n,\n \n  \n\"title\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"CenteredCard\"\n \n  \n},\n \n  \n\"components\"\n:\n \n[\n \n    \n{\n \n      \n\"type\"\n:\n \n\"Card\"\n,\n \n      \n\"id\"\n:\n \n\"loginCard\"\n,\n \n      \n\"header\"\n:\n \n{\n \n        \n\"icon\"\n:\n \n\"app_logo_icon\"\n,\n \n        \n\"title\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n\n\"subtitle\"\n:\n \n\"Chào\n \nm\nừ\nng\n \ntr\nở\n \nl\nạ\ni\n \nv\nớ\ni\n \nRetailOnboardPro\"\n \n      \n},\n \n      \n\"form\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"loginForm\"\n,\n \n        \n\"action\"\n:\n \n\"/login\"\n,\n \n        \n\"method\"\n:\n \n\"POST\"\n,\n \n        \n\"fields\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"username\"\n,\n \n\"label\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\"\n,\n \n\"placeholder\"\n:\n \n\"Nh\nậ\np\n \ntên\n \nđăng\n \nnh\nậ\np\"\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"password\"\n,\n \n\"label\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\"\n,\n \n\"placeholder\"\n:\n \n\"Nh\nậ\np\n \nm\nậ\nt\n \nkh\nẩ\nu\"\n},\n \n          \n{\n\"type\"\n:\n \n\"Checkbox\"\n,\n \n\"id\"\n:\n \n\"rememberMe\"\n,\n \n\"label\"\n:\n \n\"Ghi\n \nnh\nớ\n \nđăng\n \nnh\nậ\np\"\n}\n \n        \n],\n \n        \n\"actions\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"SubmitButton\"\n,\n \n\"id\"\n:\n \n\"submitLogin\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC002_UserLogin\"\n}\n \n        \n],\n \n        \n\"links\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"registerLink\"\n,\n \n\"text\"\n:\n \n\"Ch\nư\na\n \ncó\n \ntài\n \nkho\nả\nn?\n \nĐăng\n \nký\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n},\n \n          \n{\n\"id\"\n:\n \n\"forgotPasswordLink\"\n,\n \n\"text\"\n:\n \n\"(Forgot\n \nPassword?)\"\n,\n \n\"navigatesTo\"\n:\n \n\"ResetPasswordScreen\"\n}\n \n//\n \nAssuming\n \n        \n]\n \n      \n},\n \n      \n\"footerLink\"\n:\n \n{\n\"id\"\n:\n \n\"backToHomeLink\"\n,\n \n\"text\"\n:\n \n\"←\n \nV\nề\n \ntrang\n \nch\nủ\n\"\n,\n \n\"navigatesTo\"\n:\n \n\"Homepage\"\n}\n \n    \n},\n \n    \n{\n \n      \n\"type\"\n:\n \n\"ErrorMessage\"\n,\n \n\"id\"\n:\n \n\"loginError\"\n,\n \n\"condition\"\n:\n \n\"loginFailed\"\n,\n \n\"text\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\n \nho\nặ\nc\n \nm\nậ\nt\n \nkh\nẩ\nu\n \nkhông\n \nđúng!\"\n \n    \n}\n \n  \n]\n \n}\n \nScreen\n \n1:\n \nScreen\n \nLogin\n\nTable\n \n17Element\n \ndescription\n \nof\n \nthe\n \nLoginPage\n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nLogin\n \nCard\n \nContainer\n \ndiv.login-ca\nrd\n \nMain\n \ncontainer\n \nfor\n \nlogin\n \nform\n \nand\n \nrelated\n \nelements\n \nStyled\n \ncard\n \nfor\n \nfocused\n \nlogin\n \nexperience\n \nLogin\n \nError\n \nMessage\n \ndiv\n \n(th:if=\"${pa\nram.error}\")\n \nDisplays\n \nerror\n \nif\n \nlogin\n \nfails\n \nMessage:\n \n\"Tên\n \nđăng\n \nnhập\n \nhoặc\n \nmật\n \nkhẩu\n \nkhông\n \nđúng!\"\n \nLogin\n \nForm\n \n(Dedicated\n \nPage)\n \nform\n \n(action=\"@\n{/login}\")\n \nAllows\n \nexisting\n \nusers\n \nto\n \nlog\n \nin\n \nFields:\n \nUsername,\n \nPassword,\n \nRemember\n \nme;\n \nlike\n \nindex.html\n \nform\n \nRegister\n \nLink\n \n(Login\n \nPage)\n \na\n \n(th:href=\"@\n{/register}\")\n \nNavigates\n \nto\n \nregistration\n \npage\n \nText:\n \n\"Đăng\n \nký\n \nngay\"\n \n \n \n.Screen\n \n2:\n \nRegister\n\n{\n \n  \n\"screenName\"\n:\n \n\"RegistrationPage\"\n,\n \n  \n\"title\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"CenteredCard\"\n \n  \n},\n \n  \n\"components\"\n:\n \n[\n \n    \n{\n \n      \n\"type\"\n:\n \n\"Card\"\n,\n \n      \n\"id\"\n:\n \n\"registerCard\"\n,\n \n      \n\"header\"\n:\n \n{\n \n        \n\"icon\"\n:\n \n\"add_user_icon\"\n,\n \n        \n\"title\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n        \n\"subtitle\"\n:\n \n\"T\nạ\no\n \ntài\n \nkho\nả\nn\n \nm\nớ\ni\n \nv\nớ\ni\n \nquy\nề\nn\n \nSTAFF\"\n\n},\n \n      \n\"form\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"registerForm\"\n,\n \n        \n\"action\"\n:\n \n\"/register\"\n,\n \n        \n\"method\"\n:\n \n\"POST\"\n,\n \n        \n\"fields\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"fullName\"\n,\n \n\"label\"\n:\n \n\"H\nọ\n \nvà\n \ntên\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"username\"\n,\n \n\"label\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputEmail\"\n,\n \n\"id\"\n:\n \n\"email\"\n,\n \n\"label\"\n:\n \n\"Email\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"password\"\n,\n \n\"label\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n,\n \n\"helperText\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\n \nph\nả\ni\n \ncó\n \nít\n \nnh\nấ\nt\n \n8\n \nký\n \nt\nự\n\"\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"confirmPassword\"\n,\n \n\"label\"\n:\n \n\"Xác\n \nnh\nậ\nn\n \nm\nậ\nt\n \nkh\nẩ\nu\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n}\n \n        \n],\n \n        \n\"infoBox\"\n:\n \n{\n \n          \n\"text\"\n:\n \n\"L\nư\nu\n \ný:\n \nTài\n \nkho\nả\nn\n \nm\nớ\ni\n \ns\nẽ\n \nđ\nượ\nc\n \nt\nạ\no\n \nv\nớ\ni\n \nquy\nề\nn\n \nSTAFF.\n \nĐ\nể\n \nthay\n \nđ\nổ\ni\n \nquy\nề\nn\n \nh\nạ\nn,\n \nb\nạ\nn\n \nc\nầ\nn\n \ng\nử\ni\n \nyêu\n \nc\nầ\nu\n \nthay\n \nđ\nổ\ni\n \nvai\n \ntrò\n \nsau\n \nkhi\n \nđăng\n \nnh\nậ\np.\"\n \n        \n},\n \n        \n\"consent\"\n:\n \n{\n\"type\"\n:\n \n\"Checkbox\"\n,\n \n\"id\"\n:\n \n\"termsConsent\"\n,\n \n\"label\"\n:\n \n\"Tôi\n \nđ\nồ\nng\n \ný\n \nv\nớ\ni\n \nđi\nề\nu\n \nkho\nả\nn\n \ns\nử\n \nd\nụ\nng\n \nvà\n \nchính\n \nsách\n \nb\nả\no\n \nm\nậ\nt\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n        \n\"actions\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"SubmitButton\"\n,\n \n\"id\"\n:\n \n\"submitRegistration\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC001_RegisterNewAccount\"\n}\n \n        \n],\n \n        \n\"links\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"loginLink\"\n,\n \n\"text\"\n:\n \n\"Đã\n \ncó\n \ntài\n \nkho\nả\nn?\n \nĐăng\n \nnh\nậ\np\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"footerLink\"\n:\n \n{\n\"id\"\n:\n \n\"backToHomeLink\"\n,\n \n\"text\"\n:\n \n\"←\n \nV\nề\n \ntrang\n \nch\nủ\n\"\n,\n \n\"navigatesTo\"\n:\n \n\"Homepage\"\n}\n \n    \n}\n \n  \n]\n\n}\n \n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nRegister\n \nCard\n \nContainer\n \ndiv."}, {"heading_number": "1.3", "title": "1.3 Screen: DashBoard", "Content": "container\n \nfor\n \nthe\n \nregistration\n \nform.\n \n \nRegistration\n \nForm\n \n(Dedicated)\n \nform#registerF\norm\n \nAllows\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount\n \n(defaults\n \nto\n \nSTAFF).\n \nFields:\n \nFull\n \nName,\n \nUsername,\n \nEmail,\n \nPassword,\n \nConfirm\n \nPassword.\n \nIncludes\n \npassword\n \nstrength\n \nindicator.\n \nRegister\n \nFull\n \nName\n \nInput\n \ninput#name\n \nField\n \nfor\n \nnew\n \nuser's\n \nfull\n \nname.\n \n \nRegister\n \nUsername\n \nInput\n \ninput#usernam\ne\n \nField\n \nfor\n \nnew\n \nuser's\n \nusername.\n \n \nRegister\n \nEmail\n \nInput\n \ninput#email\n \nField\n \nfor\n \nnew\n \nuser's\n \nemail.\n \n \nRegister\n \nPassword\n \nInput\n \ninput#password\n \nField\n \nfor\n \nnew\n \nuser's\n \npassword.\n \nMinlength\n \n8,\n \nwith\n \nstrength\n \nchecker.\n \nPassword\n \nStrength\n \nIndicator\n \ndiv#passwordS\ntrength\n \nVisual\n \nfeedback\n \non\n \nthe\n \nstrength\n \nof\n \nthe\n \nentered\n \npassword.\n \nChanges\n \nclass\n \nbased\n \non\n \npassword\n \ncomplexity.\n \nConfirm\n \nPassword\n \nInput\n \n(Register)\n \ninput#confirmP\nassword\n \nField\n \nto\n \nconfirm\n \nthe\n \nnew\n \npassword.\n \nValidated\n \nagainst\n \nthe\n \npassword\n \nfield.\n \nAgree\n \nto\n \nTerms\n \nCheckbox\n \ninput#agreeTer\nms\n \nCheckbox\n \nfor\n \nusers\n \nto\n \nagree\n \nto\n \nterms\n \nand\n \nconditions.\n \nRequired\n \nfor\n \nregistration.\n \nLogin\n \nLink\n \n(Register\n \nPage)\n \na\n \n(th:href=\"@{/lo\ngin}\")\n \nNavigates\n \nto\n \nthe\n \nlogin\n \npage.\n \n\"Đăng\n \nnhập\n \nngay\"\n \n \n \n \n1.3\n \nScreen:\n \nDashBoard\n \nUI\n \nDesign\n\n(\nStaffDashboardPage\n)\n \n \n(\nManagerDashboardPage\n)\n \n \n(\nDirectorDashboardPage\n)\n\n(\nCEODashboardPage\n)\n \n \n{\n \n  \n\"screenName\"\n:\n \n\"DashboardScreen\"\n,\n \n  \n\"title\"\n:\n \n\"Dashboard\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"AppLayoutWithSidebar\"\n,\n \n    \n\"header\"\n:\n \n{\n \n      \n\"appName\"\n:\n \n\"RetailOnboardPro\"\n,\n\n\"userProfile\"\n:\n \n{\n\"id\"\n:\n \n\"userProfileDropdown\"\n,\n \n\"userName\"\n:\n \n\"[User's\n \nName]\"\n,\n \n\"userRole\"\n:\n \n\"[User's\n \nRole]\"\n,\n \n\"actions\"\n:\n \n[\n\"View\n \nProfile\"\n,\n \n\"Logout\"\n]}\n \n    \n},\n \n    \n\"sidebar\"\n:\n \n{\n \n      \n\"id\"\n:\n \n\"appSidebar\"\n,\n \n      \n\"navLinks\"\n:\n \n[\n \n//\n \nConditional\n \nbased\n \non\n \nrole\n \n        \n{\n\"id\"\n:\n \n\"dashboardLink\"\n,\n \n\"text\"\n:\n \n\"Dashboard\"\n,\n \n\"icon\"\n:\n \n\"dashboard_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"DashboardScreen\"\n},\n \n        \n{\n\"id\"\n:\n \n\"trainingRequestsLink\"\n,\n \n\"text\"\n:\n \n\"Training\n \nRequests\"\n,\n \n\"icon\"\n:\n \n\"training_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestListScreen_Training\"\n},\n \n        \n{\n\"id\"\n:\n \n\"createTrainingRequestLink\"\n,\n \n\"text\"\n:\n \n\"Create\n \nNew\n \nRequest\"\n,\n \n\"icon\"\n:\n \n\"add_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"CreateTrainingRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n},\n \n        \n{\n\"id\"\n:\n \n\"roleChangeRequestsLink\"\n,\n \n\"text\"\n:\n \n\"Role\n \nChange\n \nRequests\"\n,\n \n\"icon\"\n:\n \n\"role_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"RoleChangeRequestScreen_StaffView\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n},\n \n        \n{\n\"id\"\n:\n \n\"approveRoleChangesLink\"\n,\n \n\"text\"\n:\n \n\"Approve\n \nRole\n \nChanges\"\n,\n \n\"icon\"\n:\n \n\"approve_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"ApproveRoleChangeRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==CEO_OR_DIRECTOR\"\n},\n \n        \n{\n\"id\"\n:\n \n\"userManagementLink\"\n,\n \n\"text\"\n:\n \n\"User\n \nManagement\"\n,\n \n\"icon\"\n:\n \n\"users_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"UserManagementScreen\"\n,\n \n\"condition\"\n:\n \n\"role==CEO_OR_ADMIN\"\n},\n \n        \n{\n\"id\"\n:\n \n\"profileLink\"\n,\n \n\"text\"\n:\n \n\"Profile\"\n,\n \n\"icon\"\n:\n \n\"profile_icon\"\n,\n \n\"navigatesTo\"\n:\n \n\"ProfileScreen\"\n},\n \n        \n{\n\"id\"\n:\n \n\"logoutLink\"\n,\n \n\"text\"\n:\n \n\"Logout\"\n,\n \n\"icon\"\n:\n \n\"logout_icon\"\n,\n \n\"action\"\n:\n \n\"LOGOUT\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC003_UserLogout\"\n}\n \n      \n]\n \n    \n},\n \n    \n\"mainContent\"\n:\n \n{\n \n      \n\"pageHeader\"\n:\n \n{\n\"title\"\n:\n \n\"Dashboard\"\n,\n \n\"roleBadge\"\n:\n \n\"[User's\n \nRole]\"\n,\n \n\"actions\"\n:\n \n[{\n\"id\"\n:\n \n\"createNewButton_Training\"\n,\n \n\"text\"\n:\n \n\"Create\n \nNew\"\n,\n \n\"navigatesTo\"\n:\n \n\"CreateTrainingRequestScreen\"\n,\n \n\"condition\"\n:\n \n\"role==STAFF\"\n}]},\n \n      \n\"statsCards\"\n:\n \n[\n \n        \n{\n\"id\"\n:\n \n\"pendingStats\"\n,\n \n\"title\"\n:\n \n\"Pending\"\n,\n \n\"value\"\n:\n \n\"[stats.pending]\"\n,\n \n\"icon\"\n:\n \n\"pending_icon\"\n},\n \n        \n{\n\"id\"\n:\n \n\"approvedStats\"\n,\n \n\"title\"\n:\n \n\"Approved\"\n,\n \n\"value\"\n:\n \n\"[stats.approved]\"\n,\n \n\"icon\"\n:\n \n\"approved_icon\"\n},\n \n        \n{\n\"id\"\n:\n \n\"rejectedStats\"\n,\n \n\"title\"\n:\n \n\"Rejected\"\n,\n \n\"value\"\n:\n \n\"[stats.rejected]\"\n,\n \n\"icon\"\n:\n \n\"rejected_icon\"\n}\n\n],\n \n      \n\"infoPanel\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"systemInfoPanel\"\n,\n \n        \n\"title\"\n:\n \n\"V\nề\n \nh\nệ\n \nth\nố\nng\n \nRetailOnboardPro\"\n,\n \n        \n\"sections\"\n:\n \n[\n \n          \n{\n\"title\"\n:\n \n\"Ch\nứ\nc\n \nnăng\n \nchính\"\n,\n \n\"items\"\n:\n \n[\n\"Training\n \nRequests:\n \n...\"\n,\n \n\"Quy\n \ntrình\n \nphê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np:\n \n...\"\n]},\n \n          \n{\n\"title\"\n:\n \n\"Ch\nứ\nc\n \nnăng\n \nph\nụ\n\"\n,\n \n\"items\"\n:\n \n[\n\"Role\n \nRequests:\n \n...\"\n,\n \n\"Qu\nả\nn\n \nlý\n \nng\nườ\ni\n \ndùng:\n \n...\"\n]}\n \n        \n]\n \n      \n},\n \n      \n\"recentRequestsSection\"\n:\n \n{\n \n        \n\"title\"\n:\n \n\"Recent\n \nTraining\n \nRequests\n \n(Core\n \nFeature)\"\n,\n \n        \n\"actions\"\n:\n \n[{\n\"id\"\n:\n \n\"viewAllRequestsButton\"\n,\n \n\"text\"\n:\n \n\"View\n \nAll\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestListScreen_Training\"\n}],\n \n        \n\"list\"\n:\n \n{\n \n          \n\"id\"\n:\n \n\"recentRequestsList\"\n,\n \n          \n\"itemTemplate\"\n:\n \n{\n \n            \n\"title\"\n:\n \n\"[request.title]\n \n-\n \n[request.category]\"\n,\n \n            \n\"description\"\n:\n \n\"[request.shortDescriptionOrReason]\"\n,\n \n            \n\"metadata\"\n:\n \n\"[request.submissionDate]\n \n|\n \n[request.submitterRole]\"\n,\n \n            \n\"status\"\n:\n \n\"[request.status]\"\n,\n \n            \n\"viewAction\"\n:\n \n{\n\"id\"\n:\n \n\"viewRequestDetailButton\"\n,\n \n\"text\"\n:\n \n\"View\"\n,\n \n\"navigatesTo\"\n:\n \n\"RequestDetailScreen_Training\"\n,\n \n\"params\"\n:\n \n{\n\"requestId\"\n:\n \n\"[request.id]\"\n}}\n \n          \n},\n \n          \n\"dataSource\"\n:\n \n\"recentTrainingRequests\"\n \n        \n}\n \n      \n}\n \n    \n}\n \n  \n}\n \n}\n\nTable\n \n18:\n \nElement\n \ndescription\n \nof\n \nthe\n \nDashboar d\n \nscreen\n \nof\n \nStaff\n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nDashboard\n \nRole\n \nBadge\n \nspan.badge.bg-pr\nimary\n \nDisplays\n \nthe\n \ncurrent\n \nuser's\n \nrole.\n \nsec:authentication=\"principal.role\"\n \nPending\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(pending)\n \nShows\n \ncount\n \nof\n \npending\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.pending}\"),\n \nand\n \ntitle.\n \nApproved\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(approved)\n \nShows\n \ncount\n \nof\n \napproved\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.approved}\"),\n \nand\n \ntitle.\n \nRejected\n \nRequests\n \nStat\n \nCard\n \ndiv.status-card\n \n(rejected)\n \nShows\n \ncount\n \nof\n \nrejected\n \ntraining\n \nrequests.\n \nContains\n \nicon,\n \ncount\n \n(th:text=\"${stats.rejected}\"),\n \nand\n \ntitle.\n \nSystem\n \nFeatures\n \nInfo\n \nPanel\n \ndiv.alert.alert-inf\no\n \nProvides\n \noverview\n \nof\n \nsystem\n \nmain\n \nfunctionalities.\n \nMentions\n \nTraining\n \nRequests\n \nand\n \nRole\n \nRequests.\n \nRecent\n \nTraining\n \nRequests\n \nSection\n \ndiv.card\n \nLists\n \nrecent\n \ntraining\n \nrequests\n \nfor\n \nquick\n \naccess.\n \nIncludes\n \n\"Create\n \nNew\"\n \n(for\n \nStaff)\n \nand\n \n\"View\n \nAll\"\n \nbuttons.\n \nRecent\n \nRequest\n \nItem\n \n(Dashboard)\n \ndiv.card.border-st\nart-0\n \nDisplays\n \nsummary\n \nof\n \na\n \nsingle\n \nrecent\n \ntraining\n \nrequest.\n \nShows\n \ncreator,\n \ncategory,\n \nreason\n \nsnippet,\n \ndate,\n \nrole,\n \nstatus,\n \nand\n \na\n \nview\n \nbutton.\n \nLogin\n \nSuccess\n \nNotification\n \ndiv#loginNotifica\ntion.notification\n \nTemporary\n \npop-up\n \nshown\n \nafter\n \nsuccessful\n \nlogin.\n \nMessage:\n \n\"Login\n \nSuccessful.\n \nWelcome,\n \n[User]!\"\n \n \n \n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nMain\n \nNavigation\n \nBar\n \nnav.navbar\n \nProvides\n \ntop-level\n \nsite\n \nnavigation\n \nand\n \nuser\n \nactions.\n \nIncludes\n \nbrand\n \nlogo/name,\n \nnavigation\n \nlinks\n \n(if\n \nany\n \non\n \nthat\n \npage),\n \nand\n \na\n \nuser\n \ndropdown\n \nmenu.\n \nUser\n \nDropdown\n \n(Navbar)\n \nli.nav-item.dropd\nown\n \nAllows\n \nuser\n \nto\n \naccess\n \nprofile\n \nor\n \nlogout.\n \nTypically\n \ncontains\n \nlinks\n \nto\n \n'Profile'\n \nand\n \na\n \n'Logout'\n \nbutton/form.\n \nUser's\n \nname\n \nis\n \noften\n \ndisplayed.\n \nLogout\n \nButton/Link\n \nform\n \n(action=\"/logout\")\n \n/\n \na\n \nLogs\n \nthe\n \ncurrent\n \nuser\n \nout\n \nof\n \nthe\n \nsystem.\n \nCan\n \nbe\n \na\n \ndirect\n \nlink\n \nor\n \na\n \nform\n \nsubmission\n \ntriggering\n \nlogout.\n \nSidebar\n \nNavigation\n \ndiv.sidebar\n \nProvides\n \nprimary\n \nnavigation\n \nfor\n \nContains\n \na\n \nlist\n \nof\n \nlinks\n \n(ul.nav.nav-pills)\n \nto\n \ndifferent\n \napplication\n \nsections\n \nlike\n \nDashboard,\n \nRequests,\n\nauthenticated\n \nusers.\n \nProfile,\n \netc.\n \nConditional\n \nlinks\n \nbased\n \non\n \nrole.\n \nSidebar\n \nNavigation\n \nLink\n \na.nav-link\n \n(within\n \nsidebar)"}, {"heading_number": "1.4", "title": "1.4 Screen: Role Change Request", "Content": "section\n \nof\n \nthe\n \napplication.\n \nEach\n \nlink\n \nusually\n \nhas\n \nan\n \nicon\n \nand\n \ntext.\n \nThe\n \nactive\n \nlink\n \nis\n \nhighlighted.\n \nAlert\n \nMessage\n \nContainer\n \ndiv#alert<PERSON>ontaine\nr\n \nDisplays\n \ndynamic\n \nsuccess,\n \nerror,\n \nor\n \ninfo\n \nmessages\n \nto\n \nuser.\n \nContent\n \nis\n \nusually\n \npopulated\n \nby\n \nJavaScript\n \nafter\n \nan\n \naction.\n \n \n1.4\n \nScreen:\n \nRole\n \nChange\n \nRequest\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nCreate\n \nRole\n \nRequest\n \nForm\n \nform#cre\nateRoleR\nequestFor\nm\n \nAllows\n \nStaff\n \nto\n \nsubmit\n \na\n \nnew\n \nrole\n \nchange\n \nrequest.\n \nIncludes\n \nfields\n \nfor\n \n'Requested\n \nRole'\n \nand\n \n'Reason'.\n \nHidden\n \nif\n \na\n \npending\n \nrequest\n \nexists.\n \nRequested\n \nRole\n \nSelect\n \n(Create)\n \nselect#re\nquestedR\nole\n \nDropdown\n \nfor\n \nstaff\n \nto\n \nchoose\n \ndesired\n \nnew\n \nrole.\n \nOptions\n \ninclude:\n \nManager,\n \nDirector,\n \nCEO.\n \nReason\n \nTextarea\n \n(Create)\n \ntextarea#\nreason\n \nInput\n \nfor\n \nstaff\n \nto\n \nexplain\n \nthe\n \nreason\n \nfor\n \nrole\n \nchange.\n \nEnforces\n \nmin/max\n \nlength.\n \nSubmit\n \nRole\n \nRequest\n \nButton\n \nbutton#su\nbmitBtn\n \nSubmits\n \nthe\n \nrole\n \nchange\n \nrequest\n \nform.\n \n \nMy\n \nRole\n \nRequests\n \nList\n \ndiv#role\nRequests\nList\n \nDisplays\n \nlist\n \nof\n \nuser's\n \nown\n \nrole\n \nchange\n \nrequests.\n \nShows\n \nrequested\n \nrole,\n \nreason,\n \nstatus,\n \nand\n \nreviewer.\n\n{\n \n  \n\"screenName\":\n \n\"RoleChangeRequestScreen_StaffView\",\n \n  \n\"title\":\n \n\"Role\n \nChange\n \nRequests\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \nheader/sidebar\n \nfrom\n \nDashboard\n \nstructure\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\"title\":\n \n\"Role\n \nChange\n \nRequests\",\n \n\"subtitle\":\n \n\"Manage\n \nyour\n \nrole\n \nchange\n \nrequests\",\n \n\"currentUserRole\":\n \n\"[User's\n \nCurrent\n \nRole]\"},\n \n    \n\"createRequestFormSection\":\n \n{\n \n      \n\"id\":\n \n\"createRoleChangeRequestFormSection\",\n \n      \n\"title\":\n \n\"Create\n \nNew\n \nRole\n \nChange\n \nRequest\",\n \n      \n\"condition\":\n \n\"noPendingRequestForUser\",\n \n      \n\"form\":\n \n{\n \n        \n\"id\":\n \n\"createRoleChangeForm\",\n \n        \n\"fields\":\n \n[\n \n          \n{\"type\":\n \n\"Dropdown\",\n \n\"id\":\n \n\"requestedRole\",\n \n\"label\":\n \n\"Requested\n \nRole\n \n*\",\n \n\"optionsSource\":\n \n\"availableRoles\",\n \n\"required\":\n \ntrue},\n \n          \n{\"type\":\n \n\"TextArea\",\n \n\"id\":\n \n\"reasonForRequest\",\n \n\"label\":\n \n\"Reason\n \nfor\n \nRequest\n \n*\",\n \n\"placeholder\":\n \n\"Please\n \ndescribe\n \nwhy\n \nyou\n \nwant\n \nto\n \nchange\n \nyour\n \nrole...\",\n \n\"minLength\":\n \n10,\n \n\"maxLength\":\n \n1000,\n \n\"required\":\n \ntrue}\n \n        \n],\n \n        \n\"actions\":\n \n[\n\n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"submitRoleChangeRequest\",\n \n\"text\":\n \n\"Submit\n \nRequest\",\n \n\"icon\":\n \n\"submit_icon\",\n \n\"triggersUseCase\":\n \n\"UC-RC001_StaffCreatesRoleChangeRequest\"}\n \n        \n]\n \n      \n}\n \n    \n},\n \n    \n\"userRequestsSection\":\n \n{\n \n      \n\"id\":\n \n\"userRoleRequestsListSection\",\n \n      \n\"title\":\n \n\"Your\n \nRole\n \nRequests\",\n \n      \n\"emptyStateMessage\":\n \n\"You\n \ndon't\n \nhave\n \nany\n \nrole\n \nchange\n \nrequests.\",\n \n      \n\"list\":\n \n{\n \n        \n\"id\":\n \n\"userRoleRequestsList\",\n \n        \n\"itemTemplate\":\n \n{\n \n          \n\"requestedRole\":\n \n\"[request.requestedRole]\",\n \n          \n\"reason\":\n \n\"[request.reason]\",\n \n          \n\"status\":\n \n\"[request.status]\",\n \n          \n\"submissionDate\":\n \n\"[request.submissionDate]\",\n \n          \n\"reviewerComments\":\n \n\"[request.reviewerComments]\",\n \n\"condition\":\n \n\"request.reviewerComments_exists\"\n \n        \n},\n \n        \n\"dataSource\":\n \n\"userSubmittedRoleChangeRequests\"\n \n      \n}\n \n    \n}\n \n  \n}\n \n}\n \n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nCreate\n \nRole\n \nRequest\n \nForm\n \nform#createRole\nRequestForm\n \nAllows\n \nStaff\n \nto\n \nsubmit\n \na\n \nnew\n \nrole\n \nchange\n \nrequest.\n \nIncludes\n \nfields\n \nfor\n \n'Requested\n \nRole'\n \nand\n \n'Reason'.\n \nHidden\n \nif\n \na\n \npending\n \nrequest\n \nexists.\n\nRequested"}, {"heading_number": "1.5", "title": "1.5 Screen: Approve Role Change Request", "Content": "ole\n \nDropdown\n \nfor\n \nstaff\n \nto\n \nchoose\n \ndesired\n \nnew\n \nrole.\n \nOptions\n \ninclude:\n \nManager,\n \nDirector,\n \nCEO.\n \nReason\n \nTextarea\n \n(Create)\n \ntextarea#reason\n \nInput\n \nfor\n \nstaff\n \nto\n \nexplain\n \nthe\n \nreason\n \nfor\n \nrole\n \nchange.\n \nEnforces\n \nmin/max\n \nlength.\n \nSubmit\n \nRole\n \nRequest\n \nButton\n \nbutton#submitBtn\n \nSubmits\n \nthe\n \nrole\n \nchange\n \nrequest\n \nform.\n \n \nMy\n \nRole\n \nRequests\n \nList\n \ndiv#roleRequests\nList\n \nDisplays\n \nlist\n \nof\n \nuser's\n \nown\n \nrole\n \nchange\n \nrequests.\n \nShows\n \nrequested\n \nrole,\n \nreason,\n \nstatus,\n \nand\n \nreviewer.\n \n \n1.5\n \nScreen:\n \nApprove\n \nRole\n \nChange\n \nRequest\n \n \n{\n \n  \n\"screenName\":\n \n\"ApproveRoleChangeRequestScreen\",\n \n  \n\"title\":\n \n\"Approve\n \nRole\n \nChange\n \nRequests\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \nheader/sidebar\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\"title\":\n \n\"Approve\n \nRole\n \nChange\n \nRequests\",\n \n\"subtitle\":\n \n\"Manage\n \nand\n \napprove\n \nrole\n \nchange\n \nrequests\n \nfrom\n \nemployees\",\n \n\"currentUserRole\":\n \n\"CEO\"},\n \n    \n\"statsCards\":\n \n[\n \n      \n{\"id\":\n \n\"pendingRoleStats\",\n \n\"title\":\n \n\"Pending\",\n \n\"value\":\n \n\"[stats.pendingRoleRequests]\",\n \n\"icon\":\n \n\"pending_icon\"},\n\n{\"id\":\n \n\"approvedRoleStats\",\n \n\"title\":\n \n\"Approved\",\n \n\"value\":\n \n\"[stats.approvedRoleRequests]\",\n \n\"icon\":\n \n\"approved_icon\"},\n \n      \n{\"id\":\n \n\"rejectedRoleStats\",\n \n\"title\":\n \n\"Rejected\",\n \n\"value\":\n \n\"[stats.rejectedRoleRequests]\",\n \n\"icon\":\n \n\"rejected_icon\"}\n \n    \n],\n \n    \n\"filterTabs\":\n \n{\n \n      \n\"id\":\n \n\"requestStatusTabs\",\n \n      \n\"tabs\":\n \n[\n \n        \n{\"id\":\n \n\"pendingTab\",\n \n\"text\":\n \n\"Pending\",\n \n\"activatesFilter\":\n \n\"status=pending\"},\n \n        \n{\"id\":\n \n\"approvedTab\",\n \n\"text\":\n \n\"Approved\",\n \n\"activatesFilter\":\n \n\"status=approved\"},\n \n        \n{\"id\":\n \n\"rejectedTab\",\n \n\"text\":\n \n\"Rejected\",\n \n\"activatesFilter\":\n \n\"status=rejected\"}\n \n      \n]\n \n    \n},\n \n    \n\"requestsList\":\n \n{\n \n      \n\"id\":\n \n\"pendingRoleChangeRequestsList\",\n \n      \n\"itemTemplate\":\n \n{\n \n        \n\"userName\":\n \n\"[request.userName]\",\n \n        \n\"userEmail\":\n \n\"[request.userEmail]\",\n \n        \n\"currentRole\":\n \n\"[request.currentRole]\",\n \n        \n\"requestedRole\":\n \n\"[request.requestedRole]\",\n \n        \n\"reason\":\n \n\"[request.reason]\",\n \n        \n\"adminComments\":\n \n\"[request.adminComments]\",\n \n        \n\"createdDate\":\n \n\"[request.createdDate]\",\n \n        \n\"reviewedDate\":\n \n\"[request.reviewedDate]\",\n \n\"reviewedBy\":\n \n\"[request.reviewedBy]\",\n \n        \n\"status\":\n \n\"[request.status]\",\n \n        \n\"actions\":\n \n[\n \n          \n{\"id\":\n \n\"processRequestButton\",\n \n\"text\":\n \n\"Process\",\n \n\"icon\":\n \n\"process_icon\",\n \n\"opensModal\":\n \n\"ReviewRoleChangeModal\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[request.id]\"},\n \n\"condition\":\n \n\"request.status==pending\"}\n \n        \n]\n \n      \n},\n\n\"dataSource\":\n \n\"roleChangeRequests_filtered\"\n \n    \n},\n \n    \n\"reviewModal\":\n \n{\n \n      \n\"id\":\n \n\"ReviewRoleChangeModal\",\n \n      \n\"title\":\n \n\"Review\n \nRole\n \nChange\n \nRequest:\n \n[request.userName]\",\n \n      \n\"fields\":\n \n[\n \n        \n{\"type\":\n \n\"Display\",\n \n\"label\":\n \n\"Current\n \nRole\",\n \n\"value\":\n \n\"[request.currentRole]\"},\n \n        \n{\"type\":\n \n\"Display\",\n \n\"label\":\n \n\"Requested\n \nRole\",\n \n\"value\":\n \n\"[request.requestedRole]\"},\n \n        \n{\"type\":\n \n\"Display\",\n \n\"label\":\n \n\"Reason\",\n \n\"value\":\n \n\"[request.reason]\"},\n \n        \n{\"type\":\n \n\"TextArea\",\n \n\"id\":\n \n\"adminCommentsModal\",\n \n\"label\":\n \n\"Admin\n \nComments\"},\n \n        \n{\"type\":\n \n\"Dropdown\",\n \n\"id\":\n \n\"decisionModal\",\n \n\"label\":\n \n\"Decision\",\n \n\"options\":\n \n[\"Approve\",\n \n\"Reject\"]}\n \n      \n],\n \n      \n\"actions\":\n \n[\n \n        \n{\"type\":\n \n\"Button\",\n \n\"id\":\n \n\"submitReviewDecision\",\n \n\"text\":\n \n\"Submit\n \nDecision\",\n \n\"triggersUseCase\":\n \n\"UC-RC002_CEOManagesRoleChangeRequest\"},\n \n        \n{\"type\":\n \n\"Button\",\n \n\"id\":\n \n\"cancelReview\",\n \n\"text\":\n \n\"Cancel\",\n \n\"closesModal\":\n \ntrue}\n \n      \n]\n \n    \n}\n \n  \n}\n \n}\n \n \n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nPending\n \nCount\n \nDisplay\n \nh3#pendin\ngCount\n \nShows\n \nnumber\n \nof\n \npending\n \nrole\n \nchange\n \nrequests.\n \nPart\n \nof\n \nthe\n \nstatistics\n \ncards\n \nsection.\n \nApproved\n \nCount\n \nDisplay\n \nh3#approv\nedCount\n \nShows\n \nnumber\n \nof\n \napproved\n \nrole\n \nchange\n \nrequests.\n \nPart\n \nof\n \nthe\n \nstatistics\n \ncards\n \nsection.\n \nRejected\n \nh3#rejecte\nShows\n \nnumber\n \nof\n \nPart\n \nof\n \nthe\n \nstatistics\n \ncards\n \nsection.\n\nCount\n \nDisplay\n \ndCount\n \nrejected\n \nrole\n \nchange\n \nrequests.\n \nStatus\n \nFilter\n \nTabs\n \nul#statusT\nabs\n \nFilters\n \nrequests\n \nby\n \nstatus\n \n(Pending,\n \nApproved,\n \netc.).\n \nContains\n \ntab\n \nbuttons\n \n(#pending-tab,\n \n#approved-tab,\n \netc.)\n \nthat\n \nswitch\n \nvisible\n \ncontent.\n \nPending\n \nRequests\n \nTab\n \nPanel\n \ndiv#pendin\ngRequests\n \nContainer\n \nfor\n \npending\n \nrequests.\n \nPopulated\n \ndynamically\n \nwith\n \nrequest\n \ncards.\n \nApproved\n \nRequests\n \nTab\n \nPanel\n \ndiv#appro\nvedReques\nts\n \nContainer\n \nfor\n \napproved\n \nrequests.\n \nPopulated\n \ndynamically\n \nwith\n \nrequest\n \ncards.\n \nRejected\n \nRequests\n \nTab\n \nPanel\n \ndiv#rejecte\ndRequests\n \nContainer\n \nfor\n \nrejected\n \nrequests.\n \nPopulated\n \ndynamically\n \nwith\n \nrequest\n \ncards.\n \nRole\n \nRequest\n \nCard\n \n(Admin\n \nView)\n \ndiv.request\n-card\n \nDisplays\n \nrole\n \nchange\n \nrequest\n \ndetails.\n \nShows\n \nuser\n \ninfo,\n \nroles,\n \nreason,\n \ncomments,\n \ntimestamps,\n \nand\n \n'Process'\n \nbutton.\n \nProcess\n \nRequest\n \nButton\n \nbutton\n \n(in\n \nrequest\n \ncard)\n \nOpens\n \nreview\n \nmodal\n \nfor\n \npending\n \nrequest.\n \nonclick=\"openReviewModal(requestId)\"\n \nReview\n \nRequest\n \nModal\n \ndiv#review\nModal\n \nModal\n \ndialog\n \nfor\n \napproving/rejecting\n \na\n \nrequest.\n \nContains\n \nrequest\n \ndetails\n \nand\n \ndecision\n \nform.\n \nReview\n \nDecision\n \nSelect\n \nselect#revi\newAction\n \nAllows\n \nadmin\n \nto\n \nchoose\n \n'Approve'\n \nor\n \n'Reject'.\n \nPart\n \nof\n \nthe\n \nreview\n \nmodal\n \nform.\n \nReview\n \nComments\n \nTextarea\n \ntextarea#re\nviewCom\nments\n \nAllows\n \nadmin\n \nto\n \nadd\n \ncomments\n \nto\n \nreview.\n \nPart\n \nof\n \nthe\n \nreview\n \nmodal\n \nform.\n \nSubmit\n \nReview\n \nButton\n \nbutton#sub\nmitReview\n \nSubmits\n \nadmin's\n \ndecision\n \non\n \nthe\n \nrole\n \nchange\n \nrequest.\n \nLocated\n \nin\n \nreview\n \nmodal\n \nfooter."}, {"heading_number": "1.6", "title": "1.6 Screen: Profile", "Content": "{\n \n  \n\"screenName\":\n \n\"ProfileScreen\",\n \n  \n\"title\":\n \n\"User\n \nProfile\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \nheader/sidebar\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\"title\":\n \n\"User\n \nProfile\"},\n \n    \n\"sections\":\n \n[\n \n      \n{\n \n        \n\"id\":\n \n\"personalInfoSection\",\n \n        \n\"title\":\n \n\"Personal\n \nInformation\",\n \n        \n\"form\":\n \n{\n \n          \n\"id\":\n \n\"personalInfoForm\",\n \n          \n\"fields\":\n \n[\n \n            \n{\"type\":\n \n\"InputText\",\n \n\"id\":\n \n\"profileFullName\",\n \n\"label\":\n \n\"Full\n \nName\",\n \n\"value\":\n \n\"[user.fullName]\",\n \n\"bindsTo\":\n \n\"user.fullName\"},\n \n            \n{\"type\":\n \n\"Display\",\n \n\"id\":\n \n\"profileUsername\",\n \n\"label\":\n \n\"Username\",\n \n\"value\":\n \n\"[user.username]\"},\n \n//\n \nUsually\n \nnot\n \neditable\n \n            \n{\"type\":\n \n\"InputEmail\",\n \n\"id\":\n \n\"profileEmail\",\n \n\"label\":\n \n\"Email\",\n \n\"value\":\n \n\"[user.email]\",\n \n\"bindsTo\":\n \n\"user.email\"},\n \n            \n{\"type\":\n \n\"Display\",\n \n\"id\":\n \n\"profileRole\",\n \n\"label\":\n \n\"Role\",\n \n\"value\":\n \n\"[user.role]\"}\n \n//\n \nRole\n \nchanged\n \nvia\n \nrequests\n \n          \n],\n\n\"actions\":\n \n[\n \n            \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"updatePersonalInfo\",\n \n\"text\":\n \n\"Update\n \nInformation\",\n \n\"triggersUseCase\":\n \n\"UC_2_ManageUserProfile_Info\"}\n \n          \n]\n \n        \n}\n \n      \n},\n \n      \n{\n \n        \n\"id\":\n \n\"changePasswordSection\",\n \n        \n\"title\":\n \n\"Change\n \nPassword\",\n \n        \n\"form\":\n \n{\n \n          \n\"id\":\n \n\"changePasswordForm\",\n \n          \n\"fields\":\n \n[\n \n            \n{\"type\":\n \n\"InputPassword\",\n \n\"id\":\n \n\"currentPassword\",\n \n\"label\":\n \n\"Current\n \nPassword\",\n \n\"required\":\n \ntrue},\n \n            \n{\"type\":\n \n\"InputPassword\",\n \n\"id\":\n \n\"newPassword\",\n \n\"label\":\n \n\"New\n \nPassword\",\n \n\"required\":\n \ntrue},\n \n            \n{\"type\":\n \n\"InputPassword\",\n \n\"id\":\n \n\"confirmNewPassword\",\n \n\"label\":\n \n\"Confirm\n \nNew\n \nPassword\",\n \n\"required\":\n \ntrue}\n \n          \n],\n \n          \n\"actions\":\n \n[\n \n            \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"updatePassword\",\n \n\"text\":\n \n\"Change\n \nPassword\",\n \n\"triggersUseCase\":\n \n\"UC_2_ManageUserProfile_Password\"}\n \n          \n]\n \n        \n}\n \n      \n}\n \n    \n]\n \n  \n}\n \n}\n \n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n\nUser\n \nInfo\n \n(Sidebar)\n \ndiv.text-ce\nnter.mb-4\n \nDisplays\n \nuser's\n \navatar,\n \nname,\n \nand\n \nrole\n \nin\n \nsidebar.\n \nFetched\n \nusing\n \nth:text=\"${user.name}\"\n \nand\n \nth:text=\"${user.role}\".\n \nUpdate\n \nPersonal\n \nInfo\n \nForm\n \nform\n \n(action=\"\n@{/profile\n/update}\")\n \nAllows\n \nusers\n \nto\n \nupdate\n \ntheir\n \nname\n \nand\n \nemail.\n \nUsername\n \nand\n \nRole\n \nare\n \ntypically\n \nread-only.\n \nP"}, {"heading_number": "1.7", "title": "1.7 Screen: Request List", "Content": "input#nam\ne\n \nField\n \nto\n \nupdate\n \nuser's\n \nfull\n \nname.\n \n \nProfile\n \nEmail\n \nInput\n \ninput#<PERSON><PERSON>\nl\n \n<PERSON>\n \nto\n \nupdate\n \nuser's\n \nemail.\n \n \nChange\n \nPassword\n \nForm\n \nform\n \n(action=\"\n@{/profile\n/update}\")\n \nAllows\n \nusers\n \nto\n \nchange\n \ntheir\n \npassword.\n \nFields:\n \nCurrent\n \nPassword,\n \nNew\n \nPassword,\n \nConfirm\n \nNew\n \nPassword.\n \nCurrent\n \nPassword\n \nInput\n \ninput#curr\nentPasswo\nrd\n \nField\n \nfor\n \ncurrent\n \npassword\n \n(for\n \nverification).\n \n \nNew\n \nPassword\n \nInput\n \ninput#new\nPassword\n \nField\n \nfor\n \nentering\n \nnew\n \npassword.\n \n \nConfirm\n \nNew\n \nPassword\n \nInput\n \ninput#conf\nirmPasswo\nrd\n \nField\n \nto\n \nconfirm\n \nthe\n \nnew\n \npassword.\n \nChecked\n \nagainst\n \nNew\n \nPassword\n \nvia\n \nJavaScript.\n \n \n \n1.7\n \nScreen:\n \nRequest\n \nList\n \n \n{\n\n\"screenName\":\n \n\"RequestListScreen_Training\",\n \n  \n\"title\":\n \n\"Request\n \nList\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\"title\":\n \n\"Request\n \nList\"},\n \n    \n\"infoPanel\":\n \n{\n \n      \n\"id\":\n \n\"requestListInfoPanel\",\n \n      \n\"title\":\n \n\"Thông\n \ntin\n \nvề\n \nhệ\n \nthống\n \nyêu\n \ncầu\",\n \n      \n\"text\":\n \n\"<PERSON><PERSON><PERSON>\n \nlà\n \nchức\n \nnăng\n \nchính\n \ncủa\n \nhệ\n \nthống...\",\n \n      \n\"roleSpecificInfo\":\n \n[\n \n        \n{\"role\":\n \n\"STAFF\",\n \n\"text\":\n \n\"<PERSON>h<PERSON>\n \nviên\n \n(STAFF):\n \n<PERSON><PERSON><PERSON>\n \nyêu\n \ncầu\n \nđào\n \ntạo\"},\n \n        \n{\"role\":\n \n\"MANAGER\",\n \n\"text\":\n \n\"Quản\n \nlý\n \n(MANAGER):\n \nXét\n \nduyệt\n \nbước\n \nđầu\"},\n \n        \n{\"role\":\n \n\"DIRECTOR\",\n \n\"text\":\n \n\"Giám\n \nđốc\n \n(DIRECTOR):\n \nXét\n \nduyệt\n \ncấp\n \ntrung\"},\n \n        \n{\"role\":\n \n\"CEO\",\n \n\"text\":\n \n\"CEO:\n \nPhê\n \nduyệt\n \ncuối\n \ncùng\"}\n \n      \n]\n \n    \n},\n \n    \n\"requestTable\":\n \n{\n \n      \n\"id\":\n \n\"trainingRequestTable\",\n \n      \n\"columns\":\n \n[\n \n        \n{\"header\":\n \n\"ID\",\n \n\"dataField\":\n \n\"id\"},\n \n        \n{\"header\":\n \n\"Category\",\n \n\"dataField\":\n \n\"category\"},\n \n        \n{\"header\":\n \n\"Creator\",\n \n\"dataField\":\n \n\"creatorName\"},\n \n        \n{\"header\":\n \n\"Created\n \nDate\",\n \n\"dataField\":\n \n\"createdDate\",\n \n\"format\":\n \n\"date\"},\n \n        \n{\"header\":\n \n\"Status\",\n \n\"dataField\":\n \n\"status\",\n \n\"displayType\":\n \n\"Badge\"},\n \n        \n{\"header\":\n \n\"Actions\",\n \n\"displayType\":\n \n\"ActionButtons\",\n \n\"actions\":\n \n[\n \n          \n{\"id\":\n \n\"viewDetailAction\",\n \n\"text\":\n \n\"View\",\n \n\"icon\":\n \n\"view_icon\",\n \n\"navigatesTo\":\n \n\"RequestDetailScreen_Training\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[row.id]\"}}\n \n        \n]}\n \n      \n],\n \n      \n\"dataSource\":\n \n\"trainingRequests_all_or_filtered\",\n \n//\n \nDepends\n \non\n \nuser\n \nrole\n \nand\n \nfilters\n\n\"pagination\":\n \ntrue,\n \n      \n\"searchable\":\n \ntrue,\n \n      \n\"sortable\":\n \ntrue\n \n    \n}\n \n  \n}\n \n}\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nCreateNewTr\nainingReques\ntPage\n \nButton\n \na\n \n(href=\"/requ\nests-view/cr\neate\")\n \nAllows\n \nStaff\n \nto\n \nnavigate\n \nto\n \nrequest\n \ncreation\n \npage.\n \nVisible\n \nonly\n \nto\n \nusers\n \nwith\n \nthe\n \n'STAFF'\n \nrole.\n \nTraining\n \nRequest\n \nInfo\n \nPanel\n \ndiv.alert.aler\nt-info\n \nExplains\n \ntraining\n \nrequest\n \nsystem\n \nand\n \napproval\n \nflow.\n \nDescribes\n \nroles\n \nin\n \napproval:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO.\n \nTraining\n \nRequests\n \nTable\n \ntable.table\n \nDisplays\n \ntraining\n \nrequests\n \nin\n \na\n \ntable.\n \nColumns\n \ninclude:\n \nID,\n \nCategory,\n \nCreator,\n \nCreated\n \nDate,\n \nStatus,\n \nActions.\n \nView\n \nTraining\n \nRequest\n \nButton\n \na.btn-primar\ny\n \n(in\n \ntable\n \nrow)\n \nNavigates\n \nto\n \nrequest\n \ndetail\n \npage.\n \nAllows\n \nusers\n \nto\n \nview\n \nmore\n \ndetailed\n \ninformation\n \nabout\n \na\n \nspecific\n \nrequest."}, {"heading_number": "1.8", "title": "1.8 Screen: Request Details", "Content": "{\n \n  \n\"screenName\":\n \n\"RequestDetailScreen_Training\",\n \n  \n\"title\":\n \n\"Request\n \nDetails\n \n#[request.id]\n \n-\n \n[request.category]\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\n \n      \n\"title\":\n \n\"Request\n \nDetails\n \n#[request.id]\n \n-\n \n[request.category]\",\n \n      \n\"backButton\":\n \n{\"text\":\n \n\"←\n \nBack\",\n \n\"navigatesTo\":\n \n\"RequestListScreen_Training\"},\n \n      \n\"statusBadge\":\n \n{\"text\":\n \n\"[request.status]\",\n \n\"type\":\n \n\"[request.statusType]\"}\n \n    \n},\n \n    \n\"requestInformationSection\":\n \n{\n \n      \n\"id\":\n \n\"requestInfoCard\",\n \n      \n\"title\":\n \n\"Request\n \nInformation\",\n \n      \n\"fields\":\n \n[\n \n        \n{\"label\":\n \n\"Category:\",\n \n\"value\":\n \n\"[request.category]\"},\n \n        \n{\"label\":\n \n\"Creator:\",\n \n\"value\":\n \n\"[request.creatorName]\"},\n \n        \n{\"label\":\n \n\"Created\n \nDate:\",\n \n\"value\":\n \n\"[request.createdDate]\",\n \n\"format\":\n \n\"datetime\"},\n \n        \n{\"label\":\n \n\"Last\n \nUpdated:\",\n \n\"value\":\n \n\"[request.lastUpdatedDate]\",\n \n\"format\":\n \n\"datetime\"},\n \n        \n{\"label\":\n \n\"Description:\",\n \n\"value\":\n \n\"[request.description]\",\n \n\"displayType\":\n \n\"MultilineText\"}\n \n      \n]\n\n},\n \n    \n\"approvalActionsSection\":\n \n{\n \n//\n \nConditional\n \nbased\n \non\n \nrole\n \nand\n \nrequest\n \nstatus\n \n      \n\"id\":\n \n\"approvalActionForm\",\n \n      \n\"condition\":\n \n\"canCurrentUserActOnRequest\",\n \n      \n\"form\":\n \n{\n \n        \n\"fields\":\n \n[\n \n          \n{\"type\":\n \n\"TextArea\",\n \n\"id\":\n \n\"approverComments\",\n \n\"label\":\n \n\"Comments\n \n(Optional\n \nfor\n \nApprove,\n \nRequired\n \nfor\n \nReject)\"}\n \n        \n],\n \n        \n\"actions\":\n \n[\n \n          \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"approveRequestButton\",\n \n\"text\":\n \n\"Approve\",\n \n\"triggersUseCase\":\n \n\"[RelevantApproveUseCase]\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[request.id]\"}},\n \n          \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"rejectRequestButton\",\n \n\"text\":\n \n\"Reject\",\n \n\"triggersUseCase\":\n \n\"[RelevantRejectUseCase]\",\n \n\"params\":\n \n{\"requestId\":\n \n\"[request.id]\"}}\n \n        \n]\n \n      \n}\n \n    \n},\n \n    \n\"approvalHistorySection\":\n \n{\n \n      \n\"id\":\n \n\"approvalHistoryTimeline\",\n \n      \n\"title\":\n \n\"Approval\n \nHistory\",\n \n      \n\"timelineItems\":\n \n[\n \n//\n \nLoop\n \nthrough\n \nhistory\n \n        \n{\n \n          \n\"actor\":\n \n\"[historyItem.actorName]\n \n([historyItem.actorRole])\",\n \n          \n\"action\":\n \n\"[historyItem.actionTaken]\",\n \n//\n \ne.g.,\n \nCreated,\n \nApproved,\n \nRejected\n \n          \n\"timestamp\":\n \n\"[historyItem.timestamp]\",\n \n          \n\"comments\":\n \n\"[historyItem.comments]\",\n \n\"condition\":\n \n\"historyItem.comments_exists\"\n \n        \n}\n \n      \n],\n \n      \n\"dataSource\":\n \n\"requestApprovalHistory\"\n \n    \n}\n \n  \n}\n\n}\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nRequest\n \nStatus\n \nBadge\n \n(Detail)\n \nspan.badge\n \nDisplays\n \nthe\n \ncurrent\n \nstatus\n \nof\n \nthe\n \ntraining\n \nrequest.\n \nE.g.,\n \nPending,\n \nApproved,\n \nRejected,\n \nwith\n \ncorresponding\n \ncolors.\n \nRequest\n \nInformation\n \nSection\n \ndiv.card-b\nody\n \nDisplays\n \ncore\n \ndetails\n \nof\n \nthe\n \nrequest\n \n(Category,\n \nCreator,\n \netc.)\n \n \nApprove\n \nRequest\n \nForm\n \nform\n \n(action=\"...\n/approve\")\n \nAllows\n \nauthorized\n \nusers\n \nto\n \napprove\n \nthe\n \ntraining\n \nrequest.\n \nIncludes\n \noptional\n \ncomments\n \ntextarea.\n \nVisibility\n \ndepends\n \non\n \nrole\n \nand\n \nrequest\n \nstatus.\n \nApprove\n \nComments\n \nTextarea\n \ntextarea#a\npprove-co\nmments\n \nFor\n \nadding\n \ncomments\n \nwhen\n \napproving\n \na\n \nrequest.\n \n \nApprove\n \nRequest\n \nButton\n \nbutton.btn-\nsuccess\n \nSubmits\n \nthe\n \napproval\n \nfor\n \nthe\n \ntraining\n \nrequest.\n \n \nReject\n \nRequest\n \nForm\n \nform\n \n(action=\"...\n/reject\")\n \nAllows\n \nauthorized\n \nusers\n \nto\n \nreject\n \nthe\n \ntraining\n \nrequest.\n \nIncludes\n \nrequired\n \ncomments/reason\n \ntextarea.\n \nVisibility\n \ndepends\n \non\n \nrole\n \nand\n \nrequest\n \nstatus.\n \nReject\n \nComments\n \nTextarea\n \ntextarea#re\nject-comm\nents\n \nFor\n \nproviding\n \na\n \nreason\n \nwhen\n \nrejecting\n \na\n \nrequest.\n \nRequired\n \nfield.\n \nReject\n \nRequest\n \nButton\n \nbutton.btn-\ndanger\n \nSubmits\n \nthe\n \nrejection\n \nfor\n \nthe\n \ntraining\n \nrequest.\n \n \nApproval\n \nHistory\n \nTimeline\n \nul.timeline\n \nDisplays\n \nstep-by-step\n \napproval\n \nhistory\n \nof\n \nthe\n \nrequest.\n \nEach\n \nli\n \nshows\n \naction\n \ntype\n \n(Create/Approve/Reject),\n \nuser,\n \nrole,\n \ntimestamp,\n \nand\n \nany\n \ncomments."}, {"heading_number": "1.9", "title": "1.9 Screen: CreateNewTrainingRequestPage", "Content": "{\n \n  \n\"screenName\":\n \n\"CreateTrainingRequestScreen\",\n \n  \n\"title\":\n \n\"CreateNewTrainingRequestPage\",\n \n  \n\"layout\":\n \n{\n \n\"type\":\n \n\"AppLayoutWithSidebar\"\n \n/*\n \nInherits\n \n*/\n \n},\n \n  \n\"mainContent\":\n \n{\n \n    \n\"pageHeader\":\n \n{\n \n      \n\"title\":\n \n\"CreateNewTrainingRequestPage\",\n \n      \n\"backButton\":\n \n{\"text\":\n \n\"←\n \nBack\",\n \n\"navigatesTo\":\n \n\"RequestListScreen_Training_OR_Dashboard\"}\n \n    \n},\n \n    \n\"requestInformationForm\":\n \n{\n \n      \n\"id\":\n \n\"createTrainingRequestForm\",\n \n      \n\"title\":\n \n\"Request\n \nInformation\",\n \n      \n\"form\":\n \n{\n \n        \n\"fields\":\n \n[\n \n          \n{\"type\":\n \n\"Dropdown\",\n \n\"id\":\n \n\"trainingCategory\",\n \n\"label\":\n \n\"Training\n \nCategory\n \n*\",\n \n\"optionsSource\":\n \n\"availableTrainingCategories\",\n \n\"helperText\":\n \n\"Choose\n \nthe\n \nmost\n \nappropriate\n \ncategory...\",\n \n\"required\":\n \ntrue},\n \n          \n{\"type\":\n \n\"TextArea\",\n \n\"id\":\n \n\"requestReason\",\n \n\"label\":\n \n\"Request\n \nReason\n \n*\",\n \n\"placeholder\":\n \n\"Describe\n \nin\n \ndetail\n \nthe\n \nreason\n \nfor\n \ntraining...\",\n \n\"required\":\n \ntrue,\n \n\"helperText\":\n \n\"Please\n \nprovide\n \ndetailed\n \ninformation\n \nabout\n \ntraining\n \nneeds,\n \ntarget\n \naudience,\n \nand\n \nexpected\n \noutcomes.\"}\n \n          \n//\n \nOptional:\n \nFileUpload\n \nfor\n \nsupporting\n \ndocuments\n \n          \n//\n \nOptional:\n \nDatePicker\n \nfor\n \npreferred\n \ndates\n\n],\n \n        \n\"actions\":\n \n[\n \n          \n{\"type\":\n \n\"Button\",\n \n\"id\":\n \n\"cancelCreateRequest\",\n \n\"text\":\n \n\"Cancel\",\n \n\"navigatesTo\":\n \n\"RequestListScreen_Training_OR_Dashboard\"},\n \n          \n{\"type\":\n \n\"SubmitButton\",\n \n\"id\":\n \n\"submitTrainingRequest\",\n \n\"text\":\n \n\"Submit\n \nRequest\",\n \n\"icon\":\n \n\"submit_icon\",\n \n\"triggersUseCase\":\n \n\"UC-TR001_StaffCreatesTrainingRequest\"}\n \n        \n]\n \n      \n}\n \n    \n}\n \n  \n}\n \n}\n \n \nElement\n \nName\n \n/\n \nPage\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nBack\n \nto\n \nList\n \nButton\n \na\n \n(href=\"/req\nuests-view\n\")\n \nNavigates\n \nback\n \nto\n \ntraining\n \nrequests\n \nlist\n \npage.\n \n \nTraining\n \nCategory\n \nSelect\n \nselect#cate\ngory\n \nDropdown\n \nto\n \nchoose\n \ntraining\n \nrequest\n \ncategory.\n \nPopulated\n \nwith\n \ncategories\n \nlike\n \n\"Store\n \nOperations\".\n \nTraining\n \nReason\n \nTextarea\n \ntextarea#re\nason\n \nInput\n \nfield\n \nfor\n \ndescribing\n \nreason\n \nfor\n \ntraining\n \nrequest.\n \n \nSubmit\n \nTraining\n \nRequest\n \nButton\n \nbutton\n \n(type=\"sub\nmit\")\n \nSubmits\n \nthe\n \ntraining\n \nrequest\n \nform."}]