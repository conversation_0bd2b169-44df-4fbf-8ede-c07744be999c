================================================
      COVERAGE ANALYSIS: FEATURE 'business_flow_8'
================================================
Analysis Date: 2025-07-28 18:03:49

--- SUMMARY ---
Overall Coverage: 18.2% (6 / 33 criteria covered)

--- GAPS (MISSING TESTS FOR THIS FEATURE) ---
[X] COV-BC-CART-QUANTITY: Boundary: Kiểm tra số lượng trong giỏ hàng (0, số âm, > tồn kho).
[X] COV-BC-LOG-EMAIL: Boundary: Kiểm tra trường Email đăng nhập (trống, email không tồn tại).
[X] COV-BC-LOG-PASSWORD: Boundary: Kiể<PERSON> tra trường Mật khẩu đăng nhập (trống, mật khẩu sai).
[X] COV-BC-PROD-PRICE: Boundary: Kiểm tra trường Giá sản phẩm (trống, 0, số âm, không phải số).
[X] COV-BC-PROD-STOCK: Boundary: Kiểm tra trường Tồn kho sản phẩm (trống, 0, số âm, không phải số nguyên).
[X] COV-BC-REG-EMAIL: Boundary: Kiểm tra trường Email đăng ký (trống, định dạng không hợp lệ, 256+ ký tự).
[X] COV-BC-REG-PASSWORD: Boundary: Kiểm tra trường Mật khẩu đăng ký (trống, 7 ký tự, không khớp).
[X] COV-CART-BR801: Stock Availability: Số lượng yêu cầu không được vượt quá số lượng tồn kho của sản phẩm. (BR801)
[X] COV-ST-ORD-CANCEL: State: Hủy đơn hàng thành công chuyển trạng thái sang CANCELLED.
[X] COV-ST-ORD-CONFIRM: State: Xác nhận đơn hàng thành công chuyển trạng thái sang CONFIRMED.
[X] COV-ST-ORD-CREATE: State: Đặt hàng thành công chuyển trạng thái sang PENDING/REQUEST PLACED.
[X] COV-ST-ORD-INVALID-TRANSITION: State Boundary: Cố gắng chuyển đổi trạng thái không hợp lệ (ví dụ: Hủy đơn hàng đã hoàn thành).
[X] COV-ST-PROD-APPROVE: State: Phê duyệt sản phẩm thành công chuyển trạng thái sang APPROVED.
[X] COV-ST-PROD-CREATE: State: Tạo sản phẩm thành công chuyển sang trạng thái PENDING APPROVAL.
[X] COV-ST-PROD-REJECT: State: Từ chối sản phẩm thành công chuyển trạng thái sang REJECTED.
[X] COV-UC101-BR101: Email Uniqueness: Địa chỉ email phải là duy nhất trong hệ thống. (BR101)
[X] COV-UC101-BR102: Mandatory Fields: Các trường Email, password, confirm password, first name, và last name là bắt buộc. (BR102)
[X] COV-UC101-BR103: Password Strength: Mật khẩu phải dài ít nhất 8 ký tự và bao gồm các ký tự chữ và số. (BR103)
[X] COV-UC101-BR104: Password Match: Mật khẩu và mật khẩu xác nhận phải khớp nhau. (BR104)
[X] COV-UC205-BR1001: Quantity Validation: Số lượng mới phải là một số nguyên dương. (BR1001)
[X] COV-UC205-BR1002: Stock Availability: Số lượng mới không được vượt quá số lượng tồn kho của sản phẩm. (BR1002)
[X] COV-UC207-BR1201: Stock Availability: Tất cả các mặt hàng trong giỏ hàng phải có đủ số lượng tồn kho. (BR1201)
[X] COV-UC207-BR1202: Mandatory Address: Yêu cầu phải có một địa chỉ giao hàng hợp lệ. (BR1202)
[X] COV-UC207-BR1205: Stock Update: Số lượng tồn kho của sản phẩm phải được giảm một cách nguyên tử. (BR1205)
[X] COV-UC301-BR1801: Mandatory Shop Name: Tên cửa hàng là một trường bắt buộc. (BR1801)
[X] COV-UC301-BR1802: Description Length: Tên cửa hàng không được vượt quá 255 ký tự. (BR1802)
[X] COV-UC301-BR1803: Image Constraints: Logo cửa hàng phải là JPEG hoặc PNG và không lớn hơn 3MB. (BR1803)

--- COVERED CRITERIA FOR THIS FEATURE ---
[✓] COV-BC-PROD-IMAGE: Boundary: Kiểm tra tải ảnh sản phẩm (file > 3MB, định dạng không phải JPEG/PNG).
    - Covered by: For 'file > 3MB': TC-BF08-003 (oversized 'large_image.jpg'), TC-BF08-004 (oversized 'large_image.jpg'), TC-BF08-009 (oversized 'image_over_5MB.jpg'), TC-BF08-014 (oversized 'large_product_image.jpg'), TC-BF08-019 (oversized 'large_image.jpg'), TC-BF08-022 (oversized 'image_over_5MB.jpg'). For 'định dạng không phải JPEG/PNG': TC-BF08-002 (invalid format 'product_sheet.xlsx'), TC-BF08-007 (invalid format 'invalid_format'), TC-BF08-008 (invalid format 'invalid_format'), TC-BF08-011 (invalid format 'product_image.gif'), TC-BF08-015 (invalid format 'document.txt'), TC-BF08-017 (invalid format 'product_sheet.xlsx'), TC-BF08-020 (invalid format 'product_spec.docx'), TC-BF08-021 (invalid format 'product_info.txt').
[✓] COV-UC102-BR201: Credential Validation: Hệ thống phải xác thực email và mật khẩu với bảng người dùng. (BR201)
    - Covered by: TC-BF08-002 (Login Failure with Non-Existent Email and Incorrect Password), TC-BF08-004 (Validate failed login attempt due to incorrect password on LoginPage), TC-BF08-006 (Failed login attempt with non-existent email and correct password)
[✓] COV-UC102-BR204: Session Creation: Một phiên làm việc an toàn phải được tạo sau khi đăng nhập thành công. (BR204)
    - Covered by: TC-BF08-001 (Verify a seller can successfully add a new product after logging in - implies successful authentication and session), TC-BF08-001 (Seller successfully adds a new product and navigates back to the dashboard - implies successful authentication and session)
[✓] COV-UC102-BR205: Role-Based Redirection: Hệ thống chuyển hướng người dùng dựa trên vai trò của họ. (BR205)
    - Covered by: TC-BF08-001 (Verify a seller can successfully add a new product after logging in - redirects to SellerDashboardPage), TC-BF08-002 (To verify system behavior when a seller attempts to edit a product using invalid and incomplete data - redirects to SellerDashboardPage), TC-BF08-003 (This test case validates the system's behavior when a seller attempts to delete a product while other fields on the Product Management page contain invalid data - redirects to SellerDashboardPage)
[✓] COV-UC302-BR1901: Mandatory Fields: Tên, giá và số lượng tồn kho là bắt buộc đối với sản phẩm mới. (BR1901)
    - Covered by: TC-BF08-002 (Verify error messages when editing a product with an invalid image format and empty required fields), TC-BF08-003 (Verify that deleting a product fails when other product fields have validation errors), TC-BF08-004 (Validate Product Creation Failure with Invalid Data (Image, Price, Stock) on ProductManagementPage), TC-BF08-005 (Verify product creation fails with empty price and invalid stock format), TC-BF08-007 (Verify error messages for creating a product with empty name, invalid image, and invalid stock), TC-BF08-009 (Verify Rejection Reason Display for Product Creation Failure due to Oversized Image and Missing Details), TC-BF08-010 (TC-BF08-010: Validate error on product resubmission with empty name and stock fields on the Product Management Page.), TC-BF08-011 (Verify Failed Product Deletion with Conflicting Data Entry on Product Management Page), TC-BF08-015 (Seller Fails to Add New Product with Invalid Image and Empty Fields), TC-BF08-017 (Verify Product Creation Fails with Invalid Image Format and Empty Price), TC-BF08-018 (Validate product creation failure with invalid price format and empty stock), TC-BF08-022 (Verify product creation fails due to oversized image, empty price, and invalid stock format), TC-BF08-025 (Verify Error on Product Management Page When Stock Field is Empty)
[✓] COV-UC302-BR1903: Image Constraint: Hình ảnh sản phẩm phải là JPEG hoặc PNG và không được vượt quá 3MB. (BR1903)
    - Covered by: TC-BF08-001 (Verify a seller can successfully add a new product after logging in - valid image), TC-BF08-002 (Verify error messages when editing a product with an invalid image format and empty required fields - invalid format), TC-BF08-003 (Verify that deleting a product fails when other product fields have validation errors - oversized image), TC-BF08-004 (Validate Product Creation Failure with Invalid Data (Image, Price, Stock) on ProductManagementPage - oversized image), TC-BF08-007 (Verify error messages for creating a product with empty name, invalid image, and invalid stock - invalid format), TC-BF08-008 (Verify error message on product creation with invalid image and price formats - invalid format), TC-BF08-009 (Verify Rejection Reason Display for Product Creation Failure due to Oversized Image and Missing Details - oversized image), TC-BF08-011 (Verify Failed Product Deletion with Conflicting Data Entry on Product Management Page - invalid format), TC-BF08-014 (Verify error messages when editing a product with empty stock and oversized image - oversized image), TC-BF08-015 (Seller Fails to Add New Product with Invalid Image and Empty Fields - invalid format), TC-BF08-017 (Verify Product Creation Fails with Invalid Image Format and Empty Price - invalid format), TC-BF08-019 (TC-BF08-019: Validate error messages for invalid price and oversized image during new product creation - oversized image), TC-BF08-020 (Verify failed product deletion due to invalid data in the product form on the Product Management Page - invalid format), TC-BF08-021 (Verify error handling and rejection reason display for product creation with invalid data formats - invalid format), TC-BF08-022 (Verify product creation fails due to oversized image, empty price, and invalid stock format - oversized image)