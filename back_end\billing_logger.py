#!/usr/bin/env python3
"""
Billing Logger for tracking daily costs of all modules
"""

import os
import csv
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from threading import Lock

class BillingLogger:
    """
    Class để ghi logs billing cost của các module vào CSV files theo ngày.
    Mỗi ngày sẽ có một file CSV riêng để tracking chi phí.
    """
    
    def __init__(self, log_dir: str = "back_end/logs/billing"):
        """
        Initialize BillingLogger.
        
        Args:
            log_dir (str): Thư mục để lưu billing logs
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self._lock = Lock()  # Thread safety cho concurrent access
        
    def _get_daily_log_file(self) -> Path:
        """
        Lấy đường dẫn file log theo ngày hiện tại.
        
        Returns:
            Path: Đường dẫn file CSV cho ngày hiện tại
        """
        today = datetime.now().strftime("%Y-%m-%d")
        return self.log_dir / f"{today}.csv"
    
    def _ensure_csv_headers(self, csv_file: Path) -> None:
        """
        Đảm bảo file CSV có headers đúng.
        
        Args:
            csv_file (Path): Đường dẫn file CSV
        """
        headers = [
            "Module", "Timestamp", "Model Type", "API Calls", 
            "Input Tokens", "Output Tokens", "Cache Tokens", 
            "API Cost (USD)", "Cache Cost (USD)", "Total Cost (USD)", "Notes"
        ]
        
        if not csv_file.exists():
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
    
    def _read_existing_data(self, csv_file: Path) -> list:
        """
        Đọc dữ liệu hiện có từ CSV file (không bao gồm total row).
        
        Args:
            csv_file (Path): Đường dẫn file CSV
            
        Returns:
            list: Danh sách các rows (không bao gồm total)
        """
        if not csv_file.exists():
            return []
            
        rows = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader, None)  # Skip headers
            
            for row in reader:
                # Skip total row if exists
                if row and len(row) > 0 and row[0] != "TOTAL":
                    rows.append(row)
        
        return rows
    
    def _calculate_totals(self, data_rows: list) -> Dict[str, float]:
        """
        Tính tổng từ tất cả các rows.
        
        Args:
            data_rows (list): Danh sách các data rows
            
        Returns:
            Dict[str, float]: Dictionary chứa các tổng số
        """
        totals = {
            "api_calls": 0,
            "input_tokens": 0,
            "output_tokens": 0,
            "cache_tokens": 0,
            "api_cost": 0.0,
            "cache_cost": 0.0,
            "total_cost": 0.0
        }
        
        for row in data_rows:
            if len(row) >= 10:  # Đảm bảo có đủ columns
                try:
                    totals["api_calls"] += int(row[3]) if row[3] else 0
                    totals["input_tokens"] += int(row[4]) if row[4] else 0
                    totals["output_tokens"] += int(row[5]) if row[5] else 0
                    totals["cache_tokens"] += int(row[6]) if row[6] else 0
                    totals["api_cost"] += float(row[7]) if row[7] else 0.0
                    totals["cache_cost"] += float(row[8]) if row[8] else 0.0
                    totals["total_cost"] += float(row[9]) if row[9] else 0.0
                except (ValueError, IndexError):
                    continue  # Skip invalid rows
        
        return totals
    
    def _write_csv_with_totals(self, csv_file: Path, data_rows: list, new_row: list) -> None:
        """
        Ghi lại toàn bộ CSV với total row ở cuối.
        
        Args:
            csv_file (Path): Đường dẫn file CSV
            data_rows (list): Các data rows hiện có
            new_row (list): Row mới cần thêm
        """
        headers = [
            "Module", "Timestamp", "Model Type", "API Calls", 
            "Input Tokens", "Output Tokens", "Cache Tokens", 
            "API Cost (USD)", "Cache Cost (USD)", "Total Cost (USD)", "Notes"
        ]
        
        # Add new row to existing data
        all_data_rows = data_rows + [new_row]
        
        # Calculate totals
        totals = self._calculate_totals(all_data_rows)
        
        # Create total row
        total_row = [
            "TOTAL",
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "ALL",
            totals["api_calls"],
            totals["input_tokens"], 
            totals["output_tokens"],
            totals["cache_tokens"],
            f"{totals['api_cost']:.6f}",
            f"{totals['cache_cost']:.6f}",
            f"{totals['total_cost']:.6f}",
            f"Daily total for {len(all_data_rows)} module executions"
        ]
        
        # Write everything to CSV
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerows(all_data_rows)
            writer.writerow(total_row)
    
    def log_module_cost(self, module_name: str, token_summary: Dict[str, Any]) -> None:
        """
        Ghi log cost của một module vào CSV file.
        
        Args:
            module_name (str): Tên module
            token_summary (Dict[str, Any]): Token summary từ module
        """
        if not token_summary.get("token_counting_enabled", False):
            return  # Skip if token counting is disabled
        
        with self._lock:  # Thread safety
            csv_file = self._get_daily_log_file()
            self._ensure_csv_headers(csv_file)
            
            # Read existing data
            existing_data = self._read_existing_data(csv_file)
            
            # Prepare new row data
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            model_type = token_summary.get("model_type", "Unknown").upper()
            api_calls = token_summary.get("api_call_count", 1)
            input_tokens = token_summary.get("total_input_tokens", 0)
            output_tokens = token_summary.get("total_output_tokens", 0)
            
            # Handle cache and costs
            cache_tokens = token_summary.get("cache_tokens", 0)
            cache_enabled = token_summary.get("cache_enabled", False)
            
            if cache_enabled and "combined_total_cost_usd" in token_summary:
                # With cache
                api_costs = token_summary.get("api_costs", {})
                cache_costs = token_summary.get("cache_costs", {})
                
                api_cost = api_costs.get("api_total_cost_usd", 0.0)
                cache_cost = cache_costs.get("cache_total_cost_usd", 0.0)
                total_cost = token_summary.get("combined_total_cost_usd", 0.0)
                notes = f"Cache: {cache_costs.get('storage_hours', 1.0):.1f}h"
                
                if model_type == "PRO" and 'tier' in cache_costs:
                    notes += f", {cache_costs['tier']}"
                elif model_type == "FLASH" and 'content_type' in cache_costs:
                    notes += f", {cache_costs['content_type']}"
            else:
                # Without cache
                api_cost = token_summary.get("total_cost_usd", 0.0)
                cache_cost = 0.0
                total_cost = api_cost
                notes = "No cache"
            
            # Create new row
            new_row = [
                module_name,
                timestamp,
                model_type,
                api_calls,
                input_tokens,
                output_tokens,
                cache_tokens,
                f"{api_cost:.6f}",
                f"{cache_cost:.6f}", 
                f"{total_cost:.6f}",
                notes
            ]
            
            # Write CSV with totals
            self._write_csv_with_totals(csv_file, existing_data, new_row)
            
            print(f"💾 Billing logged to: {csv_file}")
            print(f"📊 Module: {module_name}, Cost: ${total_cost:.6f}")
    
    def get_daily_summary(self, date: Optional[str] = None) -> Dict[str, Any]:
        """
        Lấy tổng kết chi phí cho một ngày.
        
        Args:
            date (str, optional): Ngày theo format YYYY-MM-DD. Mặc định là hôm nay.
            
        Returns:
            Dict[str, Any]: Tổng kết chi phí
        """
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        csv_file = self.log_dir / f"{date}.csv"
        
        if not csv_file.exists():
            return {"error": f"No billing data found for {date}"}
        
        data_rows = self._read_existing_data(csv_file)
        totals = self._calculate_totals(data_rows)
        
        return {
            "date": date,
            "total_executions": len(data_rows),
            "total_api_calls": totals["api_calls"],
            "total_input_tokens": totals["input_tokens"],
            "total_output_tokens": totals["output_tokens"],
            "total_cache_tokens": totals["cache_tokens"],
            "total_api_cost": totals["api_cost"],
            "total_cache_cost": totals["cache_cost"],
            "total_cost": totals["total_cost"],
            "csv_file": str(csv_file)
        }
    
    def print_daily_summary(self, date: Optional[str] = None) -> None:
        """
        In tổng kết chi phí cho một ngày.
        
        Args:
            date (str, optional): Ngày theo format YYYY-MM-DD. Mặc định là hôm nay.
        """
        summary = self.get_daily_summary(date)
        
        if "error" in summary:
            print(f"❌ {summary['error']}")
            return
        
        print(f"\n📊 DAILY BILLING SUMMARY - {summary['date']}")
        print("=" * 50)
        print(f"🔄 Total Executions: {summary['total_executions']}")
        print(f"📞 Total API Calls: {summary['total_api_calls']}")
        print(f"📥 Total Input Tokens: {summary['total_input_tokens']:,}")
        print(f"📤 Total Output Tokens: {summary['total_output_tokens']:,}")
        print(f"🗄️ Total Cache Tokens: {summary['total_cache_tokens']:,}")
        print(f"💰 Total Cost: ${summary['total_cost']:.6f} USD")
        print(f"  📡 API Cost: ${summary['total_api_cost']:.6f} USD")
        print(f"  🗄️ Cache Cost: ${summary['total_cache_cost']:.6f} USD")
        print(f"📁 Log File: {summary['csv_file']}")
        print("=" * 50)

# Global instance
billing_logger = BillingLogger()
