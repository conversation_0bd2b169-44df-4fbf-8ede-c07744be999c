import os
import re
from pathlib import Path
from back_end.BusinessFlowDetector import GenBusinessFlow
# from back_end.ExtractAndProcessToJson import DiagramProcessor
from back_end.test_case_evaluator import TestCaseEvaluator
from back_end.document_processor import DocumentProcessor
from back_end.path_to_csv import ScreenTestCaseGenerator
from back_end.relevant_content_processor import RelevantContentProcessor
from back_end.path_processor import ScreenPathGenerator
from back_end.gen_test_case import GenTestCaseBussinessFlow

# Get PDF path from user
pdf_path = input("Enter the PDF path: ")
pdf_path = Path(pdf_path)
pdf_name = pdf_path.stem
parent_dir = Path('./back_end/output')

print(f"Processing PDF: {pdf_name}")
print(f"Output directory: {parent_dir / pdf_name}")

# ===== PATH CONFIGURATION (NEW STRUCTURE) =====

# Base directories
document_processor_dir = parent_dir / pdf_name / 'document_processor'
merged_json = document_processor_dir / 'merged_output.json'
analyzed_json = document_processor_dir / 'description_output.json'

pdf_file = pdf_path
output_directory = parent_dir / pdf_name / 'diagrams'
config_path_flash = "back_end/document/gemini_config_flash.json"
config_path_pro = "back_end/document/gemini_config_pro.json"
doc_processor_file = document_processor_dir / 'merged_output.json'

# Business flows directory
output_business_flow_directory = parent_dir / pdf_name / 'business_flows'

# Relevant content processor directory (contains business_flow_X subdirs + shared files)
relevant_content_processor_dir = parent_dir / pdf_name / 'relevant_content_processor'
# Shared files at base level
screen_graph_json = relevant_content_processor_dir / 'screen_graph.json'

# Path processor directory (contains business_flow_X subdirs + shared files)
path_processor_dir = parent_dir / pdf_name / 'path_processor'
# Shared files at base level
variables_output_path = path_processor_dir / 'screen_variables.json'
graph_image_output_path = path_processor_dir / 'screen_flow_graph.png'

# Test case generator directory (will contain business_flow_X subdirs)
test_case_generator_dir = parent_dir / pdf_name / 'test_case_csv_generator'

# Final test case output directory (will contain business_flow_X subdirs)
test_case_output_dir = parent_dir / pdf_name / 'TestCase_Output'

print("✅ Path configuration completed")
print(f"📁 Main output directory: {parent_dir / pdf_name}")

processor = DocumentProcessor(
    pdf_path=pdf_file,
    use_auto_toc=True, 
    output_dir=str(document_processor_dir),
    merged_json=str(merged_json),
    config_file=config_path_flash,
    analyzed_json=str(analyzed_json),
    count_token=True
)

# Chạy xử lý với verbose output
processor.execute(verbose=False, skip_llm=False)

print("\n" + "="*60)

# Business flow generation
if not os.path.exists(doc_processor_file):
    print(f"❌ Error: Input file not found at {doc_processor_file}")
else:
    print("🔄 Starting business flow generation...")
    flow_generator = GenBusinessFlow(
        input_json_file=str(doc_processor_file),
        business_flow_dir=str(output_business_flow_directory),
        config_file=config_path_flash,
        count_token=True
    )
    generated_flows = flow_generator.execute()

    if generated_flows:
        print("✅ Successfully generated business flows.")
    else:
        print("❌ Failed to generate business flows or no flows were produced.")

# Relevant content processing (NEW STRUCTURE - processes all business flows)
print("🔄 Starting relevant content processing for all business flows...")
processor = RelevantContentProcessor(
    description_json=str(analyzed_json),
    merged_json=str(merged_json), 
    config_file=config_path_pro,
    business_flows_dir=str(output_business_flow_directory),  
    base_output_dir=str(relevant_content_processor_dir), 
    max_workers=5,
    enable_caching=True               
)
processor.execute_all_flows()
print("✅ Relevant content processing completed for all business flows")

# Screen path generation (NEW STRUCTURE - processes all business flows)
print("🔄 Starting screen path generation for all business flows...")
results = ScreenPathGenerator.process_all_business_flows(
    config_file=config_path_pro,
    relevant_content_base_dir=str(relevant_content_processor_dir),
    business_flows_dir=str(output_business_flow_directory),
    base_output_dir=str(path_processor_dir),
    count_token=True
)
print(f"✅ Screen path generation completed for {len(results)} business flows")

# Test case CSV generation (NEW STRUCTURE - processes all business flows)
print("🔄 Starting test case CSV generation for all business flows...")
csv_results = ScreenTestCaseGenerator.process_from_base_dir(
    base_dir=str(parent_dir / pdf_name),
    output_dir=str(test_case_generator_dir)
)
print(f"✅ Test case CSV generation completed for {len(csv_results)} business flows")

# Final test case generation (NEW STRUCTURE - processes all business flows)
print("🔄 Starting final test case generation for all business flows...")

# Use the new enhanced GenTestCaseBussinessFlow API
final_results = GenTestCaseBussinessFlow.process_from_base_dir(
    base_dir=str(parent_dir / pdf_name),
    output_dir=str(test_case_output_dir),
    config_file=config_path_pro,
    count_token=True
)

if final_results:
    print("✅ Final test case generation completed")
    print(f"📊 Processed {len(final_results)} business flows")
    
    total_json_files = 0
    successful_flows = [r for r in final_results if r['success']]
    
    for result in final_results:
        status = "✅" if result['success'] else "❌"
        bf_num = result['business_flow_number']
        print(f"  {status} Business Flow {bf_num}")
        
        if result['success']:
            # Fix: processed_files is a list, so get its length
            processed_files = result.get('processed_files', [])
            total_files = result.get('total_files', 0)
            
            # Count the number of processed files
            processed_count = len(processed_files) if isinstance(processed_files, list) else processed_files
            total_json_files += processed_count
            
            print(f"    📄 Generated {processed_count}/{total_files} JSON test case files")
            print(f"    📁 Output: {result['output_directory']}")
    
    print(f"\n🎉 Summary:")
    print(f"   📈 Total JSON test case files: {total_json_files}")
    print(f"   ✅ Successful business flows: {len(successful_flows)}/{len(final_results)}")
else:
    print("❌ Final test case generation failed")

print("\n✅ Final test case generation completed for all business flows")

# Test case evaluation
print("🔄 Starting test case evaluation...")
benchmark = "back_end/document/evaluation_benchmark.json"
evaluate_output = parent_dir / pdf_name / 'evaluate_output'

evaluator = TestCaseEvaluator(
    test_case_dir=str(test_case_output_dir),
    benchmark_file=benchmark,
    original_doc_file=str(merged_json),
    output_dir=str(evaluate_output),
    config_file=config_path_flash,
    rotate_api_key=True
)
evaluator.run()
print("✅ Test case evaluation completed")