1. Seller navigates to shop settings in Seller Dashboard
2. Seller updates profile information in Edit Profile Page
3. <PERSON>ller uploads images in Edit Profile Page
4. Seller clicks save changes button in Edit Profile Page
5. <PERSON><PERSON> navigates to product management page in Seller Products Page
6. Seller clicks Add Product button in Seller Products Page
7. Seller enters product information in Edit Product Page
8. Seller uploads images in Edit Product Page
9. Seller sets pricing details in Edit Product Page
10. <PERSON>ller sets inventory details in Edit Product Page
11. Seller clicks save button in Edit Product Page
12. Anonymous User navigates to Homepage
13. Anonymous User clicks "Sign Up" button in Homepage
14. Anonymous User enters email in Customer Registration Page
15. Anonymous User enters password in Customer Registration Page
16. Anonymous User confirms password in Customer Registration Page
17. Anonymous User enters first name in Customer Registration Page
18. Anonymous User enters last name in Customer Registration Page
19. Anonymous User clicks "Register" button in Customer Registration Page
20. Customer views product in Product Detail Page
21. Customer selects options in Product Detail Page
22. Customer specifies quantity in Product Detail Page
23. Customer clicks "Add to Cart" button in Product Detail Page
24. Customer navigates to checkout page from Shopping Cart Page
25. Customer enters shipping information in Checkout Page
26. Customer selects payment method in Checkout Page
27. Customer reviews order in Checkout Page
28. Customer confirms order placement in Checkout Page
29. Seller navigates to order management page in Seller Dashboard
30. Seller selects order to process in Seller Orders Page
31. Seller updates order status in Seller Order Details Page
32. Seller adds tracking information in Seller Order Details Page
33. Seller clicks save changes button in Seller Order Details Page
34. Customer navigates to 'My Account' section in User Profile Page
35. Customer clicks 'Order History' link in User Orders Page
36. Customer views orders in User Orders Page
37. Customer clicks on specific order in User Orders Page
38. Customer views order details in User Orders Page
39. Customer selects service in Create Booking Page
40. Customer selects date and time in Create Booking Page
41. Customer clicks "Submit Booking" button in Create Booking Page
42. Customer accesses booking history in Customer Bookings Page
43. Customer selects booking to cancel in Customer Bookings Page
44. Customer confirms cancellation in Booking Details Page
45. Seller navigates to bookings management page in Seller Dashboard
46. Seller selects booking to review in Seller Bookings Page
47. Seller updates booking status to 'Cancelled' in Seller Booking Details Page
48. Seller clicks save changes button in Seller Booking Details Page
49. Admin navigates to product oversight page in Admin Dashboard
50. Admin searches for products in Admin Products Page
51. Admin selects product in Admin Products Page
52. Admin clicks approve button in Admin Pending Products Page
Business Process Context: The interaction between a Customer and a Seller on the SASUCare platform represents a dynamic and continuous lifecycle, beginning long before a single purchase is made. Initially, a Seller establishes their digital presence by setting up a detailed shop pr ofile (UC -301) and meticulously populating their catalog with products, including images, descriptions, and accurate inventory levels (UC -302). A Customer then begins their journey by registering an account (UC -101) or logging in (UC -102), discovering the Seller's offerings, and adding a desired item to their shopping cart (UC -203). This culminates in the core transaction where the Customer completes the checkout process, providing payment and shipping details to finalize their order (UC -207). Immediately, the Seller is notified of the new sale and navigates their dashboard to manage the order (UC -305), updating its status from 'Processing' to 'Shipped' after dispatching the item and providing a tracking number, which the Customer can monitor through their o wn order history page (UC -208). Beyond this standard product flow, a different Customer might engage with the Seller's service offerings, viewing their availability and booking a specific appointment (UC -211). Should that Customer's plans change, they can then navigate back to their bookings to request a cancellation, which the Seller reviews and processes according to their stated policy (UC -212). Following these successful customer interactions, the Seller’s work continues as they might update their business information, refine product listings, or add entirely new items, which may require a brief period of pending approval before becoming visible to the public, e nsuring platform quality standards are met (reflecting UC -405). This entire ecosystem demonstrates a continuous loop of preparation, transaction, fulfillment, and ongoing management from both the Customer and Seller perspectives.
