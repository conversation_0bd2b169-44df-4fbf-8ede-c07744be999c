/**
 * LiveTerminal Component - Resizable Terminal with xterm.js
 * Real terminal using xterm.js library with real-time WebSocket connection
 */
import React, { useState, useRef, useCallback, useEffect } from "react";
import {
    Search,
    Copy,
    Download,
    Maximize2,
    Wifi,
    Move,
    Refresh<PERSON>w,
    Zap,
} from "lucide-react";
import { Terminal } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import { SearchAddon } from "xterm-addon-search";
import { WebLinksAddon } from "xterm-addon-web-links";
import { useWebSocket, ConnectionStatus } from "../hooks/useWebSocket";
import "xterm/css/xterm.css";

interface LiveTerminalProps {
    height?: number;
    onResize?: (height: number) => void;
    className?: string;
}

export const LiveTerminal: React.FC<LiveTerminalProps> = ({
    height: initialHeight = 300,
    onResize,
    className = "",
}) => {
    const [height, setHeight] = useState(initialHeight);
    const [isResizing, setIsResizing] = useState(false);
    const startY = useRef(0);
    const startHeight = useRef(0);
    const terminalRef = useRef<HTMLDivElement>(null);
    const xtermRef = useRef<Terminal | null>(null);
    const fitAddonRef = useRef<FitAddon | null>(null);
    const searchAddonRef = useRef<SearchAddon | null>(null);

    // WebSocket connection for real-time updates
    const { status, sendMessage, reconnect } = useWebSocket({
        initialDelay: 3000, // Wait 3 seconds before initial connection
        onMessage: (message) => {
            handleWebSocketMessage(message);
        },
        onStatusChange: (newStatus) => {
            handleConnectionStatusChange(newStatus);
        },
    });

    // Handle WebSocket messages with delay for better UX
    const handleWebSocketMessage = useCallback(
        (message: any) => {
            if (!xtermRef.current) return;

            const terminal = xtermRef.current;
            const timestamp = new Date().toLocaleTimeString();

            // Add 300ms delay between log messages for better readability
            setTimeout(() => {
                if (!xtermRef.current) return; // Check again after delay

                switch (message.type) {
                    case "connection_status":
                        // Skip connection status messages - handled by status change callback
                        break;

                    case "pipeline_status":
                        // Skip pipeline status updates - handled by Inspector component
                        break;

                    case "pipeline_log":
                        // Show important pipeline log messages (INFO, WARNING, ERROR)
                        const logLevel = message.data.level?.toUpperCase();
                        if (
                            logLevel === "INFO" ||
                            logLevel === "WARNING" ||
                            logLevel === "ERROR"
                        ) {
                            const logIcon = getLogIcon(message.data.level);
                            const logMessage = message.data.message;
                            terminal.writeln(
                                `\r\n[${timestamp}] ${logIcon} ${logLevel}: ${logMessage}`
                            );
                        }
                        break;

                    case "root_log":
                        // Display Python root logger messages directly (backend handles delay)
                        const rootLogMessage = message.data.message;
                        console.log(
                            `Received root log: ${rootLogMessage.substring(
                                0,
                                50
                            )}...`
                        );
                        terminal.writeln(`\r\n${rootLogMessage}`);
                        break;

                    case "pipeline_step_update":
                        const stepIcon = getStatusIcon(message.data.status);
                        terminal.writeln(
                            `\r\n[${timestamp}] ${stepIcon} ${
                                message.data.name
                            }: ${message.data.status.toUpperCase()}`
                        );
                        break;

                    case "log_message":
                        const logIcon = getLogIcon(message.data.level);
                        terminal.writeln(
                            `\r\n[${timestamp}] ${logIcon} ${message.data.message}`
                        );
                        break;

                    case "ping":
                        // Respond to server ping
                        sendMessage({ type: "pong" });
                        break;

                    case "pong":
                        terminal.writeln(
                            `\r\n[${timestamp}] 💓 Server heartbeat received`
                        );
                        break;

                    case "client_connected":
                    case "client_disconnected":
                        // Skip client connection messages - not relevant for user
                        break;

                    default:
                        terminal.writeln(
                            `\r\n[${timestamp}] 📨 ${
                                message.type
                            }: ${JSON.stringify(message.data)}`
                        );
                }

                // Don't add prompt here - let individual handlers manage prompts
            }, 300); // 300ms delay
        },
        [sendMessage]
    );

    // Test WebSocket connection
    const handleTestConnection = useCallback(() => {
        if (!xtermRef.current) return;

        const terminal = xtermRef.current;
        const timestamp = new Date().toLocaleTimeString();

        terminal.writeln(
            `\r\n[${timestamp}] 🧪 Testing WebSocket connection...`
        );

        // Send test message
        sendMessage({
            type: "test_message",
            data: {
                message: "Hello from frontend!",
                timestamp: new Date().toISOString(),
            },
        });

        terminal.write("$ ");
    }, [sendMessage]);

    // Handle connection status changes
    const handleConnectionStatusChange = useCallback(
        (newStatus: ConnectionStatus) => {
            if (!xtermRef.current) return;

            const terminal = xtermRef.current;
            const timestamp = new Date().toLocaleTimeString();

            switch (newStatus) {
                case "connecting":
                    terminal.writeln(
                        `\r\n[${timestamp}] 🔄 Connecting to Phoenix Pipeline Server...`
                    );
                    terminal.write("$ ");
                    break;
                case "connected":
                    terminal.writeln(
                        `\r\n[${timestamp}] ✅ Connected to Phoenix Pipeline Server`
                    );
                    terminal.write("$ ");
                    // Delay second message for better UX
                    setTimeout(() => {
                        if (xtermRef.current) {
                            xtermRef.current.writeln(
                                `[${timestamp}] 🚀 Ready to receive pipeline updates`
                            );
                            xtermRef.current.write("$ ");
                        }
                    }, 300);
                    break;
                case "disconnected":
                    terminal.writeln(
                        `\r\n[${timestamp}] ❌ Disconnected from server`
                    );
                    terminal.write("$ ");
                    break;
                case "error":
                    terminal.writeln(
                        `\r\n[${timestamp}] 🚨 Connection error - retrying...`
                    );
                    terminal.write("$ ");
                    break;
            }
        },
        []
    );

    // Helper functions for status icons
    const getStatusIcon = (status: string): string => {
        switch (status.toLowerCase()) {
            case "completed":
                return "✅";
            case "running":
                return "⏳";
            case "failed":
                return "❌";
            case "pending":
                return "⏸️";
            case "warning":
                return "⚠️";
            case "paused":
                return "⏸️";
            case "cancelled":
                return "🚫";
            default:
                return "📄";
        }
    };

    const getLogIcon = (level: string): string => {
        switch (level.toLowerCase()) {
            case "error":
                return "🚨";
            case "warning":
                return "⚠️";
            case "info":
                return "ℹ️";
            case "debug":
                return "🐛";
            default:
                return "📝";
        }
    };

    // Get WiFi icon color and animation based on connection status
    const getWifiColor = (): string => {
        switch (status) {
            case "connected":
                return "text-green-500 wifi-connected";
            case "connecting":
                return "text-yellow-500 wifi-connecting";
            case "disconnected":
                return "text-red-500";
            case "error":
                return "text-red-600 wifi-error";
            default:
                return "text-gray-500";
        }
    };

    // Get status text
    const getStatusText = (): string => {
        switch (status) {
            case "connected":
                return "Connected";
            case "connecting":
                return "Connecting...";
            case "disconnected":
                return "Disconnected";
            case "error":
                return "Connection Error";
            default:
                return "Unknown";
        }
    };

    // Initialize xterm.js terminal
    useEffect(() => {
        if (!terminalRef.current) return;

        // Create terminal instance
        const terminal = new Terminal({
            theme: {
                background: "#000000",
                foreground: "#00ff00",
                cursor: "#00ff00",
                cursorAccent: "#000000",
                selectionBackground: "rgba(255, 255, 255, 0.3)",
            },
            fontSize: 14,
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            cursorBlink: true,
            allowTransparency: true,
        });

        // Create addons
        const fitAddon = new FitAddon();
        const searchAddon = new SearchAddon();
        const webLinksAddon = new WebLinksAddon();

        // Load addons
        terminal.loadAddon(fitAddon);
        terminal.loadAddon(searchAddon);
        terminal.loadAddon(webLinksAddon);

        // Open terminal
        terminal.open(terminalRef.current);

        // Store references
        xtermRef.current = terminal;
        fitAddonRef.current = fitAddon;
        searchAddonRef.current = searchAddon;

        // Initial content with countdown
        terminal.writeln("🚀 Phoenix Pipeline Terminal");
        terminal.writeln("Initializing connection in 3 seconds...");
        terminal.write("$ ");

        // Countdown before connection attempt
        let countdown = 3;
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                terminal.writeln(`Connecting in ${countdown}...`);
                terminal.write("$ ");
            } else {
                terminal.writeln("Attempting connection...");
                terminal.write("$ ");
                clearInterval(countdownInterval);

                // Clear terminal and show cool banner after countdown
                setTimeout(() => {
                    terminal.clear();

                    // Cool ASCII banner
                    const banner = [
                        "╔══════════════════════════════════════════════════════════════╗",
                        "║                                                              ║",
                        "║    ██████╗ ██╗  ██╗ ██████╗ ███████╗███╗   ██╗██╗██╗  ██╗    ║",
                        "║    ██╔══██╗██║  ██║██╔═══██╗██╔════╝████╗  ██║██║╚██╗██╔╝    ║",
                        "║    ██████╔╝███████║██║   ██║█████╗  ██╔██╗ ██║██║ ╚███╔╝     ║",
                        "║    ██╔═══╝ ██╔══██║██║   ██║██╔══╝  ██║╚██╗██║██║ ██╔██╗     ║",
                        "║    ██║     ██║  ██║╚██████╔╝███████╗██║ ╚████║██║██╔╝ ██╗    ║",
                        "║    ╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝╚═╝╚═╝  ╚═╝    ║",
                        "║                                                              ║",
                        "║                    🔥 PIPELINE TERMINAL 🔥                     ║",
                        "║                                                              ║",
                        "║              ⚡ Real-time Pipeline Monitoring ⚡               ║",
                        "║                                                              ║",
                        "║                       Status: STARTED                        ║",
                        "║                                                              ║",
                        "╚══════════════════════════════════════════════════════════════╝",
                    ];

                    banner.forEach((line, index) => {
                        setTimeout(() => {
                            terminal.writeln(line);
                            if (index === banner.length - 1) {
                                terminal.writeln("");
                                terminal.write("$ ");
                            }
                        }, index * 50); // Animate each line with 50ms delay
                    });
                }, 500); // Wait 500ms after countdown before clearing
            }
        }, 1000);

        // Fit terminal to container
        fitAddon.fit();

        // Cleanup
        return () => {
            terminal.dispose();
        };
    }, []);

    // Handle resize
    const handleMouseDown = useCallback(
        (e: React.MouseEvent) => {
            setIsResizing(true);
            startY.current = e.clientY;
            startHeight.current = height;

            const handleMouseMove = (e: MouseEvent) => {
                const deltaY = startY.current - e.clientY;
                const newHeight = Math.max(
                    150,
                    Math.min(800, startHeight.current + deltaY)
                );

                setHeight(newHeight);
                onResize?.(newHeight);

                // Fit terminal after resize
                setTimeout(() => {
                    fitAddonRef.current?.fit();
                }, 0);
            };

            const handleMouseUp = () => {
                setIsResizing(false);
                document.removeEventListener("mousemove", handleMouseMove);
                document.removeEventListener("mouseup", handleMouseUp);
            };

            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
            e.preventDefault();
        },
        [height, onResize]
    );

    // Fit terminal when height changes
    useEffect(() => {
        if (fitAddonRef.current) {
            setTimeout(() => {
                fitAddonRef.current?.fit();
            }, 100);
        }
    }, [height]);

    // Handle button actions
    const handleSearch = useCallback(() => {
        // You can implement search functionality here
        console.log("Search in terminal");
    }, []);

    const handleCopy = useCallback(() => {
        if (xtermRef.current) {
            const selection = xtermRef.current.getSelection();
            if (selection) {
                navigator.clipboard.writeText(selection);
                console.log("Copied to clipboard:", selection);
            }
        }
    }, []);

    const handleDownload = useCallback(() => {
        if (xtermRef.current) {
            const content =
                xtermRef.current.buffer.active
                    .getLine(0)
                    ?.translateToString() || "";
            const blob = new Blob([content], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "terminal-output.txt";
            a.click();
            URL.revokeObjectURL(url);
        }
    }, []);

    const handleMaximize = useCallback(() => {
        const newHeight = height === 600 ? 300 : 600;
        setHeight(newHeight);
        onResize?.(newHeight);
    }, [height, onResize]);

    return (
        <div
            className={`bg-gray-900 border border-gray-700 rounded-lg ${className} ${
                isResizing ? "select-none" : ""
            }`}
        >
            {/* Resize Handle */}
            <div
                className="h-3 bg-gray-700 hover:bg-blue-500 cursor-row-resize transition-colors relative group flex items-center justify-center border-b border-gray-600"
                onMouseDown={handleMouseDown}
                style={{ userSelect: "none" }}
            >
                <Move className="w-4 h-4 text-gray-400 group-hover:text-blue-300 rotate-90" />
                <div className="absolute inset-0 -top-1 -bottom-1" />
            </div>

            {/* Terminal Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <Wifi
                            className={`w-4 h-4 ${getWifiColor()} transition-colors duration-300`}
                        />
                        <span className="text-sm text-gray-300">Terminal</span>
                    </div>
                    <div className="text-xs text-gray-500">
                        {getStatusText()} • Height: {height}px
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <button
                        onClick={handleTestConnection}
                        className="p-1.5 text-blue-400 hover:text-blue-300 hover:bg-gray-700 rounded transition-colors"
                        title="Test WebSocket connection"
                        disabled={status !== "connected"}
                    >
                        <Zap className="w-4 h-4" />
                    </button>
                    <button
                        onClick={reconnect}
                        className={`p-1.5 rounded transition-colors ${
                            status === "connected"
                                ? "text-green-400 hover:text-green-300 hover:bg-gray-700"
                                : "text-yellow-400 hover:text-yellow-300 hover:bg-gray-700"
                        }`}
                        title="Reconnect to server"
                    >
                        <RefreshCw
                            className={`w-4 h-4 ${
                                status === "connecting" ? "animate-spin" : ""
                            }`}
                        />
                    </button>
                    <button
                        onClick={handleSearch}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Search in terminal"
                    >
                        <Search className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleCopy}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Copy selection"
                    >
                        <Copy className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleDownload}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Download terminal output"
                    >
                        <Download className="w-4 h-4" />
                    </button>
                    <button
                        onClick={handleMaximize}
                        className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                        title="Toggle maximize"
                    >
                        <Maximize2 className="w-4 h-4" />
                    </button>
                </div>
            </div>

            {/* Terminal Content - xterm.js */}
            <div
                ref={terminalRef}
                className="bg-black"
                style={{ height: `${height}px` }}
            />
        </div>
    );
};
