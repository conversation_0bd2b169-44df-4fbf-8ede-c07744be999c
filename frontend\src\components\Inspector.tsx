/**
 * Inspector Component - UI Only
 * Static artifact viewer display without logic
 */
import React from "react";
import { Eye, FileText, Download } from "lucide-react";

export const Inspector: React.FC = () => {
    return (
        <div className="h-full bg-gray-900 border-l border-gray-700 flex">
            {/* Artifact List */}
            <div className="w-80 border-r border-gray-700 flex flex-col">
                <div className="p-4 border-b border-gray-700">
                    <h2 className="text-white font-medium flex items-center gap-2">
                        <Eye className="w-4 h-4" />
                        Artifacts (2)
                    </h2>
                    <p className="text-gray-400 text-sm mt-1">
                        Step: sample-step
                    </p>
                </div>

                <div className="flex-1 overflow-y-auto">
                    <div className="p-2">
                        <button className="w-full text-left p-3 rounded-lg mb-2 transition-colors bg-blue-600 text-white">
                            <div className="flex items-center gap-2">
                                <FileText className="w-4 h-4 flex-shrink-0" />
                                <span className="truncate font-medium">
                                    output.txt
                                </span>
                            </div>
                            <div className="text-xs text-gray-300 mt-1">
                                text • 1,234 chars
                            </div>
                        </button>
                        
                        <button className="w-full text-left p-3 rounded-lg mb-2 transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700">
                            <div className="flex items-center gap-2">
                                <FileText className="w-4 h-4 flex-shrink-0" />
                                <span className="truncate font-medium">
                                    result.json
                                </span>
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                                text • 567 chars
                            </div>
                        </button>
                    </div>
                </div>
            </div>

            {/* Artifact Content */}
            <div className="flex-1 p-4">
                <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-white font-medium">
                            output.txt
                        </h3>
                        <button className="flex items-center gap-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                            <Download className="w-4 h-4" />
                            Download
                        </button>
                    </div>
                    
                    <div className="flex-1 bg-gray-800 rounded border border-gray-600 p-4 overflow-auto">
                        <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                            Sample output content...
                            This is a static display of artifact content.
                            No dynamic loading or processing.
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    );
};
