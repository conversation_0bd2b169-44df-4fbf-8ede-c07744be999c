{"screen_definitions": [{"screen_name": "Homepage", "variables": {}}, {"screen_name": "LoginPage", "variables": {"username": {"valid": ["valid_username"], "invalid": [""]}, "password": {"valid": ["valid_password"], "invalid": [""]}, "rememberMe": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["<PERSON><PERSON><PERSON>"], "invalid": []}}}, {"screen_name": "RegistrationPage", "variables": {"fullName": {"valid": ["valid_string"], "invalid": [""]}, "username": {"valid": ["valid_string"], "invalid": [""]}, "email": {"valid": ["valid_email_format"], "invalid": ["", "invalid_email_format"]}, "password": {"valid": ["password_ge_8_chars"], "invalid": ["", "password_lt_8_chars"]}, "confirmPassword": {"valid": ["matches_password"], "invalid": ["", "does_not_match_password"]}, "termsConsent": {"valid": ["checked"], "invalid": ["unchecked"]}, "button_action": {"valid": ["<PERSON><PERSON><PERSON> ký tài k<PERSON>n"], "invalid": []}}}], "navigation_rules": {"(Homepage, LoginPage)": "loginButton", "(Homepage, RegistrationPage)": "registerButton", "(LoginPage, RegistrationPage)": "registerLink", "(RegistrationPage, LoginPage)": "submitRegistration or loginLink", "(LoginPage, StaffDashboardPage)": "submitLogin", "(LoginPage, ManagerDashboardPage)": "submitLogin", "(LoginPage, DirectorDashboardPage)": "submitLogin", "(LoginPage, CEODashboardPage)": "submitLogin", "(StaffDashboardPage, RequestListScreen_Training)": "trainingRequestsLink", "(StaffDashboardPage, CreateTrainingRequestScreen)": "createTrainingRequestLink", "(StaffDashboardPage, RoleChangeRequestScreen_StaffView)": "roleChangeRequestsLink", "(StaffDashboardPage, ProfileScreen)": "profileLink", "(StaffDashboardPage, LoginPage)": "logoutLink", "(RequestListScreen_Training, RequestDetailScreen_Training)": "viewDetailAction", "(RequestListScreen_Training, StaffDashboardPage)": "dashboardLink", "(RequestListScreen_Training, LoginPage)": "logoutLink", "(CreateTrainingRequestScreen, RequestListScreen_Training)": "submitTrainingRequest", "(CreateTrainingRequestScreen, StaffDashboardPage)": "cancelCreateRequest", "(CreateTrainingRequestScreen, LoginPage)": "logoutLink", "(RequestDetailScreen_Training, RequestListScreen_Training)": "backButton", "(ProfileScreen, StaffDashboardPage)": "dashboardLink", "(ProfileScreen, LoginPage)": "logoutLink", "(ManagerDashboardPage, ManageTrainingRequestsListPage)": "trainingRequestsLink", "(ManagerDashboardPage, TrainingRequestHistoryPage)": null, "(ManagerDashboardPage, ProfileScreen)": "profileLink", "(ManagerDashboardPage, LoginPage)": "logoutLink", "(ManageTrainingRequestsListPage, RequestDetailScreen_Training)": "viewDetailAction", "(ManageTrainingRequestsListPage, ManagerDashboardPage)": "dashboardLink", "(RequestDetailScreen_Training, ManageTrainingRequestsListPage)": "backButton", "(TrainingRequestHistoryPage, ManagerDashboardPage)": "dashboardLink", "(DirectorDashboardPage, RequestListScreen_Training)": "trainingRequestsLink", "(DirectorDashboardPage, ProfileScreen)": "profileLink", "(DirectorDashboardPage, LoginPage)": "logoutLink", "(RequestListScreen_Training, DirectorDashboardPage)": "dashboardLink", "(ProfileScreen, DirectorDashboardPage)": "dashboardLink", "(CEODashboardPage, RequestListScreen_Training)": "trainingRequestsLink", "(CEODashboardPage, ApproveRoleChangeRequestScreen)": "approveRoleChangesLink", "(CEODashboardPage, ProfileScreen)": "profileLink", "(CEODashboardPage, LoginPage)": "logoutLink", "(RequestListScreen_Training, CEODashboardPage)": "dashboardLink", "(ApproveRoleChangeRequestScreen, RoleChangeRequestDetailsModal)": "processRequestButton", "(ApproveRoleChangeRequestScreen, CEODashboardPage)": "dashboardLink", "(ApproveRoleChangeRequestScreen, LoginPage)": "logoutLink", "(RoleChangeRequestDetailsModal, ApproveRoleChangeRequestScreen)": "submitReviewDecision", "(ProfileScreen, CEODashboardPage)": "dashboardLink"}}