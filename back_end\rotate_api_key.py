import json
import threading
import queue
from typing import List

class APIKeyRotator:
    def __init__(self, config_path: str = "back_end/document/gemini_config_flash.json", state_path: str = "back_end/document/api_key_state.json"):
        self.config_path = config_path
        self.state_path = state_path
        self.lock = threading.Lock()
        self.api_keys = self._load_api_keys()
        self.key_queue = queue.Queue()

        # Load trạng thái queue từ file nếu có
        self._load_queue_state()

    def _load_api_keys(self) -> List[str]:
        """Load API keys từ file JSON."""
        with open(self.config_path, 'r') as file:
            data = json.load(file)
        keys = [v for k, v in sorted(data.items()) if k.startswith("api_key_")]
        if not keys:
            raise ValueError("Không tìm thấy API keys trong file cấu hình.")
        return keys

    def _load_queue_state(self):
        """Load trạng thái hàng đợi từ file hoặc khởi tạo mới."""
        try:
            with open(self.state_path, "r") as file:
                state = json.load(file)
                saved_keys = state.get("api_key_queue", [])

                # Nếu danh sách đã lưu hợp lệ, sử dụng nó
                if set(saved_keys) == set(self.api_keys):
                    for key in saved_keys:
                        self.key_queue.put(key)
                else:
                    raise ValueError("API keys trong state không khớp với cấu hình.")
        except (FileNotFoundError, ValueError):
            # Nếu lỗi hoặc file không tồn tại, khởi tạo queue từ đầu
            for key in self.api_keys:
                self.key_queue.put(key)

    def _save_queue_state(self):
        """Lưu trạng thái hàng đợi vào file."""
        with open(self.state_path, "w") as file:
            json.dump({"api_key_queue": list(self.key_queue.queue)}, file)

    def get_api_key(self) -> str:
        """Lấy API key hiện tại mà không thay đổi trạng thái."""
        with self.lock:
            return self.key_queue.queue[0]  # Lấy API key đầu tiên nhưng không thay đổi thứ tự

    def rotate_api_key(self):
        """Xoay vòng API key bằng cách lấy key tiếp theo từ hàng đợi."""
        with self.lock:
            old_key = self.key_queue.get()  # Lấy API key hiện tại ra khỏi hàng đợi
            self.key_queue.put(old_key)  # Đưa nó vào cuối hàng đợi
            self._save_queue_state()  # Lưu trạng thái mới vào file


