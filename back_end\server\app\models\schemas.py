"""
Data Models for Phoenix Pipeline Server
Pydantic models for type safety and validation
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Literal
from datetime import datetime
from enum import Enum

class ConnectionStatus(str, Enum):
    """WebSocket connection status"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"

class PipelineStepStatus(str, Enum):
    """Pipeline step execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    WARNING = "warning"
    PAUSED = "paused"
    CANCELLED = "cancelled"

class LogLevel(str, Enum):
    """Log message levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class PipelineStep(BaseModel):
    """Individual pipeline step model"""
    id: str = Field(..., description="Unique step identifier")
    name: str = Field(..., description="Human-readable step name")
    number: int = Field(..., description="Step order number")
    status: PipelineStepStatus = Field(default=PipelineStepStatus.PENDING)
    start_time: Optional[datetime] = Field(default=None)
    end_time: Optional[datetime] = Field(default=None)
    duration: Optional[float] = Field(default=None, description="Duration in seconds")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="Progress percentage")
    error_message: Optional[str] = Field(default=None)
    output_files: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PipelineStatus(BaseModel):
    """Overall pipeline status model"""
    pipeline_id: str = Field(..., description="Unique pipeline execution ID")
    document_id: Optional[str] = Field(default=None)
    status: PipelineStepStatus = Field(default=PipelineStepStatus.PENDING)
    current_step: Optional[str] = Field(default=None)
    steps: List[PipelineStep] = Field(default_factory=list)
    start_time: Optional[datetime] = Field(default=None)
    end_time: Optional[datetime] = Field(default=None)
    total_duration: Optional[float] = Field(default=None)
    overall_progress: float = Field(default=0.0, ge=0.0, le=100.0)
    error_count: int = Field(default=0)
    warning_count: int = Field(default=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def get_step_by_id(self, step_id: str) -> Optional[PipelineStep]:
        """Get a step by its ID"""
        for step in self.steps:
            if step.id == step_id:
                return step
        return None

    def update_step_status(self, step_id: str, status: PipelineStepStatus, **kwargs):
        """Update a step's status and other properties"""
        step = self.get_step_by_id(step_id)
        if step:
            step.status = status
            for key, value in kwargs.items():
                if hasattr(step, key):
                    setattr(step, key, value)

    def calculate_overall_progress(self) -> float:
        """Calculate overall pipeline progress"""
        if not self.steps:
            return 0.0
        
        total_progress = sum(step.progress for step in self.steps)
        return total_progress / len(self.steps)

class LogMessage(BaseModel):
    """Log message model"""
    timestamp: datetime = Field(default_factory=datetime.now)
    level: LogLevel = Field(...)
    message: str = Field(...)
    step_id: Optional[str] = Field(default=None)
    pipeline_id: Optional[str] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class WebSocketMessage(BaseModel):
    """Base WebSocket message model"""
    type: str = Field(...)
    data: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)
    client_id: Optional[str] = Field(default=None)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Client Message Types
class PingMessage(BaseModel):
    """Ping message from client"""
    type: Literal["ping"] = "ping"

class PipelineStartMessage(BaseModel):
    """Start pipeline message from client"""
    type: Literal["pipeline_start"] = "pipeline_start"
    data: Dict[str, str] = Field(..., description="Must contain document_id")

class PipelineStopMessage(BaseModel):
    """Stop pipeline message from client"""
    type: Literal["pipeline_stop"] = "pipeline_stop"

class GetStatusMessage(BaseModel):
    """Get status message from client"""
    type: Literal["get_status"] = "get_status"

# Server Message Types
class PongMessage(BaseModel):
    """Pong response to client"""
    type: Literal["pong"] = "pong"
    data: Dict[str, str] = Field(default_factory=dict)

class ConnectionStatusMessage(BaseModel):
    """Connection status message to client"""
    type: Literal["connection_status"] = "connection_status"
    data: Dict[str, Any] = Field(...)

class PipelineStatusMessage(BaseModel):
    """Pipeline status message to client"""
    type: Literal["pipeline_status"] = "pipeline_status"
    data: PipelineStatus = Field(...)

class PipelineLogMessage(BaseModel):
    """Pipeline log message to client"""
    type: Literal["pipeline_log"] = "pipeline_log"
    data: LogMessage = Field(...)

class ErrorMessage(BaseModel):
    """Error message to client"""
    type: Literal["error"] = "error"
    data: Dict[str, str] = Field(...)

# Default pipeline steps configuration
DEFAULT_PIPELINE_STEPS = [
    {
        "id": "document_processor",
        "name": "Document Processor",
        "number": 1
    },
    {
        "id": "business_flow_detector", 
        "name": "Business Flow Detector",
        "number": 2
    },
    {
        "id": "extract_and_process_to_json",
        "name": "Extract and Process to JSON", 
        "number": 3
    },
    {
        "id": "gen_html_structure_uidl",
        "name": "Generate HTML Structure UIDL",
        "number": 4
    },
    {
        "id": "gen_test_case",
        "name": "Generate Test Case",
        "number": 5
    },
    {
        "id": "test_case_evaluator",
        "name": "Test Case Evaluator",
        "number": 6
    }
]

# Request/Response Models
class PipelineStartRequest(BaseModel):
    """Request model for starting pipeline"""
    document_id: str = Field(..., description="Document ID to process")

class PipelineResponse(BaseModel):
    """Standard API response model"""
    success: bool = Field(...)
    message: Optional[str] = Field(default=None)
    data: Optional[Dict[str, Any]] = Field(default=None)
    timestamp: str = Field(...)

def create_default_pipeline_status(pipeline_id: str, document_id: Optional[str] = None) -> PipelineStatus:
    """Create a default pipeline status with all steps"""
    steps = [
        PipelineStep(
            id=step_config["id"],
            name=step_config["name"],
            number=step_config["number"]
        )
        for step_config in DEFAULT_PIPELINE_STEPS
    ]

    return PipelineStatus(
        pipeline_id=pipeline_id,
        document_id=document_id,
        steps=steps
    )
