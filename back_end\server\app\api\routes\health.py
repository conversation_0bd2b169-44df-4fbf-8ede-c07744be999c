"""
Health Check Routes
System health and monitoring endpoints
"""

from fastapi import APIRouter
from datetime import datetime
from app.core.config import settings

router = APIRouter()

@router.get("/")
async def health_check():
    """Detailed health check for monitoring"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": "development" if settings.DEBUG else "production"
    }

@router.get("/simple")
async def simple_health_check():
    """Simple health check for load balancers"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }
