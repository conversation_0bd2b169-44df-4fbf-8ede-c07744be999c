import json
import csv
import os
import re # Still needed for parsing rule keys
from allpairspy import AllPairs
from typing import List, Dict, Any, Tu<PERSON>, Optional
from itertools import product
import copy
import glob

class ScreenTestCaseGenerator:
    """
    Generates test case CSV files from screen paths and variables.

    Supports both single business flow processing (backward compatibility) and
    multiple business flow processing (new structure).

    New Structure Usage:
        # Process all business flows automatically
        results = ScreenTestCaseGenerator.process_from_base_dir(
            base_dir="path/to/base",
            output_dir="path/to/output"
        )

        # Or process specific business flow
        generator = ScreenTestCaseGenerator.create_for_business_flow(
            path_processor_base_dir="path/to/path_processor",
            business_flow_number=1,
            base_output_dir="path/to/output"
        )
        generator.execute()

    Old Structure Usage (backward compatibility):
        generator = ScreenTestCaseGenerator(
            path_file="path/to/screen_paths.json",
            screen_file="path/to/screen_variables.json",
            output_directory="path/to/output"
        )
        generator.execute()
    """

    def __init__(self, path_file: str, screen_file: str, output_directory: str, generated_flows_file: str = None,
                 business_flow_number: int = None):
        """
        Initialize the class with file paths.

        Args:
            path_file: Path to the screen paths JSON file.
            screen_file: Path to the screen variables JSON file.
            output_directory: Directory to output CSV files.
            generated_flows_file: Optional path to generated flows JSON file.
            business_flow_number: Optional business flow number for logging.
        """
        self.path_file = path_file
        self.screen_file = screen_file
        self.output_directory = output_directory
        self.generated_flows_file = generated_flows_file
        self.business_flow_number = business_flow_number
        self.screen_definitions: Dict[str, Dict[str, Any]] = {}
        self.navigation_rules: Dict[Tuple[str, str], str] = {} # Store parsed rules
        self.use_case_flows_list: List[Dict[str, Any]] = [] # List of use case flows from generated_flows.json
        self._load_definitions_and_rules() # Load data in constructor
        self._load_use_case_flows() # Load use case flows mapping

    def _load_definitions_and_rules(self):
        """Loads screen definitions and navigation rules from files."""
        try:
            # Load screen definitions
            with open(self.screen_file, "r", encoding="utf-8") as f:
                screen_data = json.load(f)
                screen_list = screen_data.get("screen_definitions", [])
                for screen in screen_list:
                    screen_name = screen.get("screen_name")
                    if screen_name:
                        if "variables" not in screen:
                            screen["variables"] = {}
                        self.screen_definitions[screen_name] = screen
                print(f"Loaded {len(self.screen_definitions)} screen definitions.")

                # Load and parse navigation rules
                nav_rules_raw = screen_data.get("navigation_rules", {})
                for key_str, button_name in nav_rules_raw.items():
                    # Use regex to extract screen names from keys like "(ScreenA, ScreenB)"
                    match = re.match(r'\(\s*"?([^",]+)"?\s*,\s*"?([^",]+)"?\s*\)', key_str)
                    if match:
                        source, target = match.groups()
                        self.navigation_rules[(source.strip(), target.strip())] = button_name
                    else:
                        print(f"Warning: Could not parse navigation rule key: '{key_str}'")
                print(f"Loaded {len(self.navigation_rules)} navigation rules.")

        except FileNotFoundError:
             print(f"Error: Screen/variables file not found at '{self.screen_file}'")
             self.screen_definitions = {}
             self.navigation_rules = {}
        except json.JSONDecodeError:
            print(f"Error: Could not decode JSON from screen/variables file '{self.screen_file}'")
            self.screen_definitions = {}
            self.navigation_rules = {}
        except Exception as e:
            print(f"Error reading screen/variables file ({self.screen_file}): {e}")
            self.screen_definitions = {}
            self.navigation_rules = {}

    def _load_use_case_flows(self):
        """Loads use case flows to enable number-based filename generation."""
        if not self.generated_flows_file:
            # Try to find generated_flows.json in the same directory as path_file
            path_dir = os.path.dirname(self.path_file)
            potential_flows_file = os.path.join(path_dir, "generated_flows.json")
            if os.path.exists(potential_flows_file):
                self.generated_flows_file = potential_flows_file
            else:
                print("Warning: No generated_flows.json file specified or found. Using use_case_id for filenames.")
                return

        try:
            with open(self.generated_flows_file, "r", encoding="utf-8") as f:
                flows_data = json.load(f)
                self.use_case_flows_list = flows_data.get("use_case_flows", [])
                
                print(f"Loaded {len(self.use_case_flows_list)} use case flows for number-based naming.")
                
        except FileNotFoundError:
            print(f"Warning: Generated flows file not found at '{self.generated_flows_file}'. Using use_case_id for filenames.")
            self.use_case_flows_list = []
        except json.JSONDecodeError:
            print(f"Warning: Could not decode JSON from generated flows file '{self.generated_flows_file}'. Using use_case_id for filenames.")
            self.use_case_flows_list = []
        except Exception as e:
            print(f"Warning: Error reading generated flows file ({self.generated_flows_file}): {e}. Using use_case_id for filenames.")
            self.use_case_flows_list = []

    def get_number_for_path(self, path_index: int, use_case_id: str = None) -> str:
        """
        Gets the number for a path based on its index in the processing order.
        If use_case_flows_list is available, uses the path_index to get the corresponding number.
        Otherwise, falls back to use_case_id.
        """
        if self.use_case_flows_list and path_index < len(self.use_case_flows_list):
            flow = self.use_case_flows_list[path_index]
            number = flow.get("number")
            if number:
                return str(number)
        
        # Fallback to use_case_id if number not available
        return use_case_id if use_case_id else str(path_index + 1)

    @classmethod
    def create_for_business_flow(cls, path_processor_base_dir: str, business_flow_number: int,
                                base_output_dir: str):
        """
        Factory method to create ScreenTestCaseGenerator for a specific business flow.

        Args:
            path_processor_base_dir: Base directory containing path processor output.
            business_flow_number: Number of the business flow to process.
            base_output_dir: Base directory for output files.

        Returns:
            ScreenTestCaseGenerator instance configured for the specified business flow.
        """
        # Create paths for this business flow
        business_flow_dir = os.path.join(path_processor_base_dir, f"business_flow_{business_flow_number}")
        path_file = os.path.join(business_flow_dir, "screen_paths.json")
        screen_file = os.path.join(business_flow_dir, "screen_variables.json")  # Business flow specific file
        flows_file = os.path.join(business_flow_dir, "flows_data.json")

        # Create output directory for this business flow
        output_directory = os.path.join(base_output_dir, f"business_flow_{business_flow_number}")

        return cls(
            path_file=path_file,
            screen_file=screen_file,
            output_directory=output_directory,
            generated_flows_file=flows_file,
            business_flow_number=business_flow_number
        )

    @staticmethod
    def process_all_business_flows(path_processor_base_dir: str, base_output_dir: str):
        """
        Process all business flows found in the path_processor_base_dir.

        Args:
            path_processor_base_dir: Base directory containing path processor output.
            base_output_dir: Base directory for output files.

        Returns:
            List of results from processing each business flow.
        """
        results = []

        # Find all business_flow directories
        if not os.path.exists(path_processor_base_dir):
            print(f"Error: Path processor base directory not found: {path_processor_base_dir}")
            return results

        business_flow_dirs = [d for d in os.listdir(path_processor_base_dir)
                             if d.startswith("business_flow_") and
                             os.path.isdir(os.path.join(path_processor_base_dir, d))]

        if not business_flow_dirs:
            print(f"No business flow directories found in {path_processor_base_dir}")
            return results

        business_flow_dirs.sort()  # Process in order

        for business_flow_dir in business_flow_dirs:
            # Extract business flow number
            match = re.search(r"business_flow_(\d+)", business_flow_dir)
            if not match:
                continue

            business_flow_number = int(match.group(1))
            print(f"\n{'='*50}")
            print(f"Processing Business Flow {business_flow_number} - Test Case Generation")
            print(f"{'='*50}")

            try:
                # Create processor for this business flow
                processor = ScreenTestCaseGenerator.create_for_business_flow(
                    path_processor_base_dir=path_processor_base_dir,
                    business_flow_number=business_flow_number,
                    base_output_dir=base_output_dir
                )

                # Execute processing
                processor.execute()

                results.append({
                    "business_flow_number": business_flow_number,
                    "success": True,
                    "output_directory": processor.output_directory
                })

                print(f"✅ Business Flow {business_flow_number} test cases generated successfully")

            except Exception as e:
                print(f"❌ Error processing Business Flow {business_flow_number}: {e}")
                results.append({
                    "business_flow_number": business_flow_number,
                    "success": False,
                    "error": str(e)
                })

        # Print overall summary
        successful_flows = [r for r in results if r['success']]
        print(f"\n{'='*50}")
        print("TEST CASE GENERATION SUMMARY")
        print(f"{'='*50}")
        print(f"Total Business Flows Processed: {len(results)}")
        print(f"Successful: {len(successful_flows)}")
        print(f"Failed: {len(results) - len(successful_flows)}")

        if successful_flows:
            print(f"\n📁 Generated Test Cases:")
            for result in successful_flows:
                print(f"  Business Flow {result['business_flow_number']}: {result['output_directory']}")

        return results

    @staticmethod
    def process_from_base_dir(base_dir: str, output_dir: str = None):
        """
        Convenience method to process all business flows from a base directory.
        Automatically detects path_processor/ subdirectory.

        Args:
            base_dir: Base directory containing path_processor/
            output_dir: Output directory. If None, uses base_dir/test_case_generator/

        Returns:
            List of results from processing each business flow.
        """
        path_processor_base_dir = os.path.join(base_dir, "path_processor")

        if output_dir is None:
            output_dir = os.path.join(base_dir, "test_case_generator")

        # Validate paths exist
        if not os.path.exists(path_processor_base_dir):
            print(f"❌ Path processor directory not found: {path_processor_base_dir}")
            return []

        print(f"📁 Auto-detected directories:")
        print(f"  Path Processor: {path_processor_base_dir}")
        print(f"  Output: {output_dir}")

        return ScreenTestCaseGenerator.process_all_business_flows(
            path_processor_base_dir=path_processor_base_dir,
            base_output_dir=output_dir
        )

    @staticmethod
    def normalize_screen_name(name: str) -> str:
        """Normalizes screen names by removing suffixes in parentheses like '(Seller)'."""
        return re.sub(r'\s*\([^)]*\)\s*$', '', name).strip()

    @staticmethod
    def _prepare_variable_lists(screen_variables: Dict[str, Any], valid_only: bool = False) -> List[tuple]:
        """Prepares lists of variable values for combination generation."""
        ordered_vars = []
        if not isinstance(screen_variables, dict):
            return []

        for var_name, var_info in screen_variables.items():
            vals = []
            if isinstance(var_info, dict): # Handles variables with valid/invalid lists
                valid_values = var_info.get("valid", [])
                if isinstance(valid_values, list):
                    vals.extend([(v, "valid") for v in valid_values])

                if not valid_only:
                    invalid_values = var_info.get("invalid", [])
                    if isinstance(invalid_values, list):
                        vals.extend([(v, "invalid") for v in invalid_values])

            elif var_name == "button_action" and isinstance(var_info, list): # Handle button_action specifically
                if not valid_only:
                    vals.extend([(v, "action") for v in var_info])

            if vals:
                if vals:
                   ordered_vars.append((var_name, vals))

        return ordered_vars

    @staticmethod
    def generate_pairwise_cases(screen: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generates pairwise test cases for a screen (valid & invalid)."""
        screen_name = screen.get('screen_name', 'UnknownScreen')
        variables = screen.get("variables", {})
        if not variables or not isinstance(variables, dict):
            return [{"params": {}, "outcome": "true"}]

        ordered_vars = ScreenTestCaseGenerator._prepare_variable_lists(variables, valid_only=False)

        if not ordered_vars:
             return [{"params": {}, "outcome": "true"}]

        values_lists = []
        vars_in_pairwise = []
        for var_name, vals in ordered_vars:
            filtered_vals = [item for item in vals if item is not None and isinstance(item, tuple) and len(item) > 0 and item[0] is not None]
            if filtered_vals:
                 values_lists.append(filtered_vals)
                 vars_in_pairwise.append((var_name, filtered_vals))

        combos = []
        if len(values_lists) == 1:
            combos = [[val] for val in values_lists[0]]
        elif len(values_lists) > 1:
            try:
                combos = list(AllPairs(values_lists))
            except Exception as e:
                print(f"Error: AllPairs failed for screen '{screen_name}': {e}. Returning default valid case only.")
                return ScreenTestCaseGenerator.generate_first_valid_combination(screen)

        testcases = []
        processed_combo_strs = set()

        if not combos and ordered_vars:
            print(f"Warning: No pairwise combinations generated for screen '{screen_name}'. Using default valid case.")
            default_valid_cases = ScreenTestCaseGenerator.generate_first_valid_combination(screen)
            if default_valid_cases:
                testcases.append(default_valid_cases[0])
                processed_combo_strs.add(json.dumps(default_valid_cases[0]["params"], sort_keys=True))

        for combo in combos:
            params = {}
            all_valid_or_action = True

            if len(vars_in_pairwise) != len(combo):
                 print(f"Warning: Mismatch combo length ({len(combo)}) vs vars ({len(vars_in_pairwise)}) in '{screen_name}'. Skipping combo: {combo}")
                 continue

            for i, (var_name, _) in enumerate(vars_in_pairwise):
                if i < len(combo) and isinstance(combo[i], tuple) and len(combo[i]) == 2:
                    value, cat = combo[i]
                    params[var_name] = value
                    if cat == "invalid":
                        all_valid_or_action = False
                else:
                     print(f"Warning: Unexpected combo item format: {combo[i]} in {combo} for screen {screen_name}")
                     all_valid_or_action = False

            outcome = "true" if all_valid_or_action else "false"
            param_str = json.dumps(params, sort_keys=True)
            if param_str not in processed_combo_strs:
                testcases.append({"params": params, "outcome": outcome})
                processed_combo_strs.add(param_str)

        if not testcases:
             if variables:
                 print(f"Warning: No test cases (pairwise/fallback) generated for screen '{screen_name}' despite variables. Adding default empty case.")
             testcases.append({"params": {}, "outcome": "true"})

        return testcases

    @staticmethod
    def generate_first_valid_combination(screen: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generates exactly ONE test case dictionary representing the first valid
        combination based on the variable definitions.
        """
        screen_name = screen.get('screen_name', 'UnknownScreen')
        variables = screen.get("variables", {})
        params = {}

        if not variables or not isinstance(variables, dict):
            return [{"params": {}, "outcome": "true"}]

        for var_name, var_info in variables.items():
            if isinstance(var_info, dict):
                valid_list = var_info.get("valid", [])
                if isinstance(valid_list, list) and valid_list:
                    params[var_name] = valid_list[0]

        return [{"params": params, "outcome": "true"}]

    def execute(self):
        """Generates test case CSV files based on screen paths."""
        # Add business flow context to logging
        bf_context = f" (Business Flow {self.business_flow_number})" if self.business_flow_number else ""
        print(f"🔄 Starting test case generation{bf_context}...")

        try:
            with open(self.path_file, "r", encoding="utf-8") as f:
                uc_paths_data = json.load(f)
                
                # Handle BFS paths structure (new structure only uses BFS paths)
                if isinstance(uc_paths_data, dict):
                    # Check for BFS paths structure
                    if "bfs_paths" in uc_paths_data and isinstance(uc_paths_data["bfs_paths"], list):
                        # Process BFS paths and convert to simple path format
                        bfs_paths = uc_paths_data["bfs_paths"]
                        uc_paths = []
                        print(f"Processing {len(bfs_paths)} BFS paths...")

                        for i, bfs_path in enumerate(bfs_paths):
                            print(f"  Processing BFS path {i+1}: {bfs_path.get('use_case_id', 'Unknown UC')}")

                            if isinstance(bfs_path, dict) and "use_case_id" in bfs_path:
                                # Extract screen names from BFS path format
                                screen_names = []

                                # Check if path exists and is a list
                                if "path" in bfs_path and isinstance(bfs_path["path"], list):
                                    path_steps = bfs_path["path"]
                                    if path_steps:
                                        # Start with the source screen of the first step
                                        if isinstance(path_steps[0], dict) and "source_screen" in path_steps[0]:
                                            screen_names.append(path_steps[0]["source_screen"])

                                        # Add target screens for each step, avoiding duplicates
                                        for step in path_steps:
                                            if isinstance(step, dict) and "target_screen" in step:
                                                target_screen = step["target_screen"]
                                                if not screen_names or screen_names[-1] != target_screen:
                                                    screen_names.append(target_screen)
                                    else:
                                        print(f"    Warning: Empty path for UC {bfs_path.get('use_case_id')}")

                                # Fallback: check if there's a 'screen' field for single-screen UCs
                                if not screen_names and "screen" in bfs_path:
                                    screen_names = [bfs_path["screen"]]
                                    print(f"    Using 'screen' field: {bfs_path['screen']}")

                                # Create path format if we have screens
                                if screen_names:
                                    path_data = {
                                        "use_case_id": bfs_path["use_case_id"],
                                        "path": screen_names
                                    }
                                    uc_paths.append(path_data)
                                    print(f"    ✓ Converted: {' -> '.join(screen_names)}")
                                else:
                                    print(f"    ❌ No screens found for UC {bfs_path.get('use_case_id')}")
                            else:
                                print(f"    ❌ Invalid BFS path format for path {i+1}")

                        print(f"Successfully converted {len(uc_paths)} out of {len(bfs_paths)} BFS paths")
                    else:
                        # Handle other dictionary structures as fallback
                        for potential_key in ["paths", "screen_paths", "data"]:
                            if potential_key in uc_paths_data and isinstance(uc_paths_data[potential_key], list):
                                uc_paths = uc_paths_data[potential_key]
                                print(f"Using '{potential_key}' from file ({len(uc_paths)} paths)")
                                break
                        else:
                            # If no list found in expected keys, use the dictionary itself as a single path
                            uc_paths = [uc_paths_data]
                            print("Using entire JSON object as a single path")
                elif isinstance(uc_paths_data, list):
                    uc_paths = uc_paths_data
                    print(f"Using JSON array directly ({len(uc_paths)} paths)")
                else:
                    # Convert to string and treat as a single path
                    uc_paths = [str(uc_paths_data)]
                    print("Converting non-list/non-dict data to a single path")
                
                print(f"Loaded {len(uc_paths)} paths from file{bf_context}.")
        except FileNotFoundError:
             print(f"❌ Error: Path file not found at '{self.path_file}'{bf_context}")
             return
        except json.JSONDecodeError:
            print(f"❌ Error: Could not decode JSON from path file '{self.path_file}'{bf_context}")
            return
        except Exception as e:
            print(f"❌ Error reading path file ({self.path_file}){bf_context}: {e}")
            return

        if not self.screen_definitions:
            print(f"❌ Error: No screen definitions loaded{bf_context}. Aborting test case generation.")
            return

        os.makedirs(self.output_directory, exist_ok=True)
        print(f"📁 Output directory: {self.output_directory}")

        generated_files = []

        for path_index, path_info in enumerate(uc_paths):
            try:
                # Handle different path formats
                if isinstance(path_info, dict):
                    # Try to extract path from dictionary using various possible keys
                    for key in ["path", "screens", "screen_path"]:
                        if key in path_info and path_info[key]:
                            if isinstance(path_info[key], list):
                                screen_path_names = path_info[key]
                                break
                            elif isinstance(path_info[key], str):
                                screen_path_names = [name.strip() for name in path_info[key].replace('\n', ',').split(',') if name.strip()]
                                break
                    else:
                        # If no path found in expected keys, look for any list value
                        for value in path_info.values():
                            if isinstance(value, list) and value and all(isinstance(item, str) for item in value):
                                screen_path_names = value
                                break
                        else:
                            # Default empty
                            screen_path_names = []
                    
                    # Get use case ID for filename and logging
                    uc_id = None
                    for key in ["use_case_id", "usecase", "uc_id", "id"]:
                        if key in path_info:
                            uc_id = path_info[key]
                            break
                    
                    # Get number based on path index for filename
                    if isinstance(uc_id, list):
                        uc_path_str_for_logging = "_".join(uc_id)
                        # For multiple UC IDs, use the first one with path index
                        first_uc_id = uc_id[0] if uc_id else None
                        number = self.get_number_for_path(path_index, first_uc_id)
                        output_filename_base = f"Testcase_({number})"
                    elif isinstance(uc_id, str):
                        uc_path_str_for_logging = uc_id
                        # Get number based on path index, fallback to use_case_id
                        number = self.get_number_for_path(path_index, uc_id)
                        output_filename_base = f"Testcase_({number})"
                    else:
                        uc_path_str_for_logging = f"Index {path_index + 1}"
                        number = self.get_number_for_path(path_index)
                        output_filename_base = f"Testcase_({number})"
                        
                elif isinstance(path_info, list):
                    # If it's a list, assume it's directly the screen path names
                    screen_path_names = path_info
                    uc_path_str_for_logging = f"Index {path_index + 1}"
                    output_filename_base = f"Testcase_{path_index + 1}"
                    
                elif isinstance(path_info, str):
                    # If it's a string, split by commas or newlines
                    screen_path_names = [name.strip() for name in path_info.replace('\n', ',').split(',') if name.strip()]
                    uc_path_str_for_logging = f"Index {path_index + 1}"
                    output_filename_base = f"Testcase_{path_index + 1}"
                    
                else:
                    print(f"Warning: Skipping path {path_index + 1} as its format is not recognized.")
                    continue

                if not screen_path_names:
                    print(f"Warning: Skipping path {path_index + 1} (UC Path: '{uc_path_str_for_logging}') as it has no screens.")
                    continue

                print(f"\nProcessing path {path_index + 1} (UC Path: '{uc_path_str_for_logging}'): {' -> '.join(screen_path_names)}")

                screen_objs_in_path = []
                valid_path = True
                for screen_name in screen_path_names:
                    # Normalize screen name by removing suffixes in parentheses like "(Seller)"
                    normalized_screen_name = self.normalize_screen_name(screen_name)
                    
                    screen_obj_orig = self.screen_definitions.get(normalized_screen_name)
                    if not screen_obj_orig:
                        # Try to find a screen that starts with the base name (as a fallback)
                        base_name = normalized_screen_name.split()[0]  # Get first word
                        matching_screens = [s for s in self.screen_definitions.keys() 
                                          if s.startswith(base_name)]
                        
                        if matching_screens:
                            screen_obj_orig = self.screen_definitions.get(matching_screens[0])
                            print(f"Warning: Screen '{screen_name}' not found, using closest match '{matching_screens[0]}' instead.")
                    
                    if screen_obj_orig:
                        # Create a copy and update screen_name to match the path
                        screen_obj_copy = copy.deepcopy(screen_obj_orig)
                        screen_obj_copy["screen_name"] = screen_name  # Use original name for output
                        screen_objs_in_path.append(screen_obj_copy)
                    else:
                        print(f"Error: Screen '{screen_name}' (normalized: '{normalized_screen_name}') not found in loaded definitions for path {path_index + 1} ('{uc_path_str_for_logging}'). Skipping this path.")
                        valid_path = False
                        break
                if not valid_path: 
                    continue

                num_steps = len(screen_objs_in_path)

                fieldnames = []
                for i, sc_obj in enumerate(screen_objs_in_path):
                    sc_name = sc_obj.get("screen_name", "Unknown")
                    fieldnames.append(f"{sc_name}_params")
                    fieldnames.append(f"{sc_name}_outcome")
                    if i < num_steps - 1:
                        fieldnames.append(f"{sc_name}_navigation_button")

                preceding_valid_cases_repr = []
                for i, screen_obj in enumerate(screen_objs_in_path[:-1]):
                    repr_case_list = self.generate_first_valid_combination(screen_obj)
                    if repr_case_list:
                         preceding_valid_cases_repr.append(repr_case_list[0])
                    else:
                         print(f"Error: Could not generate representative valid case for intermediate screen '{screen_obj.get('screen_name')}' in path '{uc_path_str_for_logging}'. Using default empty.")
                         preceding_valid_cases_repr.append({"params": {}, "outcome": "true"})

                last_screen_pairwise_cases = []
                if screen_objs_in_path:
                    last_screen_obj = screen_objs_in_path[-1]
                    last_screen_pairwise_cases = self.generate_pairwise_cases(last_screen_obj)
                    if not last_screen_pairwise_cases:
                         print(f"Error: No pairwise cases generated for last screen '{last_screen_obj.get('screen_name')}' in path '{uc_path_str_for_logging}'. Skipping path.")
                         continue

                merged_rows_data = []
                if not screen_objs_in_path:
                     print(f"Warning: screen_objs_in_path is empty for path '{uc_path_str_for_logging}'. Skipping row generation.")
                     continue

                num_fields = len(fieldnames)

                for last_case in last_screen_pairwise_cases:
                    row_data_list = [None] * num_fields
                    for i in range(num_steps - 1):
                        current_screen_obj = screen_objs_in_path[i]
                        next_screen_obj = screen_objs_in_path[i+1]
                        current_screen_name = current_screen_obj.get("screen_name", "Unknown")
                        next_screen_name = next_screen_obj.get("screen_name", "Unknown")

                        params_idx = i * 3
                        outcome_idx = i * 3 + 1
                        button_idx = i * 3 + 2

                        if i < len(preceding_valid_cases_repr):
                            repr_case = preceding_valid_cases_repr[i]
                            row_data_list[params_idx] = json.dumps(repr_case.get("params", {}), ensure_ascii=False, sort_keys=True)
                            row_data_list[outcome_idx] = repr_case.get("outcome", "true")
                        else:
                            print(f"Error: Index mismatch accessing preceding cases repr for path '{uc_path_str_for_logging}', index {i}")
                            row_data_list[params_idx] = json.dumps({})
                            row_data_list[outcome_idx] = "error"

                        # --- *** Look up, clean, and fill navigation button *** ---
                        transition_key = (current_screen_name, next_screen_name)
                        raw_button_name = self.navigation_rules.get(transition_key)
                        
                        # If not found, try with normalized names
                        if raw_button_name is None:
                            normalized_current = self.normalize_screen_name(current_screen_name)
                            normalized_next = self.normalize_screen_name(next_screen_name)
                            transition_key = (normalized_current, normalized_next)
                            raw_button_name = self.navigation_rules.get(transition_key)
                            
                            # If still not found, try matching by normalized + prefix
                            if raw_button_name is None:
                                for rule_key, button in self.navigation_rules.items():
                                    rule_src, rule_tgt = rule_key
                                    if (normalized_current == self.normalize_screen_name(rule_src) and 
                                        normalized_next == self.normalize_screen_name(rule_tgt)):
                                        raw_button_name = button
                                        break
                        
                        # If still not found, use default
                        if raw_button_name is None:
                            raw_button_name = f"Navigate to {next_screen_name}"
                            print(f"Warning: No navigation rule found for transition '{current_screen_name}' -> '{next_screen_name}'. Using default.")

                        # Clean the button name: replace \n and \t with space, handle potential \\n, trim whitespace
                        cleaned_button_name = raw_button_name.replace("\\n", " ").replace("\\t", " ") # Handle literal escapes
                        cleaned_button_name = cleaned_button_name.replace("\n", " ").replace("\t", " ") # Handle actual escapes
                        cleaned_button_name = ' '.join(cleaned_button_name.split()) # Consolidate whitespace and trim

                        if button_idx < num_fields:
                            row_data_list[button_idx] = cleaned_button_name # Use cleaned name
                        else:
                             print(f"Error: Calculated button index {button_idx} out of bounds ({num_fields}) for path '{uc_path_str_for_logging}'.")
                        # --- *** End Navigation Button Logic *** ---

                    last_params_idx = (num_steps - 1) * 3
                    last_outcome_idx = (num_steps - 1) * 3 + 1
                    if last_params_idx < num_fields and last_outcome_idx < num_fields:
                        row_data_list[last_params_idx] = json.dumps(last_case.get("params", {}), ensure_ascii=False, sort_keys=True)
                        row_data_list[last_outcome_idx] = last_case.get("outcome", "error")
                    else:
                        print(f"Error: Calculated last step indices ({last_params_idx}, {last_outcome_idx}) out of bounds ({num_fields}) for path '{uc_path_str_for_logging}'.")

                    if len(row_data_list) == num_fields:
                         merged_rows_data.append(row_data_list)
                    else:
                         print(f"Error: Generated row data length ({len(row_data_list)}) does not match fieldnames length ({num_fields}) for path '{uc_path_str_for_logging}'. Skipping row.")

                if merged_rows_data:
                    output_filename = os.path.join(self.output_directory, f"{output_filename_base}.csv")
                    try:
                        with open(output_filename, "w", newline="", encoding="utf-8") as f:
                            writer = csv.writer(f)
                            writer.writerow(fieldnames)
                            writer.writerows(merged_rows_data)
                        print(f"✔ Generated: {output_filename} ({len(merged_rows_data)} rows)")
                        generated_files.append(output_filename)
                    except IOError as e:
                        print(f"❌ Error writing CSV file {output_filename}: {e}")
                    except Exception as e:
                        print(f"❌ An unexpected error occurred during CSV writing for {output_filename}: {e}")
                else:
                    print(f"ℹ No data generated for path index {path_index + 1}, CSV file '{output_filename_base}.csv' not created.")
                    
            except Exception as e:
                # Extract UC ID for error reporting
                uc_id_for_error = "Unknown"
                try:
                    if isinstance(path_info, dict):
                        for key in ["use_case_id", "usecase", "uc_id", "id"]:
                            if key in path_info:
                                original_uc_id = str(path_info[key])
                                # Try to get number based on path index for error reporting
                                number = self.get_number_for_path(path_index, original_uc_id)
                                uc_id_for_error = f"{number} ({original_uc_id})"
                                break
                    if uc_id_for_error == "Unknown":
                        uc_id_for_error = f"Index {path_index + 1}"
                except:
                    uc_id_for_error = f"Index {path_index + 1}"
                
                print(f"❌ Error processing path {path_index + 1} (UC: {uc_id_for_error}): {e}")
                print(f"   Continuing with next path...")
                continue

        # Print summary
        print(f"\n{'='*50}")
        print(f"TEST CASE GENERATION COMPLETE{bf_context}")
        print(f"{'='*50}")
        print(f"📊 Generated {len(generated_files)} CSV files")
        print(f"📁 Output directory: {self.output_directory}")

        if generated_files:
            print(f"📄 Generated files:")
            for file_path in generated_files:
                filename = os.path.basename(file_path)
                print(f"  - {filename}")
        else:
            print("⚠️ No CSV files were generated")

        return generated_files