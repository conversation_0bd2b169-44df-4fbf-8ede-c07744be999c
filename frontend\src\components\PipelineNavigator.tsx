/**
 * Pipeline Navigator Component - UI with Upload Functionality
 * Pipeline display with working document upload
 */
import React, { useState, useRef, useEffect, useCallback } from "react";
import {
    Upload,
    FileText,
    Play,
    CheckCircle,
    Clock,
    Wifi,
    XCircle,
    AlertCircle,
    Pause,
    RotateCcw,
    X,
    RefreshCw,
} from "lucide-react";
import { usePipelineModules } from "../hooks/usePipelineModules";
import { useWebSocket } from "../hooks/useWebSocket";

// Pipeline step status type
interface PipelineStepStatus {
    id: string;
    status:
        | "pending"
        | "running"
        | "completed"
        | "failed"
        | "warning"
        | "paused";
    progress?: number;
    error_message?: string;
}

// Function to get status icon and color
const getStatusIcon = (status: string) => {
    switch (status) {
        case "completed":
            return {
                icon: <CheckCircle className="w-4 h-4" />,
                color: "text-green-500",
                text: "Completed",
            };
        case "running":
            return {
                icon: <Clock className="w-4 h-4 animate-spin" />,
                color: "text-yellow-500",
                text: "Running",
            };
        case "failed":
            return {
                icon: <XCircle className="w-4 h-4" />,
                color: "text-red-500",
                text: "Failed",
            };
        case "warning":
            return {
                icon: <AlertCircle className="w-4 h-4" />,
                color: "text-orange-500",
                text: "Warning",
            };
        case "paused":
            return {
                icon: <Pause className="w-4 h-4" />,
                color: "text-blue-500",
                text: "Paused",
            };
        case "pending":
        default:
            return {
                icon: <Clock className="w-4 h-4" />,
                color: "text-gray-500",
                text: "Pending",
            };
    }
};

export const PipelineNavigator: React.FC = () => {
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [uploadedFileName, setUploadedFileName] = useState<string | null>(
        null
    ); // Server file name
    const [isDragging, setIsDragging] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [isExecuting, setIsExecuting] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Load pipeline modules from API
    const {
        modules,
        loading: modulesLoading,
        error: modulesError,
        refetch: refetchModules,
    } = usePipelineModules();

    // WebSocket connection for real-time updates
    const { isConnected, lastMessage } = useWebSocket();

    // Pipeline status state - default all to pending
    const [pipelineStatus, setPipelineStatus] = useState<
        Record<string, PipelineStepStatus>
    >({});

    // Initialize pipeline status when modules load
    useEffect(() => {
        if (modules.length > 0) {
            const initialStatus: Record<string, PipelineStepStatus> = {};
            modules.forEach((module) => {
                initialStatus[module.id] = {
                    id: module.id,
                    status: "pending",
                    progress: 0,
                };
            });
            setPipelineStatus(initialStatus);
        }
    }, [modules]);

    // Reset pipeline status when component mounts
    useEffect(() => {
        const resetPipeline = async () => {
            try {
                await fetch("/api/v1/pipeline/reset", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                console.log("Pipeline reset on page load");
            } catch (error) {
                console.error("Error resetting pipeline:", error);
            }
        };

        resetPipeline();
    }, []);

    // Polling state
    const [pollingInterval, setPollingInterval] = useState<number | null>(null);

    // Start polling function
    const startPolling = useCallback(() => {
        // Clear existing interval if any
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        const pollPipelineStatus = async () => {
            try {
                const response = await fetch("/api/v1/pipeline/status");
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data && result.data.steps) {
                        const newStatus: Record<string, PipelineStepStatus> =
                            {};
                        let hasRunningStep = false;

                        result.data.steps.forEach((step: any) => {
                            const stepStatus = step.status || "pending";
                            newStatus[step.id] = {
                                id: step.id,
                                status: stepStatus,
                                progress: step.progress || 0,
                                error_message: step.error_message,
                            };

                            // Check if any step is still running
                            if (stepStatus === "running") {
                                hasRunningStep = true;
                            }
                        });

                        setPipelineStatus(newStatus);

                        // Stop polling if no steps are running
                        if (!hasRunningStep) {
                            console.log(
                                "All steps completed/failed, stopping polling"
                            );
                            setPollingInterval((prev) => {
                                if (prev) clearInterval(prev);
                                return null;
                            });
                        }
                    }
                }
            } catch (error) {
                console.error("Error polling pipeline status:", error);
            }
        };

        // Poll immediately
        pollPipelineStatus();

        // Start polling every 2 seconds
        const newInterval = setInterval(pollPipelineStatus, 2000);
        setPollingInterval(newInterval);
    }, [pollingInterval]);

    // Initial polling setup
    useEffect(() => {
        startPolling();

        return () => {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        };
    }, []);

    // Handle WebSocket messages for real-time pipeline updates
    useEffect(() => {
        if (lastMessage && lastMessage.data) {
            try {
                const message =
                    typeof lastMessage.data === "string"
                        ? JSON.parse(lastMessage.data)
                        : lastMessage.data;

                if (message.type === "pipeline_status" && message.data) {
                    // Update pipeline status from WebSocket
                    const pipelineData = message.data;
                    if (pipelineData.steps) {
                        const newStatus: Record<string, PipelineStepStatus> =
                            {};
                        pipelineData.steps.forEach((step: any) => {
                            newStatus[step.id] = {
                                id: step.id,
                                status: step.status || "pending",
                                progress: step.progress || 0,
                                error_message: step.error_message,
                            };
                        });
                        setPipelineStatus(newStatus);
                    }
                }
            } catch (error) {
                console.error("Error parsing WebSocket message:", error);
            }
        }
    }, [lastMessage]);

    // Execute individual pipeline step
    const executeStep = async (moduleId: string) => {
        if (!uploadedFile) {
            alert("Please upload a PDF file first");
            return;
        }

        try {
            // Update status to running immediately for better UX
            setPipelineStatus((prev) => ({
                ...prev,
                [moduleId]: {
                    ...prev[moduleId],
                    status: "running",
                },
            }));

            // Restart polling when execution starts
            startPolling();

            const response = await fetch("/api/v1/pipeline/execute-step", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    module_id: moduleId,
                    file_path: `uploads/${
                        uploadedFileName || uploadedFile.name
                    }`,
                }),
            });

            if (!response.ok) {
                // Revert status on error
                setPipelineStatus((prev) => ({
                    ...prev,
                    [moduleId]: {
                        ...prev[moduleId],
                        status: "failed",
                        error_message: `HTTP ${response.status}: ${response.statusText}`,
                    },
                }));
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const result = await response.json();
            console.log("Step execution started:", result);
        } catch (error) {
            console.error("Error executing step:", error);
            // Set failed status if not already set
            setPipelineStatus((prev) => ({
                ...prev,
                [moduleId]: {
                    ...prev[moduleId],
                    status: "failed",
                    error_message: (error as Error).message || "Unknown error",
                },
            }));
            alert("Failed to execute step. Please try again.");
        }
    };

    // Execute all pipeline steps
    const executeAllSteps = async () => {
        if (!uploadedFile) {
            alert("Please upload a PDF file first");
            return;
        }

        setIsExecuting(true);
        try {
            const response = await fetch("/api/v1/pipeline/execute-all", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    file_path: `uploads/${
                        uploadedFileName || uploadedFile.name
                    }`,
                }),
            });

            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const result = await response.json();
            console.log("Pipeline execution started:", result);
        } catch (error) {
            console.error("Error executing pipeline:", error);
            alert("Failed to execute pipeline. Please try again.");
        } finally {
            setIsExecuting(false);
        }
    };

    // Upload file to server
    const uploadFileToServer = async (file: File) => {
        setIsUploading(true);

        try {
            const formData = new FormData();
            formData.append("file", file);

            const response = await fetch("/api/v1/upload/document", {
                method: "POST",
                body: formData,
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || "Upload failed");
            }

            const result = await response.json();
            console.log("✅ Upload successful:", result);

            // Save the server filename for later use
            if (result.data && result.data.filename) {
                setUploadedFileName(result.data.filename);
            }

            return result;
        } catch (error) {
            console.error("❌ Upload failed:", error);
            alert(
                `Upload failed: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
            throw error;
        } finally {
            setIsUploading(false);
        }
    };

    // Handle file selection
    const handleFileSelect = async (file: File) => {
        // Check if file is PDF
        if (file.type !== "application/pdf") {
            alert("Please select a PDF file only.");
            return;
        }

        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert("File size must be less than 10MB.");
            return;
        }

        try {
            // Upload file to server
            await uploadFileToServer(file);

            // Set uploaded file state
            setUploadedFile(file);
            console.log(
                "File selected and uploaded:",
                file.name,
                "Size:",
                (file.size / 1024 / 1024).toFixed(2) + "MB"
            );
        } catch (error) {
            // Upload failed, don't set the file
            console.error("File selection failed due to upload error");
        }
    };

    // Handle drag and drop
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);

        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    // Handle click to upload
    const handleUploadClick = () => {
        fileInputRef.current?.click();
    };

    // Handle file input change
    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    // Remove uploaded file
    const handleRemoveFile = () => {
        setUploadedFile(null);
        setUploadedFileName(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    return (
        <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                    <Wifi className="w-5 h-5 text-green-500" />
                    <h2 className="text-xl font-bold text-white">
                        Pipeline Navigator
                    </h2>
                </div>
                <p className="text-gray-400 text-sm">
                    Upload a document and execute pipeline steps
                </p>
            </div>

            {/* File Upload Section */}
            <div className="p-4 border-b border-gray-700">
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                        Upload Document
                    </label>

                    {/* Hidden file input */}
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept=".pdf"
                        onChange={handleFileInputChange}
                        className="hidden"
                    />

                    {/* Upload area */}
                    <div
                        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                            isUploading
                                ? "border-blue-500 bg-blue-500/10 cursor-wait"
                                : isDragging
                                ? "border-blue-500 bg-blue-500/10 cursor-pointer"
                                : "border-gray-600 hover:border-gray-500 cursor-pointer"
                        }`}
                        onDragOver={!isUploading ? handleDragOver : undefined}
                        onDragLeave={!isUploading ? handleDragLeave : undefined}
                        onDrop={!isUploading ? handleDrop : undefined}
                        onClick={!isUploading ? handleUploadClick : undefined}
                    >
                        {isUploading ? (
                            <>
                                <div className="w-8 h-8 mx-auto mb-2 animate-spin">
                                    <div className="w-full h-full border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                </div>
                                <p className="text-sm text-blue-400 mb-1">
                                    Uploading...
                                </p>
                                <p className="text-xs text-gray-500">
                                    Please wait
                                </p>
                            </>
                        ) : (
                            <>
                                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm text-gray-400 mb-1">
                                    Click to upload or drag and drop
                                </p>
                                <p className="text-xs text-gray-500">
                                    PDF files only (max 10MB)
                                </p>
                            </>
                        )}
                    </div>
                </div>

                {/* Current File Display */}
                {uploadedFile && (
                    <div className="bg-gray-700 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                                <FileText className="w-4 h-4 text-blue-400 flex-shrink-0" />
                                <span className="text-sm text-gray-300 truncate">
                                    {uploadedFile.name}
                                </span>
                            </div>
                            <button
                                onClick={handleRemoveFile}
                                className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                                title="Remove file"
                            >
                                <X className="w-4 h-4" />
                            </button>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                            Uploaded •{" "}
                            {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                    </div>
                )}
            </div>

            {/* Pipeline Steps */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                        <h3 className="text-sm font-medium text-gray-300">
                            Pipeline Steps
                        </h3>
                        <button
                            onClick={refetchModules}
                            className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors"
                            title="Refresh modules"
                        >
                            <RefreshCw
                                className={`w-3 h-3 ${
                                    modulesLoading ? "animate-spin" : ""
                                }`}
                            />
                        </button>
                    </div>

                    {modulesError && (
                        <div className="mb-3 p-2 bg-yellow-900/50 border border-yellow-600 rounded text-xs text-yellow-300">
                            Using fallback modules (API unavailable)
                        </div>
                    )}

                    <div className="space-y-2">
                        {modulesLoading ? (
                            <div className="text-center py-4">
                                <div className="w-6 h-6 mx-auto mb-2 animate-spin">
                                    <div className="w-full h-full border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                </div>
                                <p className="text-xs text-gray-400">
                                    Loading modules...
                                </p>
                            </div>
                        ) : (
                            modules.map((module, index) => {
                                const stepStatus = pipelineStatus[module.id];
                                const status = stepStatus?.status || "pending";
                                const statusInfo = getStatusIcon(status);
                                const isStepRunning = status === "running";

                                return (
                                    <div
                                        key={module.id}
                                        className="bg-gray-700 rounded-lg p-3 border border-gray-600"
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-xs text-white font-medium">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <div className="text-sm font-medium text-white">
                                                        {module.name}
                                                    </div>
                                                    {/* Only show description for non-completed status */}
                                                    {status !== "completed" && (
                                                        <div className="text-xs text-gray-400 mb-1">
                                                            {module.description}
                                                        </div>
                                                    )}
                                                    <div
                                                        className={`text-xs ${statusInfo.color}`}
                                                    >
                                                        {statusInfo.text}
                                                        {stepStatus?.progress !==
                                                            undefined &&
                                                            stepStatus.progress >
                                                                0 && (
                                                                <span className="ml-1">
                                                                    (
                                                                    {stepStatus.progress.toFixed(
                                                                        0
                                                                    )}
                                                                    %)
                                                                </span>
                                                            )}
                                                    </div>
                                                    {/* Show error message if failed */}
                                                    {status === "failed" &&
                                                        stepStatus?.error_message && (
                                                            <div className="text-xs text-red-400 mt-1">
                                                                {
                                                                    stepStatus.error_message
                                                                }
                                                            </div>
                                                        )}
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className={statusInfo.color}
                                                >
                                                    {statusInfo.icon}
                                                </div>
                                                <button
                                                    onClick={() =>
                                                        executeStep(module.id)
                                                    }
                                                    disabled={
                                                        isStepRunning ||
                                                        !uploadedFile
                                                    }
                                                    className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                    title={
                                                        !uploadedFile
                                                            ? "Upload a PDF file first"
                                                            : "Execute this step"
                                                    }
                                                >
                                                    {isStepRunning ? (
                                                        <div className="w-3 h-3 animate-spin">
                                                            <div className="w-full h-full border border-blue-500 border-t-transparent rounded-full"></div>
                                                        </div>
                                                    ) : (
                                                        <Play className="w-3 h-3" />
                                                    )}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })
                        )}
                    </div>
                </div>
            </div>

            {/* Execute All Button */}
            <div className="p-4 border-t border-gray-700">
                <button
                    onClick={executeAllSteps}
                    disabled={isExecuting || !uploadedFile}
                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                    title={
                        !uploadedFile
                            ? "Upload a PDF file first"
                            : "Execute all pipeline steps"
                    }
                >
                    {isExecuting ? (
                        <>
                            <div className="w-4 h-4 animate-spin">
                                <div className="w-full h-full border-2 border-white border-t-transparent rounded-full"></div>
                            </div>
                            Executing Pipeline...
                        </>
                    ) : (
                        <>
                            <Play className="w-4 h-4" />
                            Execute All Steps
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};
