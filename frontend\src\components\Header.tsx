/**
 * Header Component - UI Only
 * Static header display without logic
 */
import React from "react";
import { FileText, Zap, DollarSign } from "lucide-react";

export const Header: React.FC = () => {
    return (
        <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <Zap className="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h1 className="text-xl font-bold text-white">
                            Phoenix Pipeline Executor
                        </h1>
                        <p className="text-gray-400 text-sm">
                            Interactive Document Processing
                        </p>
                    </div>
                </div>

                <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2 text-gray-300">
                        <FileText className="w-4 h-4" />
                        <span className="text-sm">No file selected</span>
                    </div>

                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2 text-gray-300">
                            <Zap className="w-4 h-4 text-yellow-500" />
                            <span className="text-sm">Tokens: 0</span>
                        </div>

                        <div className="flex items-center gap-2 text-gray-300">
                            <DollarSign className="w-4 h-4 text-green-500" />
                            <span className="text-sm">Cost: $0.0000</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    );
};
