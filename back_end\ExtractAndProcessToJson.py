import re
import json
import os
import glob
import fitz  # PyMuPDF
from PIL import Image
from io import BytesIO
from fuzzywuzzy import fuzz, process
import google.generativeai as genai
from .gen_test_case import prompt_storing
from .rotate_api_key import APIKeyRotator
from .json_repair_utility import JSONRepairUtility
from .count_gemini_token import GeminiCostCalculator
from google.api_core.exceptions import ResourceExhausted
import traceback
from typing import Union, List, Any, Dict, Optional, Tuple
import logging
from PyPDF2 import PdfReader
from PyPDF2.errors import PdfReadError
import threading
import time
import statistics

# Configure logging - WebSocket handler will capture these logs with delay control
logging.basicConfig(level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")  # Capture all log levels
logger = logging.getLogger(__name__)

class DiagramProcessor:
    """
    Processes diagrams from a PDF, categorizes them, generates JSON descriptions using Gemini API,
    and merges results with document structure using a generated TOC.

    Args:
        pdf_file (str): Path to the input PDF file.
        output_directory (str): Directory to save extracted images and JSON results.
        config_file (str, optional): Path to Gemini API configuration file. Defaults to "gemini_config.json".
        count_token (bool, optional): Enable token counting and cost calculation. Defaults to False.
    """
    def __init__(self, pdf_file: str, output_directory: str, config_file: str = "back_end/gemini_config.json", count_token: bool = False):
        if not pdf_file or not os.path.isfile(pdf_file):
            error_message = f"Invalid PDF path provided: '{pdf_file}'. The path is either empty, a directory, or the file does not exist."
            logger.error(error_message)
            raise FileNotFoundError(error_message)
            
        self.pdf_path = pdf_file
        self.output_directory = output_directory
        self.base_filename = os.path.splitext(os.path.basename(pdf_file))[0]
        self.count_token = count_token
        
        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config
        
        self.config = None
        self.rotator = None
        self.api_key = None
        self.model_name = None
        self.response_mime_type = None
        self.model = None
        self.initialization_ok = False

        # Threading support
        self.results_lock = threading.Lock()
        self.results = {}
        self.processing_stats = {
            'successful_images': 0,
            'failed_images': 0,
            'unknown_images': 0
        }

        # Enhanced category definitions for better matching
        self.categories = {
            "screen_flow": ["screen flow", "ui flow", "navigation", "page flow", "activity diagram", "process flow"],
            "state_machine": ["state machine", "state transition diagram", "state diagram"], # "State" is too general
            "use_case": ["use case", "use case diagram", "uml use case", "actor diagram", "actor interactions"],
            "screen_ui": ["screen", "ui", "user interface", "mockup", "design", "page", "layout", "form", "wireframe", "gui", "Design Specifications", "login screen"],
            "system_architecture": ["system context", "context diagram", "architecture", "system architecture"],
        }
        
        # Pre-process categories for efficient N-gram matching
        self.processed_categories = {}
        for category, keywords in self.categories.items():
            self.processed_categories[category] = []
            for keyword in keywords:
                n_grams = keyword.lower().split()
                self.processed_categories[category].append({
                    "phrase": keyword.lower(),
                    "n_grams": n_grams,
                    "n_gram_count": len(n_grams)
                })
            # Sort by n-gram count descending to prioritize longer matches
            self.processed_categories[category].sort(key=lambda x: x["n_gram_count"], reverse=True)

        self.MIN_ABSOLUTE_CATEGORY_SCORE_THRESHOLD = 80 # Lowered threshold
        self.fuzzy_threshold = 80
        self.fuzzy_title_threshold = 88 # Stricter threshold for titles

        # Generate TOC
        self.doc_sections = self._generate_toc_from_pdf()

        try:
            self.config = self.load_config(config_file)
            if self.config:
                # Determine model type for token calculation
                if self.count_token:
                    self._determine_model_type()
                    
                self.rotator = APIKeyRotator(config_path=config_file)
                if not self.rotator.api_keys:
                    logger.error("APIKeyRotator failed to load any keys.")
                    return
                self.api_key = self.rotator.get_api_key()
                gemini_config = self.config.get("config_gemini_model", {})
                self.model_name = gemini_config.get("model_name")
                self.response_mime_type = gemini_config.get("response_mime_type")
                if not self.model_name or not self.response_mime_type:
                    logger.error("Missing 'model_name' or 'response_mime_type' in config_gemini_model.")
                    return
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(
                    self.model_name,
                    generation_config={"response_mime_type": self.response_mime_type}
                )
                self.initialization_ok = True
                logger.info(f"Initialization successful. Using API key ending with: ...{self.api_key[-4:]}")
        except Exception as e:
            logger.error(f"An error occurred during initialization: {e}")
            logger.error(traceback.format_exc())

    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"
            
        if self.count_token:
            logger.info(f"Token counting enabled for model type: {self.model_type}")

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)

    def get_token_summary(self) -> Dict[str, Any]:
        """
        Trả về tổng kết chi phí token.
        
        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}
            
        total_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)
        
        return {
            "token_counting_enabled": True,
            "model_type": self.model_type,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            **total_cost_info
        }

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost."""
        if not self.count_token or not self.cost_calculator:
            return
            
        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "DIAGRAM PROCESSOR")
        
        # Log billing information
        from .billing_logger import billing_logger
        billing_logger.log_module_cost("DiagramProcessor", token_summary)

    def _normalize_text(self, text: str) -> str:
        """Normalizes text for searching by lowercasing and collapsing whitespace."""
        if not isinstance(text, str):
            return ""
        return ' '.join(text.lower().strip().split())

    def _find_title_on_page(self, title: str, page: fitz.Page, threshold: int) -> Optional[Tuple[fitz.Rect, float, str]]:
        """Finds the best match for a title on a page using fuzzy matching on text blocks."""
        best_match_score = 0
        best_match_bbox = None
        best_match_text = ""
        normalized_title = re.sub(r'^[0-9.IVX]+\s*', '', title).strip().lower()

        for block in page.get_text("blocks"):
            block_text = block[4].strip().replace('\n', ' ')
            if not block_text:
                continue
            
            # Use fuzz.extractOne to find the best matching substring within the block
            # This is more precise than comparing the whole block
            extract_result = process.extractOne(normalized_title, block_text.lower().splitlines())
            if not extract_result:
                continue
            matched_substring, score = extract_result
            if not matched_substring: # handle empty block_text
                continue

            # Compare against the whole block text as well for partial matches
            partial_score = fuzz.partial_ratio(normalized_title, block_text.lower())
            
            # Take the better of the two scores
            if partial_score > score:
                score = partial_score
                matched_substring = block_text # Or find a better substring later
            
            if score > best_match_score:
                best_match_score = score
                best_match_bbox = fitz.Rect(block[:4])
                # Find the best substring again from the original block text to preserve case
                orig_extract_result = process.extractOne(normalized_title, block[4].replace('\n', ' ').splitlines())
                if orig_extract_result:
                    best_match_text, _ = orig_extract_result
                else:
                    best_match_text = ""


        if best_match_score >= threshold:
            logger.debug(f"Found title '{title}' with score {best_match_score} at {best_match_bbox} (Matched text: '{best_match_text}')")
            return best_match_bbox, best_match_score, best_match_text
        return None, 0, ""

    def _extract_content_for_sections(self, toc_items: List[Dict[str, Any]], doc: fitz.Document) -> List[Dict[str, Any]]:
        """Extracts content for each section with robust title matching and boundary detection."""
        if not toc_items:
            return []

        sorted_toc = sorted(toc_items, key=lambda x: x.get('page', 1))

        for i, current_section in enumerate(sorted_toc):
            start_page = current_section['page']
            current_title = current_section['title']
            
            end_page = doc.page_count
            next_title = None
            if i + 1 < len(sorted_toc):
                next_section = sorted_toc[i + 1]
                end_page = next_section['page']
                next_title = next_section['title']

            content_parts = []
            for p_num in range(start_page, end_page + 1):
                page = doc[p_num - 1]
                page_text = page.get_text("text")
                if not page_text:
                    logger.debug(f"No text found on page {p_num} for section '{current_title}'")
                    continue
                
                normalized_page_text = self._normalize_text(page_text)

                start_slice = 0
                if p_num == start_page:
                    match_bbox, _, matched_text = self._find_title_on_page(current_title, page, self.fuzzy_title_threshold)
                    if match_bbox and matched_text:
                        pos_in_text = -1
                        normalized_match = self._normalize_text(matched_text)
                        
                        # Attempt 1: Normalized Exact Find
                        if normalized_match:
                            pos_in_text = normalized_page_text.find(normalized_match)

                        # Attempt 2: Fuzzy Search in Proximity
                        if pos_in_text == -1 and normalized_match:
                            # Define a search window based on bbox vertical position
                            start_y_prop = max(0, match_bbox.y0 - 50) / page.rect.height
                            end_y_prop = min(page.rect.height, match_bbox.y1 + 50) / page.rect.height
                            start_search_char = int(start_y_prop * len(normalized_page_text))
                            end_search_char = int(end_y_prop * len(normalized_page_text))
                            search_window = normalized_page_text[start_search_char:end_search_char]
                            
                            # Find best match in the window
                            result = process.extractOne(normalized_match, [search_window], scorer=fuzz.partial_ratio)
                            if result and result[1] > 90: # High confidence fuzzy match
                                found_str_pos = search_window.find(result[0])
                                if found_str_pos != -1:
                                     # Adjust position to be relative to the full page text
                                    pos_in_text = start_search_char + found_str_pos
                                    logger.debug(f"Found title '{current_title}' with fuzzy search in proximity.")


                        if pos_in_text != -1:
                            # Move slice start to after the title
                            start_slice = pos_in_text + len(normalized_match)
                        else:
                            # Fallback to bbox approximation
                            logger.warning(f"Could not find exact matched_text ('{matched_text}') for title '{current_title}'. Using bbox approximation.")
                            start_slice = int(match_bbox.y1 / page.rect.height * len(page_text))
                    else:
                        logger.warning(f"Could not reliably find title '{current_title}' on page {p_num}. Section content may start inaccurately.")


                end_slice = len(page_text)
                # If we are on the page where the next section starts, find its title and truncate content
                if p_num == end_page and next_title and next_title != current_title:
                    match_bbox, _, matched_text = self._find_title_on_page(next_title, page, self.fuzzy_title_threshold)
                    if match_bbox and matched_text:
                        pos_in_text = -1
                        normalized_match = self._normalize_text(matched_text)

                        # Attempt 1: Normalized Exact Find
                        if normalized_match:
                            pos_in_text = normalized_page_text.find(normalized_match)
                        
                        # Attempt 2: Fuzzy Search in Proximity
                        if pos_in_text == -1 and normalized_match:
                            start_y_prop = max(0, match_bbox.y0 - 50) / page.rect.height
                            end_y_prop = min(page.rect.height, match_bbox.y1 + 50) / page.rect.height
                            start_search_char = int(start_y_prop * len(normalized_page_text))
                            end_search_char = int(end_y_prop * len(normalized_page_text))
                            search_window = normalized_page_text[start_search_char:end_search_char]
                            
                            result = process.extractOne(normalized_match, [search_window], scorer=fuzz.partial_ratio)
                            if result and result[1] > 90:
                                found_str_pos = search_window.find(result[0])
                                if found_str_pos != -1:
                                    pos_in_text = start_search_char + found_str_pos
                                    logger.debug(f"Found next_title '{next_title}' with fuzzy search in proximity.")

                        if pos_in_text != -1 and not (p_num == start_page and pos_in_text < start_slice):
                             # Truncate content before the next title
                            end_slice = pos_in_text
                        else:
                            # Fallback
                            logger.warning(f"Could not find exact matched_text ('{matched_text}') for next_title '{next_title}'. Using bbox approximation.")
                            end_slice = int(match_bbox.y0 / page.rect.height * len(page_text))

                if start_slice < end_slice:
                    content_parts.append(page_text[start_slice:end_slice])

            full_content = "\n".join(content_parts).strip()
            current_section['Content'] = full_content

        return sorted_toc

    def _generate_toc_from_pdf(self) -> List[Dict[str, Any]]:
        """Generates a TOC from the PDF outline or page text analysis."""
        def process_outline_item(item, reader, level=0):
            if isinstance(item, list):
                for subitem in item:
                    yield from process_outline_item(subitem, reader, level)
            else:
                try:
                    title = item.title.strip()
                    page_num = reader.get_destination_page_number(item) + 1
                    heading_number = self._generate_heading_number(title, level)
                    yield {
                        "title": title,
                        "heading_number": heading_number,
                        "page": page_num,
                        "level": level,
                    }
                except AttributeError:
                    logger.warning(f"Outline item missing 'title': {item}")
                except Exception as e:
                    logger.warning(f"Error processing outline item: {e}")

        def _generate_fallback_toc():
            logger.info("Generating fallback TOC by analyzing page text and font styles.")
            toc_items = []
            with fitz.open(self.pdf_path) as doc:
                # First pass: try improved regex for numbered headings
                for page_num, page in enumerate(doc):
                    page_text = page.get_text("text")
                    # Improved regex to catch more heading formats
                    matches = re.finditer(
                        r'^(?:\s*(?:[IVX]+|[A-Z]|\d+)(?:\.[\d]+)*\.?)\s+([^\n]+)',
                        page_text, re.MULTILINE
                    )
                    for match in matches:
                        title = match.group(0).strip()
                        toc_items.append({
                            "title": title,
                            "heading_number": self._generate_heading_number(title, 1),
                            "page": page_num + 1,
                            "level": 1,
                            "Content": "" # Will be populated later
                        })
                
                # Second pass (if needed): Analyze font sizes for headings
                if not toc_items:
                    logger.info("No numbered headings found, analyzing font sizes.")
                    all_font_sizes = []
                    for page in doc:
                        for block in page.get_text("dict")["blocks"]:
                            for line in block.get("lines", []):
                                for span in line.get("spans", []):
                                    all_font_sizes.append(span["size"])
                    
                    if all_font_sizes:
                        try:
                            dominant_size = statistics.mode(all_font_sizes)
                            heading_threshold = dominant_size * 1.15
                            logger.info(f"Dominant font size: {dominant_size:.2f}, heading threshold: {heading_threshold:.2f}")

                            for page_num, page in enumerate(doc):
                                blocks = page.get_text("dict")["blocks"]
                                for block in blocks:
                                    for line in block.get("lines", []):
                                        if line.get("spans"):
                                            line_size = line["spans"][0]["size"]
                                            is_bold = "bold" in line["spans"][0]["font"].lower()
                                            
                                            # A heading is larger, often bold, and relatively short.
                                            if line_size > heading_threshold or (line_size >= dominant_size and is_bold):
                                                title = "".join(s["text"] for s in line["spans"]).strip()
                                                if 2 < len(title) < 150: # Filter out noise
                                                    toc_items.append({
                                                        "title": title,
                                                        "heading_number": self._generate_heading_number(title, 2),
                                                        "page": page_num + 1,
                                                        "level": 2,
                                                        "Content": ""
                                                    })
                        except statistics.StatisticsError:
                            logger.warning("Could not determine a single dominant font size.")


            if not toc_items:
                logger.warning("No sections found in text analysis. Using single document section.")
                text = "\n".join(page.get_text("text") for page in doc)
                toc_items = [{
                    "title": "Full Document Content",
                    "heading_number": "full_document",
                    "page": 1,
                    "level": 0,
                    "Content": text
                }]
            # Post-process to remove duplicate/subsumed titles
            unique_toc = []
            seen_titles = set()
            for item in sorted(toc_items, key=lambda x: (x['page'], x['level'])):
                if item['title'].lower() not in seen_titles:
                    unique_toc.append(item)
                    seen_titles.add(item['title'].lower())

            logger.info(f"Generated {len(unique_toc)} TOC items from fallback analysis.")
            return unique_toc

        try:
            with open(self.pdf_path, "rb") as f_pypdf, fitz.open(self.pdf_path) as doc_fitz:
                reader = PdfReader(f_pypdf)
                if not reader.outline:
                    logger.warning(f"PDF '{self.pdf_path}' has no outline. Using fallback TOC.")
                    raw_toc_items = _generate_fallback_toc()
                    if not raw_toc_items: return []
                else:
                    raw_toc_items = list(process_outline_item(reader.outline, reader))
                
                if not raw_toc_items:
                    logger.warning("No TOC items extracted. Using fallback TOC.")
                    raw_toc_items = _generate_fallback_toc()
                    if not raw_toc_items: return []

                sections_with_content = self._extract_content_for_sections(raw_toc_items, doc_fitz)
                
                logger.info(f"Extracted {len(sections_with_content)} TOC items from PDF outline.")
                for item in sections_with_content:
                    logger.info(f"TOC Item: '{item['title']}', Page: {item['page']}, Content Length: {len(item.get('Content', ''))}")
                return sections_with_content
        except FileNotFoundError:
            logger.error(f"PDF file not found: {self.pdf_path}")
            return []
        except PdfReadError as e:
            logger.error(f"Error reading PDF: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error generating TOC: {e}")
            logger.error(traceback.format_exc())
            return []

    def _generate_heading_number(self, title: str, level: int) -> str:
        number_match = re.match(r'^(\d+(?:\.\d+)*)', title.strip())
        if number_match:
            return number_match.group(1)
        roman_match = re.match(r'^([IVX]+)\.?\s', title.strip())
        if roman_match:
            return roman_match.group(1)
        clean_title = re.sub(r'[^\w\s-]', '', title).strip().replace(' ', '_')
        return f"section_{level}_{clean_title[:30]}"

    def load_config(self, file_path: str = "gemini_config.json") -> Optional[dict]:
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            logger.error(f"Configuration file {file_path} not found.")
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {file_path}: {e}")
        except Exception as e:
            logger.error(f"An unexpected error occurred loading config {file_path}: {e}")
            logger.error(traceback.format_exc())
        return None

    def _rotate_and_reconfigure_api(self) -> bool:
        current_key_ending = self.api_key[-4:] if self.api_key else "N/A"
        logger.info(f"Quota exceeded or invalid response with key ending ...{current_key_ending}. Rotating API key...")
        try:
            self.rotator.rotate_api_key()
            self.api_key = self.rotator.get_api_key()
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(
                self.model_name,
                generation_config={"response_mime_type": self.response_mime_type}
            )
            logger.info(f"Switched to new API key ending: ...{self.api_key[-4:]}")
            return True
        except Exception as e:
            logger.error(f"Error during API key rotation or reconfiguration: {e}")
            logger.error(traceback.format_exc())
            return False

    def _attempt_generate_content(self, prompt: Union[str, List[Any]]) -> str:
        if not self.initialization_ok or not self.rotator or not self.model:
            return "Error: Generator not properly initialized."

        # Count input tokens if enabled
        input_tokens = 0
        if self.count_token:
            try:
                input_token_count = self.model.count_tokens(prompt)
                input_tokens = input_token_count.total_tokens
            except Exception as e:
                logger.warning(f"Failed to count input tokens: {e}")

        max_attempts = len(self.rotator.api_keys)
        for attempt in range(max_attempts):
            current_key_ending = self.api_key[-4:] if self.api_key else "N/A"
            try:
                logger.info(f"Attempt {attempt + 1}/{max_attempts} using key ending ...{current_key_ending}")
                response = self.model.generate_content(prompt)
                
                if response and hasattr(response, "text") and response.text:
                    # Count output tokens if enabled
                    output_tokens = 0
                    if self.count_token:
                        try:
                            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                                output_tokens = response.usage_metadata.candidates_token_count
                            else:
                                # Fallback: estimate tokens based on response text
                                output_token_count = self.model.count_tokens(response.text)
                                output_tokens = output_token_count.total_tokens
                        except Exception as e:
                            logger.warning(f"Failed to count output tokens: {e}")
                    
                    try:
                        json.loads(response.text)
                        
                        # Track tokens if enabled and successful
                        if self.count_token and input_tokens > 0:
                            self.total_input_tokens += input_tokens
                            self.total_output_tokens += output_tokens
                            
                            # Calculate cost for this request
                            cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                            
                            logger.info(f"Token usage - Input: {input_tokens:,}, Output: {output_tokens:,}")
                            if cost_info:
                                logger.info(f"Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                                cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                                logger.info(f"Cumulative cost: ${cumulative_cost:.6f}")
                        
                        return response.text
                    except json.JSONDecodeError as je:
                        logger.warning(f"Invalid JSON response on attempt {attempt + 1}: {je}")
                        logger.debug(f"Invalid JSON: {response.text[:200]}...")
                        if attempt < max_attempts - 1:
                            if not self._rotate_and_reconfigure_api():
                                return f"Error: Failed to rotate API key after invalid JSON - {je}"
                            continue
                        else:
                            logger.error(f"Exhausted all {max_attempts} attempts with invalid JSON.")
                            return f"Error: Invalid JSON after all attempts - {je}"
                elif response and hasattr(response, "prompt_feedback"):
                    logger.warning(f"Received response with feedback: {response.prompt_feedback}")
                    return f"Error: Generation failed due to API feedback - {response.prompt_feedback}"
                else:
                    logger.warning(f"Received unexpected response format or no text: {response}")
                    return "Error: No valid response text generated."
            except ResourceExhausted as e:
                logger.warning(f"ResourceExhausted error on attempt {attempt + 1}.")
                if attempt < max_attempts - 1:
                    if not self._rotate_and_reconfigure_api():
                        return f"Error: Failed to rotate API key - {e}"
                else:
                    logger.error(f"Exhausted all {max_attempts} API keys.")
                    return f"Error: All API keys exhausted - {e}"
            except ValueError as e:
                if "400 The model does not support" in str(e):
                    logger.error(f"Model does not support function calling, prompt: {prompt}")
                    return f"Error: Model configuration issue - {e}"
                else:
                    # Handle other ValueErrors if necessary
                    logger.error(f"ValueError during API call on attempt {attempt + 1}: {e}")
                    logger.error(traceback.format_exc())
                    return f"Error: A ValueError occurred - {e}"
            except Exception as e:
                logger.error(f"Unexpected error during API call on attempt {attempt + 1}: {e}")
                logger.error(traceback.format_exc())
                return f"Error: An unexpected exception occurred - {e}"
        return "Error: Generation failed after all attempts."

    def get_section_data_for_page(self, page_num: int, item_bbox: Optional[fitz.Rect] = None) -> Optional[Dict[str, Any]]:
        """
        Finds the section data for a given page number.
        If item_bbox is provided, it disambiguates when multiple sections start on the same page
        by finding the last section title that appears above the item.
        """
        logger.debug(f"Searching section for page {page_num}" + (f" with item_bbox y0={item_bbox.y0}" if item_bbox else ""))
        if not self.doc_sections:
            logger.warning("No document sections available.")
            return None

        # Find all sections that could potentially contain this page's content
        candidate_sections = [s for s in self.doc_sections if s.get('page', float('inf')) <= page_num]
        if not candidate_sections:
            logger.warning(f"No TOC section found starting on or before page {page_num}.")
            return None # Early exit if no candidates

        # If an item_bbox is provided, we can do a more precise search on the item's page
        if item_bbox:
            with fitz.open(self.pdf_path) as doc:
                # This check is important to avoid errors with invalid page numbers
                if not (0 <= page_num - 1 < len(doc)):
                    logger.warning(f"Page number {page_num} is out of bounds for document.")
                    return None
                page = doc[page_num - 1]
                
                # Find all sections that start on this specific page
                sections_on_this_page = [s for s in self.doc_sections if s.get('page') == page_num]
                
                if sections_on_this_page:
                    last_section_above_item = None
                    best_title_y1 = -1

                    for section in sections_on_this_page:
                        title_bbox, _, _ = self._find_title_on_page(section['title'], page, self.fuzzy_title_threshold)
                        
                        if title_bbox and title_bbox.y1 <= item_bbox.y0:
                            # This title is located above the item, so it's a candidate.
                            # We want the one that is closest (lowest) to the item.
                            if title_bbox.y1 > best_title_y1:
                                best_title_y1 = title_bbox.y1
                                last_section_above_item = section
                    
                    if last_section_above_item:
                        logger.info(f"Disambiguated: Selected section '{last_section_above_item.get('title')}' for item on page {page_num} based on vertical position.")
                        return last_section_above_item

        # Fallback logic: return the latest section that starts on or before the page_num
        # Added title to the key for a stable sort in case of same page number
        current_section = max(candidate_sections, key=lambda x: (x.get('page', -1), x.get('title', ''))) 
        
        if current_section:
            logger.info(f"Selected section for page {page_num}: {current_section.get('title', 'No title')} (Fallback method)")
            return current_section
        else:
            # This part of the code is now less likely to be hit due to earlier checks
            logger.warning(f"No TOC section found for page {page_num}. This should not happen if candidate_sections is not empty.")
            return None

    def categorize_image(self, caption: str, section_text: str, table_text: Optional[str] = None) -> Tuple[str, float]:
        """Categorizes an image using a hybrid N-gram and FuzzyWuzzy matching model."""
        scores = {category: 0.0 for category in self.categories}
        
        # Define weights for different evidence sources and match types
        weights = {
            'caption': {'exact': 1.2, 'fuzzy': 0.7}, # Increased weight for caption
            'section': {'exact': 0.4, 'fuzzy': 0.2},
            'table_boost': 120.0  # Slightly reduced table boost
        }

        # 1. Table Content is a very strong signal for screen_ui
        if table_text:
            table_text_lower = table_text.lower()
            ui_table_keywords = ['element name', 'html tag', 'purpose', 'control', 'selector', 'field', 'id']
            if any(kw in table_text_lower for kw in ui_table_keywords):
                scores['screen_ui'] += weights['table_boost']
                logger.debug("Category boosted to 'screen_ui' by UI table content.")

        texts_to_analyze = {
            'caption': caption,
            'section': section_text
        }
        
        for source_name, text in texts_to_analyze.items():
            if not text:
                continue
            
            text_lower = text.lower()
            
            for category, keyword_defs in self.processed_categories.items():
                for keyword_def in keyword_defs:
                    phrase = keyword_def['phrase']
                    n_gram_count = keyword_def['n_gram_count']
                    base_score = 60.0 # Increased base score for a match
                    
                    # A. Exact N-gram match (higher score for longer n-grams)
                    if phrase in text_lower:
                        # Score is weighted by N-gram length and source
                        score_increase = base_score * n_gram_count * weights[source_name]['exact']
                        scores[category] += score_increase
                        logger.debug(f"Exact match for '{phrase}' in {source_name} added {score_increase:.2f} to {category}")

                    # B. Fuzzy match on the phrase for resilience
                    else: # Only do fuzzy if no exact match for this phrase, to avoid double counting
                        # Use fuzz.partial_ratio to find phrase even if it's inside a larger string
                        fuzzy_score = fuzz.partial_ratio(phrase, text_lower)
                        if fuzzy_score > 88: # Stricter threshold for fuzzy matches
                            score_increase = (fuzzy_score / 100) * base_score * n_gram_count * weights[source_name]['fuzzy']
                            scores[category] += score_increase
                            logger.debug(f"Fuzzy match ({fuzzy_score}%) for '{phrase}' in {source_name} added {score_increase:.2f} to {category}")

        if not any(s > 0 for s in scores.values()):
            logger.warning(f"No category clues found for image with caption '{caption}'")
            return "unknown", 0.0
        
        best_category = max(scores, key=scores.get)
        best_score = scores[best_category]

        if best_score < self.MIN_ABSOLUTE_CATEGORY_SCORE_THRESHOLD:
            logger.info(f"Best score for '{best_category}' ({best_score:.2f}) is below threshold ({self.MIN_ABSOLUTE_CATEGORY_SCORE_THRESHOLD}). Classifying as 'unknown'.")
            return "unknown", 0.0

        total_score = sum(scores.values())
        confidence = (best_score / total_score) if total_score > 0 else 0.0
        
        logger.info(f"Categorized as '{best_category}' with score {best_score:.2f} and confidence {confidence:.2f} (Scores: {scores}) for caption: '{caption}'")
        return best_category, confidence

    def convert_table_to_markdown(self, table_data: List[List[str]]) -> str:
        if not table_data:
            return ""
        markdown = "| " + " | ".join(map(str, table_data[0])) + " |\n"
        markdown += "| " + " | ".join(["---"] * len(table_data[0])) + " |\n"
        for row in table_data[1:]:
            markdown += "| " + " | ".join(map(str, row)) + " |\n"
        return markdown

    def find_closest_item(self, source_bbox: fitz.Rect, items_list: List[Dict], text_key: str = 'text', search_radius: int = 500, preferred_prefix: Optional[str] = None, disallow_prefix: Optional[str] = None) -> Tuple[str, Optional[Dict]]:
        """
        Finds the closest item within a search radius, with logic for preferred and disallowed prefixes.
        """
        best_item_text = ""
        best_item_obj = None
        min_dist = float('inf')

        for item in items_list:
            if item.get('used'):
                continue
            
            item_text_raw = item.get(text_key, "")
            item_text = item_text_raw.strip().lower()

            # Skip items with a disallowed prefix
            if disallow_prefix and item_text.startswith(disallow_prefix.lower()):
                continue

            item_bbox = item['bbox']
            
            # Calculate vertical and horizontal distances
            v_dist = max(0, source_bbox.y0 - item_bbox.y1, item_bbox.y0 - source_bbox.y1)
            h_dist = max(0, source_bbox.x0 - item_bbox.x1, item_bbox.x0 - source_bbox.x1)

            # Weighted Euclidean distance, heavily prioritizing vertical proximity
            dist = (v_dist**2 + (h_dist * 0.2)**2)**0.5

            if dist < search_radius:
                # Give a massive bonus (reduce distance) if the preferred prefix is found.
                if preferred_prefix and item_text.startswith(preferred_prefix.lower()):
                    dist *= 0.1  # 90% distance reduction, a huge advantage
                    logger.debug(f"Applied prefix bonus to '{item_text_raw[:30]}...' (new distance: {dist:.2f})")
                
                # Also apply a bonus for general caption patterns, but less than the preferred one
                elif re.match(r'^(figure|table)\s+\d+', item_text, re.IGNORECASE):
                    dist *= 0.5 # Boost score for matching caption pattern
                
                if dist < min_dist:
                    min_dist = dist
                    best_item_text = item_text_raw
                    best_item_obj = item
                    
        if best_item_obj:
            logger.debug(f"Found closest item: {best_item_text[:50]}... (distance: {min_dist})")
        return best_item_text, best_item_obj

    def extract_images_and_captions(self) -> List[Dict[str, Any]]:
        try:
            doc = fitz.open(self.pdf_path)
            image_data = []
            processed_image_bboxes = [] # Keep track of bboxes for duplication check
            logger.info(f"Opened PDF: {self.pdf_path}, {len(doc)} pages. Starting advanced extraction...")

            all_tables = []
            all_captions = []

            # First pass: Extract all potential captions and tables from the document
            logger.info("Pass 1: Extracting all tables and potential captions...")
            for p_num, page in enumerate(doc):
                # Extract tables with UI element keywords
                try:
                    for table in page.find_tables():
                        table_content = table.extract()
                        if not table_content or not table_content[0]: continue
                        header_text = ' '.join(map(str, table_content[0])).lower()
                        keywords = ['element', 'component', 'control', 'selector', 'purpose', 'field', 'id', 'description', 'name', 'tag']
                        if any(fuzz.partial_ratio(keyword, header_text) > 85 for keyword in keywords):
                            all_tables.append({
                                'table_markdown': self.convert_table_to_markdown(table_content),
                                'bbox': fitz.Rect(table.bbox), 'used': False, 'page_num': p_num + 1
                            })
                except Exception as e:
                    logger.warning(f"Error finding tables on page {p_num+1}: {e}")

                # Extract text blocks that look like captions
                for block in page.get_text("blocks"):
                    block_text = block[4].strip().replace('\n', ' ')
                    if re.match(r'^(Figure|Fig\.|Table)\s+\d+[:.]?', block_text, re.IGNORECASE):
                        all_captions.append({
                            'text': block_text, 'bbox': fitz.Rect(block[:4]), 'used': False, 'page_num': p_num + 1
                        })
            logger.info(f"Found {len(all_tables)} potential UI tables and {len(all_captions)} potential captions in the document.")

            # Second pass: Extract images and associate them with captions and tables
            logger.info("Pass 2: Extracting images and associating with captions/tables...")
            for page_num, page in enumerate(doc):
                images_on_page = page.get_images(full=True)
                for img_index, img_info in enumerate(images_on_page):
                    try:
                        img_bbox = page.get_image_bbox(img_info, transform=False)
                        if not img_bbox or img_bbox.is_empty or img_bbox.width < 50 or img_bbox.height < 50:
                            logger.warning(f"Skipping small or invalid image on page {page_num + 1}")
                            continue

                        # BBox overlap detection for visually duplicate images
                        is_duplicate = False
                        for proc_bbox in processed_image_bboxes:
                            intersect_rect = fitz.Rect(img_bbox).intersect(proc_bbox)
                            
                            # Sửa lỗi: Diện tích được tính bằng width * height
                            intersect_area = intersect_rect.width * intersect_rect.height
                            img_bbox_area = img_bbox.width * img_bbox.height
                            proc_bbox_area = proc_bbox.width * proc_bbox.height
                            
                            union_area = img_bbox_area + proc_bbox_area - intersect_area
                            
                            if union_area > 0 and (intersect_area / union_area) > 0.95:
                                logger.warning(f"Skipping visually duplicate image (IoU > 95%) on page {page_num + 1}.")
                                is_duplicate = True
                                break
                        if is_duplicate:
                            continue
                        
                        # --- Sophisticated Caption and Table Search ---
                        best_caption, best_caption_item = "", None
                        best_table, best_table_item = "", None

                        # 1. Search on the same page for a "Figure" caption. Disallow "Table" captions for images.
                        captions_on_this_page = [c for c in all_captions if c['page_num'] == page_num + 1]
                        best_caption, best_caption_item = self.find_closest_item(
                            img_bbox, 
                            captions_on_this_page, 
                            preferred_prefix="Figure",
                            disallow_prefix="Table"
                        )

                        # 2. Cross-page caption search (if no good caption found)
                        if not best_caption_item:
                            # Check top of next page
                            if page_num + 1 < len(doc):
                                captions_on_next_page = [c for c in all_captions if c['page_num'] == page_num + 2]
                                # Look for captions at the very top of the next page
                                top_of_next_page_captions = [c for c in captions_on_next_page if c['bbox'].y0 < 100]
                                if top_of_next_page_captions:
                                    logger.debug(f"Image at bottom of page {page_num+1}, checking top of page {page_num+2} for caption.")
                                    best_caption, best_caption_item = self.find_closest_item(
                                        img_bbox, 
                                        top_of_next_page_captions, 
                                        search_radius=800, 
                                        preferred_prefix="Figure",
                                        disallow_prefix="Table"
                                    )

                        if best_caption_item:
                            best_caption_item['used'] = True

                        # Associate best table, this can still be associated even if caption is for a figure
                        tables_on_this_page = [t for t in all_tables if t['page_num'] == page_num + 1]
                        # We don't need to disallow "Figure" here as tables are separate objects
                        best_table, best_table_item = self.find_closest_item(img_bbox, tables_on_this_page, 'table_markdown')

                        if best_table_item:
                            best_table_item['used'] = True
                            logger.info(f"Associated table with image on page {page_num+1}: {best_table[:100]}...")

                        # --- Image Processing ---
                        xref = img_info[0]
                        base_image = doc.extract_image(xref)
                        image = Image.open(BytesIO(base_image["image"]))
                        
                        section_data = self.get_section_data_for_page(page_num + 1, img_bbox)
                        section_title = section_data.get('title', 'Unknown Section') if section_data else "Unknown Section"
                        section_content = section_data.get('Content', page.get_text()) if section_data else page.get_text()
                        
                        category, confidence = self.categorize_image(best_caption, section_content, table_text=best_table)
                        
                        if confidence < 0.3 and category != 'unknown':
                            logger.warning(f"Low confidence ({confidence:.2f}) for categorizing image {len(image_data) + 1} as '{category}'. Flagging as 'unknown'.")
                            category = 'unknown'

                        current_index = len(image_data)
                        image_data.append({
                            "image": image,
                            "index": current_index,
                            "caption": best_caption,
                            "section_title": section_title,
                            "section_content": section_content,
                            "element_table": best_table,
                            "category": category,
                            "page_num": page_num + 1,
                            "confidence": confidence
                        })
                        processed_image_bboxes.append(img_bbox) # Add to list for duplication check
                        logger.info(f"Image {current_index + 1} (Page {page_num + 1}): Category='{category}', Confidence={confidence:.2f}, Caption='{best_caption}', Section='{section_title}', Table Found={'Yes' if best_table else 'No'}")
                    except Exception as e:
                        logger.error(f"Error processing image details (xref {img_info[0]} on page {page_num+1}): {e}")
                        logger.error(traceback.format_exc())

            doc.close()
            logger.info(f"Extraction complete. Found {len(image_data)} images.")
            return image_data
        except Exception as e:
            logger.error(f"Error extracting from PDF: {e}")
            logger.error(traceback.format_exc())
            return []

    def _process_single_image(self, image_data, image_index, category_dirs, results_by_category):
        """
        Processes a single image and generates JSON for it.
        This method will be called by multiple threads.
        """
        thread_start_time = time.time()
        category = image_data.get("category", "unknown")
        page_num = image_data.get("page_num")
        caption = image_data.get("caption", "N/A")
        section = image_data.get("section_title", "N/A")
        
        print(f"[Thread {image_index + 1}] Starting JSON processing for image {image_index + 1} (Page {page_num}, Category: {category})")
        
        try:
            # Save image to file
            if image_data.get("image"):
                try:
                    image_path = os.path.join(
                        category_dirs[category],
                        f"{self.base_filename}_image_{image_index + 1}_page_{page_num}.png"
                    )
                    image_data["image"].save(image_path, "PNG")
                    logger.info(f"[Thread {image_index + 1}] Saved image to {image_path}")
                except Exception as e:
                    logger.error(f"[Thread {image_index + 1}] Error saving image: {e}")

            # Process image to JSON
            result = self.process_image_to_json(image_data)
            
            # Thread-safe storage of results
            with self.results_lock:
                if "error" in result:
                    self.processing_stats['failed_images'] += 1
                    logger.error(f"[Thread {image_index + 1}] Processing failed: {result.get('error', 'Unknown error')}")
                else:
                    if category == "unknown":
                        self.processing_stats['unknown_images'] += 1
                        logger.info(f"[Thread {image_index + 1}] Successfully processed 'unknown' category image.")
                    else:
                        self.processing_stats['successful_images'] += 1
                    results_by_category[category].append(result)
                    logger.info(f"[Thread {image_index + 1}] Successfully processed JSON for category '{category}'")

            thread_elapsed_time = time.time() - thread_start_time
            print(f"[Thread {image_index + 1}] Completed ({thread_elapsed_time:.2f}s). Image {image_index + 1} processing finished.")

        except Exception as e:
            error_msg = f"Error processing image {image_index + 1}: {e}"
            logger.error(f"[Thread {image_index + 1}] {error_msg}")
            logger.error(traceback.format_exc())
            with self.results_lock:
                self.processing_stats['failed_images'] += 1

    def process_image_to_json(self, image_data: Dict[str, Any]) -> Dict[str, Any]:
        image = image_data.get("image")
        category = image_data.get("category", "unknown")
        image_index = image_data.get("index")
        caption = image_data.get("caption", "N/A")
        page_num = image_data.get("page_num")
        table_markdown = image_data.get("element_table", "")
        section = image_data.get("section_title", "N/A")
        section_content = image_data.get("section_content", "")


        if not image:
            logger.warning(f"Skipping image {image_index + 1} from page {page_num}: No image provided")
            return {"error": "No image provided", "image_index": image_index}

        prompt_map = {
            "screen_flow": prompt_storing.screen_flow_prompt,
            "state_machine": prompt_storing.state_machine_prompt,
            "use_case": prompt_storing.use_case_prompt,
            "screen_ui": prompt_storing.screen_ui_prompt,
            "unknown": prompt_storing.unknown_diagram_prompt,
            "system_architecture": getattr(prompt_storing, 'system_architecture_prompt', None),
        }
        prompt_text_template = prompt_map.get(category)
        if not prompt_text_template:
            logger.error(f"No prompt defined for category {category} for image {image_index + 1}")
            return {"error": f"No prompt for category {category}", "image_index": image_index}

        # --- Dynamic Prompt Construction ---
        prompt_text = prompt_text_template
        context_info = f"Diagram from Page: {page_num}\n"
        if caption:
            context_info += f"Caption: {caption}\n"
        if section:
            context_info += f"Section: {section}\n"
        
        # Add specific instructions based on context
        if category == 'screen_ui' and table_markdown:
            prompt_text += "\n\n**Crucial**: Use the provided Markdown table of UI elements to meticulously describe each component in the image, its purpose, and its interactions. The table is the primary source of truth for element details."
            content_for_api = [f"{prompt_text}\n\n{context_info}\n[Image Analysis Required]", image, f"UI Element Table:\n{table_markdown}"]
        elif category == "unknown":
            content_for_api = [f"{prompt_text_template}\n\n{context_info}\n[Image Analysis Required]", image]
        else:
            # General case, include all context
            prompt_text += "\n\nPlease analyze the following image based on the context provided below."
            content_for_api = [f"{prompt_text}\n\n{context_info}\n[Image Analysis Required]", image]

        try:
            result_text = self._attempt_generate_content(content_for_api)

            if result_text.startswith("Error:"):
                logger.error(f"Error processing image Caption: {caption}, Section: {section}, Image Index: {image_index + 1}: {result_text}")
                return {"error": result_text, "image_index": image_index}

            extracted_json = JSONRepairUtility.extract_json_from_text(result_text)
            parsed_json, error_msg = JSONRepairUtility.safe_parse_json(extracted_json)

            if error_msg:
                logger.warning(f"JSON parsing issues for image {image_index + 1}: {error_msg}")
                repaired_json = JSONRepairUtility.repair_json(extracted_json)
                parsed_json, new_error_msg = JSONRepairUtility.safe_parse_json(repaired_json)
                if new_error_msg:
                    logger.error(f"Repair failed for image {image_index + 1}: {new_error_msg}")
                    return {"error": f"JSON parsing failed: {new_error_msg}", "image_index": image_index}
            
            fixed_json = JSONRepairUtility.fix_unhashable_keys(parsed_json)
            if isinstance(fixed_json, dict):
                fixed_json['diagram_source'] = {
                    'page_number': page_num,
                    'image_index': image_index,
                    'original_caption': caption,
                    'original_section_title': section, # Store the title for merging
                    'determined_category': category
                }
            return fixed_json
        except Exception as e:
            logger.error(f"Unexpected error processing image {image_index + 1}: {e}")
            logger.error(traceback.format_exc())
            return {"error": f"Unexpected processing error: {e}", "image_index": image_index}

    def _refine_content(self, content: str, title: str) -> str:
        """
        Refines the extracted content by removing leading title repetitions, preambles,
        and trailing truncated words using a more robust line-by-line analysis.
        """
        if not isinstance(content, str) or not content.strip():
            return ""

        original_content = content.strip()
        lines = original_content.split('\n')
        
        # --- Remove leading artifacts (Revised Logic) ---
        
        first_content_line_index = 0
        normalized_title = re.sub(r'^[0-9.IVXLC]+\s*', '', title.lower().strip()).strip()

        for i, line in enumerate(lines):
            stripped_line = line.strip()
            if not stripped_line: # Skip empty lines in the preamble
                first_content_line_index = i + 1
                continue

            # Condition 1: Check for explicit preamble patterns (Table, Figure, Numbering).
            # This regex is designed to be aggressive in identifying lines that are headers.
            is_preamble_pattern = re.match(
                r'^(?:(?:table|figure|fig\.|hình)\s*\d+[:.]?|(?:\d+(?:\.\d+)*\.?)|[IVXLC]+\.|\([a-z]\))\s*.*$',
                stripped_line,
                re.IGNORECASE
            )
            if is_preamble_pattern:
                logger.debug(f"Line '{stripped_line[:60]}...' identified as a preamble pattern.")
                first_content_line_index = i + 1
                continue
            
            # Condition 2: Check for fuzzy match with the main section title.
            # This is to catch sub-headers that repeat the title (e.g., "Description").
            # We add a length check to avoid misidentifying a real sentence that contains the title word.
            if normalized_title and fuzz.partial_ratio(normalized_title, stripped_line.lower()) > 85 and len(stripped_line) < len(original_content) * 0.4:
                logger.debug(f"Line '{stripped_line[:60]}...' identified as title repetition for '{title}'.")
                first_content_line_index = i + 1
                continue

            # If a line does not meet any header/preamble conditions, we assume it's the start of the core content.
            break
            
        refined_content = '\n'.join(lines[first_content_line_index:]).strip()

        # --- Remove trailing artifacts ---
        if refined_content:
            last_char = refined_content[-1]
            # If the content does not end with a standard punctuation mark, trim the last partial word.
            if last_char.isalnum(): 
                last_space_index = refined_content.rfind(' ')
                if last_space_index != -1:
                    # Avoid trimming very long "words" which might be URLs or other valid data.
                    if len(refined_content) - last_space_index < 50:
                        refined_content = refined_content[:last_space_index].strip()

        return refined_content

    def execute(self) -> List[Dict[str, Any]]:
        if not self.initialization_ok:
            logger.error("Execution skipped: Initialization was not successful.")
            return []

        category_dirs = {
            "screen_flow": os.path.join(self.output_directory, "screen_flow"),
            "state_machine": os.path.join(self.output_directory, "state_machine"),
            "use_case": os.path.join(self.output_directory, "use_case"),
            "screen_ui": os.path.join(self.output_directory, "screen_ui"),
            "unknown": os.path.join(self.output_directory, "unknown"),
            "system_architecture": os.path.join(self.output_directory, "system_architecture"),
        }
        for dir_path in category_dirs.values():
            os.makedirs(dir_path, exist_ok=True)

        image_data_list = self.extract_images_and_captions()
        if not image_data_list:
            logger.error("No images were extracted from the PDF.")
            return []

        # Reset processing stats for new execution
        self.processing_stats = {
            'successful_images': 0,
            'failed_images': 0,
            'unknown_images': 0
        }

        results_by_category = {category: [] for category in self.categories.keys()}
        results_by_category["unknown"] = []
        results_by_category["system_architecture"] = []
        
        logger.info(f"Starting multithreaded image processing for {len(image_data_list)} images...\n")
        overall_start_time = time.time()
        threads = []

        # Create and start threads for each image
        for image_data in image_data_list:
            image_index = image_data.get("index")
            thread = threading.Thread(
                target=self._process_single_image,
                args=(image_data, image_index, category_dirs, results_by_category)
            )
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        overall_elapsed_time = time.time() - overall_start_time
        logger.info(f"\n--- All image processing threads completed in {overall_elapsed_time:.2f} seconds ---")

        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()

        # Save results by category
        final_results = []
        for category, results in results_by_category.items():
            if results:
                output_json_path = os.path.join(self.output_directory, f"{self.base_filename}_{category}.json")
                try:
                    with open(output_json_path, 'w', encoding='utf-8') as f:
                        json.dump(results, f, ensure_ascii=False, indent=None)
                    logger.info(f"Saved {len(results)} JSON results for category '{category}' to {output_json_path}")
                    final_results.extend(results)
                except Exception as e:
                    logger.error(f"Error saving JSON results for category '{category}': {e}")

        logger.info("\n--- Execution Summary ---")
        logger.info(f"Total images found: {len(image_data_list)}")
        logger.info(f"Successfully processed images: {self.processing_stats['successful_images']}")
        logger.info(f"Failed images: {self.processing_stats['failed_images']}")
        logger.info(f"(unknown category): {self.processing_stats['unknown_images']}")
        logger.info("-------------------------")

        # Save all results to a single file for the merge step, this simplifies the next stage
        all_results_path = os.path.join(self.output_directory, f"{self.base_filename}_all_diagrams.json")
        try:
            with open(all_results_path, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False, indent=None)
            logger.info(f"Saved all {len(final_results)} results to '{all_results_path}' for merging.")
        except Exception as e:
            logger.error(f"Error saving all diagram results to a single file: {e}")


        return final_results

    def merge_all_json_outputs(self, output_path: str) -> Optional[str]:
        logger.info("\n--- Starting Intelligent JSON Merge Process ---")

        all_diagrams_file = os.path.join(self.output_directory, f"{self.base_filename}_all_diagrams.json")
        if not os.path.exists(all_diagrams_file):
            logger.error(f"Could not find the aggregated diagram file: {all_diagrams_file}")
            # Fallback to old glob method if the single file doesn't exist
            diagram_files = glob.glob(os.path.join(self.output_directory, f"{self.base_filename}_*.json"))
            if not diagram_files:
                logger.error("No diagram JSON files found to merge.")
                return None
        else:
            diagram_files = [all_diagrams_file]

        all_diagram_data = []
        logger.info(f"Found {len(diagram_files)} diagram JSON file(s) to process.")
        for f_path in diagram_files:
            try:
                with open(f_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_diagram_data.extend(data)
                    else:
                        all_diagram_data.append(data)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                logger.warning(f"Could not read or parse {f_path}: {e}")

        doc_sections = self.doc_sections
        if not doc_sections:
            logger.error("No document sections available for merging.")
            return None
            
        def clean_text_for_matching(text: str) -> str:
            text = re.sub(r'^[0-9.IVX]+\s*', '', text)
            return text.strip().lower()

        unmatched_diagram_analyses = []
        document_content_with_diagrams = [dict(s) for s in doc_sections]

        # TASK 1: Refine extracted section content before using it for matching
        logger.info("Refining extracted section content...")
        for section in document_content_with_diagrams:
            original_content = section.get("Content", "")
            title = section.get("title", "")
            if original_content and title:
                section["Content"] = self._refine_content(original_content, title)

        for diagram in all_diagram_data:
            is_matched = False
            source_info = diagram.get("diagram_source", {})
            original_caption = source_info.get("original_caption", "")
            original_section_title = source_info.get("original_section_title", "")
            diagram_page = source_info.get("page_number")

            if not diagram_page:
                unmatched_diagram_analyses.append(diagram)
                continue
            
            # --- High-Confidence Match: Original Section Title ---
            # The section identified during extraction is the strongest clue.
            for section in document_content_with_diagrams:
                if section.get("title") == original_section_title and abs(section.get("page", -1) - diagram_page) <= 2: # Tighter page drift
                    section.setdefault("associated_diagrams", []).append(diagram)
                    is_matched = True
                    logger.info(f"Matched diagram (Caption: '{original_caption}') to section '{section.get('title')}' via stored original section.")
                    break
            if is_matched: continue

            # --- Medium-Confidence Match: Caption or Figure Reference in Content ---
            cleaned_caption_for_search = clean_text_for_matching(original_caption) if original_caption else ""
            for section in document_content_with_diagrams:
                content = section.get("Content", "").lower()
                # Search for direct caption text or references like "see figure 5"
                fig_num_match = re.search(r'\d+', original_caption)
                fig_num = fig_num_match.group(0) if fig_num_match else None

                # Make caption search more robust
                found_by_caption = False
                if cleaned_caption_for_search:
                    # Check if at least a significant part of the caption is in the content
                    if len(cleaned_caption_for_search) > 10 and cleaned_caption_for_search in content:
                         found_by_caption = True
                    # Also check with fuzzy matching for resilience
                    elif fuzz.partial_ratio(cleaned_caption_for_search, content) > 90:
                        found_by_caption = True

                found_by_ref = fig_num and re.search(rf'\b(figure|fig|table)\s+{fig_num}\b', content, re.IGNORECASE)

                if found_by_caption or found_by_ref:
                    section.setdefault("associated_diagrams", []).append(diagram)
                    is_matched = True
                    match_method = "direct caption" if found_by_caption else "figure reference"
                    logger.info(f"Matched diagram (Caption: '{original_caption}') to section '{section.get('title')}' via {match_method} in content.")
                    break
            if is_matched: continue

            # --- Lower-Confidence Match: Fuzzy Title Matching ---
            best_match_score = 0
            best_match_section = None
            if cleaned_caption_for_search:
                for section in document_content_with_diagrams:
                    # Only consider sections around the diagram's page
                    if abs(section.get("page", float('inf')) - diagram_page) > 5: # Reduced search window
                        continue
                    
                    cleaned_title = clean_text_for_matching(section.get("title", ""))
                    score = fuzz.partial_ratio(cleaned_caption_for_search, cleaned_title)

                    if score > best_match_score and score > 80: # Slightly increased Fuzzy match threshold
                        best_match_score = score
                        best_match_section = section

            if best_match_section:
                best_match_section.setdefault("associated_diagrams", []).append(diagram)
                logger.info(f"Matched diagram (Caption: '{original_caption}') to section '{best_match_section.get('title')}' via fuzzy match (Score: {best_match_score}).")
            else:
                unmatched_diagram_analyses.append(diagram)
                logger.warning(f"No confident match found for diagram (Caption: '{original_caption}') on page {diagram_page}.")

        final_output = {
            "document_content_with_diagrams": document_content_with_diagrams,
            "unmatched_diagram_analyses": unmatched_diagram_analyses
        }

        merged_output_path = output_path
        try:
            # TASK 2: Ensure output directory exists
            output_dir = os.path.dirname(merged_output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                logger.info(f"Ensured output directory exists: {output_dir}")

            with open(merged_output_path, 'w', encoding='utf-8') as f:
                json.dump(final_output, f, ensure_ascii=False, indent=None)
            logger.info(f"Created consolidated output file at: {merged_output_path}")
            logger.info(f"Matched diagrams: {len(all_diagram_data) - len(unmatched_diagram_analyses)}")
            logger.info(f" Unmatched diagrams: {len(unmatched_diagram_analyses)}")
        except Exception as e:
            logger.error(f"Error writing consolidated JSON file: {e}")
            logger.error(traceback.format_exc())

        return merged_output_path

    def analyze_merged_json(self, merged_json_path: str, analyzed_json_path: str) -> Optional[List[dict]]:
        """
        Analyzes the merged JSON output to extract keywords and descriptions for each section
        using the Gemini API, similar to DocumentProcessor's analyze_json.

        Args:
            merged_json_path (str): Path to the merged JSON file containing document structure and diagrams.
            analyzed_json_path (str): Path to save the analysis results.

        Returns:
            Optional[List[dict]]: A list of dictionaries with analysis results, or None on failure.
        """
        try:
            with open(merged_json_path, 'r', encoding='utf-8') as f:
                merged_data = json.load(f)
            document_sections = merged_data.get("document_content_with_diagrams", [])
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Could not read or parse merged JSON file at {merged_json_path}: {e}")
            return None

        if not document_sections:
            logger.error("No document sections found in the merged JSON for analysis.")
            return None

        if not self.model:
            logger.error("Gemini model is not initialized. Cannot perform analysis.")
            return None

        # We send the sections for analysis, similar to DocumentProcessor
        combined_input = json.dumps(document_sections, indent=2, ensure_ascii=False)
        
        if len(combined_input) > 3000000: 
            logger.warning(f"Input JSON size ({len(combined_input)} chars) is very large. May exceed Gemini token limits.")

        # Using the same keyword extraction prompt
        prompt = f"""
            {prompt_storing.extract_keyword_prompt}

            -----BEGIN Input JSON:-----
            {combined_input}
            -----END Input JSON:-----
        """
        try:
            # Count input tokens if enabled
            input_tokens = 0
            if self.count_token:
                try:
                    input_token_count = self.model.count_tokens(prompt)
                    input_tokens = input_token_count.total_tokens
                    logger.info(f"Input tokens for analysis: {input_tokens:,}")
                except Exception as e:
                    logger.warning(f"Failed to count input tokens for analysis: {e}")
            
            logger.info(f"Sending {len(document_sections)} sections to Gemini for analysis...")
            # Here we use the generic API call wrapper which handles retries
            response_text = self._attempt_generate_content(prompt)
            
            if response_text.startswith("Error:"):
                 logger.error(f"Failed to get response from Gemini: {response_text}")
                 return None

            logger.info("Received Gemini response.")
            
            # JSON cleaning is now encapsulated in JSONRepairUtility
            extracted_json = JSONRepairUtility.extract_json_from_text(response_text)
            results, error_msg = JSONRepairUtility.safe_parse_json(extracted_json)

            if error_msg:
                logger.warning(f"Initial JSON parsing failed: {error_msg}. Attempting to repair.")
                repaired_json_str = JSONRepairUtility.repair_json(extracted_json)
                results, error_msg = JSONRepairUtility.safe_parse_json(repaired_json_str)
                if error_msg:
                    logger.error(f"JSON repair failed. Could not parse response. Error: {error_msg}")
                    logger.debug(f"Original response text: {response_text[:500]}...")
                    return None

            # The response should be a list of sections, same as the input
            if not isinstance(results, list): 
                logger.warning(f"Gemini response was valid JSON but not a list as expected. Type: {type(results)}. Attempting to wrap if it's a single dict for a single input section.")
                if isinstance(results, dict) and len(document_sections) == 1:
                    results = [results] 
                else:
                    logger.error("Gemini response is not a list and cannot be straightforwardly converted.")
                    # We can still save what we got for debugging.
            
            # TASK 2: Ensure output directory exists
            output_dir = os.path.dirname(analyzed_json_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                logger.info(f"Ensured output directory exists: {output_dir}")

            with open(analyzed_json_path, "w", encoding="utf-8") as out: 
                json.dump(results, out, indent=None, ensure_ascii=False)
            logger.info(f"✅ Analysis saved to {analyzed_json_path}")
            
            # Print final token summary if enabled
            if self.count_token:
                logger.info("Final token summary after analysis:")
                self.print_token_summary()
            
            return results
            
        except Exception as e: 
            logger.error(f"Error processing Gemini input or handling response: {e}")
            logger.error(traceback.format_exc())
            return None

if __name__ == "__main__":
    pdf_file = r"D:\WhyFPT\llmForWebFunctionalTesting\build\llm_functional_test\back_end\document\ecommerce-19-6.pdf"
    output_dir = f"back_end/output/ecommerce-19-6/diagrams"
    config_path = "back_end/document/gemini_config_flash.json"
    output_merged_path = f"back_end/output/ecommerce-19-6/document_processor/merged_output.json"
    analyzed_output_path = f"back_end/output/ecommerce-19-6/document_processor/analyzed_output.json"

    processor = DiagramProcessor(
        pdf_file=pdf_file,
        output_directory=output_dir,
        config_file=config_path
    )
    diagram_results = processor.execute()
    if diagram_results:
        merged_file = processor.merge_all_json_outputs(output_merged_path)
        if merged_file:
            logger.info("\n--- Starting Merged Content Analysis ---")
            processor.analyze_merged_json(merged_file, analyzed_output_path)
    else:
        logger.error("Execution produced no results, skipping merge and analysis.")