"""
Pipeline Service
Manages pipeline execution and real-time status updates
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, Optional, Any
import random

from app.models.schemas import (
    PipelineStatus, PipelineStep, PipelineStepStatus,
    LogMessage, LogLevel, create_default_pipeline_status
)
from app.services.module_manager import module_manager
from app.core.pipeline_registry import pipeline_registry
from app.core.logging import flush_log_queue

logger = logging.getLogger(__name__)

class PipelineService:
    """Service for managing pipeline execution"""
    
    def __init__(self, websocket_manager):
        self.websocket_manager = websocket_manager
        self.current_pipeline: Optional[PipelineStatus] = None
        self.execution_task: Optional[asyncio.Task] = None
        self.is_running = False

        logger.info("PipelineService initialized")

    async def initialize(self):
        """Initialize the pipeline service"""
        # Initialize module manager first
        await module_manager.initialize()

        # Create a default pipeline status using dynamic modules
        pipeline_id = str(uuid.uuid4())
        self.current_pipeline = self._create_dynamic_pipeline_status(pipeline_id)

        logger.info("Pipeline service initialized with dynamic modules")

    def _create_dynamic_pipeline_status(self, pipeline_id: str, document_id: Optional[str] = None) -> PipelineStatus:
        """Create pipeline status from registered modules"""
        # Get modules from registry
        modules = pipeline_registry.list_modules()
        execution_order = pipeline_registry.get_execution_order()

        # Create steps from registered modules
        steps = []
        for i, module_id in enumerate(execution_order, 1):
            module_config = next((m for m in modules if m["id"] == module_id), None)
            if module_config:
                step = PipelineStep(
                    id=module_config["id"],
                    name=module_config["name"],
                    number=i
                )
                steps.append(step)

        # Fallback to default steps if no modules registered
        if not steps:
            logger.warning("No modules registered, using default pipeline steps")
            return create_default_pipeline_status(pipeline_id, document_id)

        return PipelineStatus(
            pipeline_id=pipeline_id,
            document_id=document_id,
            steps=steps
        )

    async def cleanup(self):
        """Cleanup pipeline service"""
        if self.execution_task and not self.execution_task.done():
            self.execution_task.cancel()
            try:
                await self.execution_task
            except asyncio.CancelledError:
                pass
        logger.info("🧹 Pipeline service cleaned up")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current pipeline status as dict"""
        if not self.current_pipeline:
            return {}
        return self.current_pipeline.model_dump(mode='json')

    async def start_pipeline(self, document_id: str):
        """Start pipeline execution"""
        if self.is_running:
            raise ValueError("Pipeline is already running")

        # Create new pipeline instance using dynamic modules
        pipeline_id = str(uuid.uuid4())
        self.current_pipeline = self._create_dynamic_pipeline_status(pipeline_id, document_id)
        self.current_pipeline.start_time = datetime.now()
        self.current_pipeline.status = PipelineStepStatus.RUNNING

        self.is_running = True

        # Start execution task
        self.execution_task = asyncio.create_task(self._execute_pipeline())

        # Broadcast pipeline started
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution started", LogLevel.INFO)

        logger.info(f"Pipeline {pipeline_id} started for document {document_id}")

    async def stop_pipeline(self):
        """Stop pipeline execution"""
        if not self.is_running:
            raise ValueError("No pipeline is currently running")
        
        self.is_running = False
        
        if self.execution_task and not self.execution_task.done():
            self.execution_task.cancel()
        
        if self.current_pipeline:
            self.current_pipeline.status = PipelineStepStatus.CANCELLED
            self.current_pipeline.end_time = datetime.now()
            
            # Cancel current step
            if self.current_pipeline.current_step:
                self.current_pipeline.update_step_status(
                    self.current_pipeline.current_step, 
                    PipelineStepStatus.CANCELLED
                )
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution stopped", LogLevel.WARNING)
        
        logger.info("Pipeline execution stopped")

    async def start_pipeline_with_file(self, file_path: str):
        """Start pipeline execution with a specific file"""
        if self.is_running:
            raise ValueError("Pipeline is already running")

        # Extract document ID from file path
        import os
        document_id = os.path.splitext(os.path.basename(file_path))[0]

        # Create new pipeline instance using dynamic modules
        pipeline_id = str(uuid.uuid4())
        self.current_pipeline = self._create_dynamic_pipeline_status(pipeline_id, document_id)
        self.current_pipeline.start_time = datetime.now()
        self.current_pipeline.status = PipelineStepStatus.RUNNING

        # Store file path for modules to use
        if not hasattr(self.current_pipeline, 'metadata'):
            self.current_pipeline.metadata = {}
        self.current_pipeline.metadata["file_path"] = file_path

        self.is_running = True

        # Start execution task
        self.execution_task = asyncio.create_task(self._execute_pipeline())

        # Broadcast pipeline started
        await self._broadcast_status_update()
        await self._broadcast_log(f"Pipeline execution started with file: {file_path}", LogLevel.INFO)

        logger.info(f"Pipeline {pipeline_id} started with file {file_path}")

    async def execute_single_step(self, module_id: str, file_path: str):
        """Execute a single pipeline step"""
        if self.is_running:
            raise ValueError("Pipeline is already running")

        # Validate file exists
        import os
        if not os.path.exists(file_path):
            raise ValueError(f"File not found: {file_path}")

        # Find the step in current pipeline
        if not self.current_pipeline:
            # Create a minimal pipeline for single step execution
            pipeline_id = str(uuid.uuid4())
            document_id = os.path.splitext(os.path.basename(file_path))[0]
            logger.info(f"Creating new pipeline with document_id: {document_id}")
            self.current_pipeline = self._create_dynamic_pipeline_status(pipeline_id, document_id)
            logger.info(f"Pipeline created with document_id: {self.current_pipeline.document_id}")
        else:
            # Ensure document_id is set for single step execution
            if not self.current_pipeline.document_id:
                document_id = os.path.splitext(os.path.basename(file_path))[0]
                logger.info(f"Setting document_id for existing pipeline: {document_id}")
                self.current_pipeline.document_id = document_id
                logger.info(f"Pipeline document_id updated: {self.current_pipeline.document_id}")

        # Find the specific step
        target_step = None
        for step in self.current_pipeline.steps:
            if step.id == module_id:
                target_step = step
                break

        if not target_step:
            raise ValueError(f"Step {module_id} not found in pipeline")

        # Store file path for module to use
        if not hasattr(self.current_pipeline, 'metadata'):
            self.current_pipeline.metadata = {}
        self.current_pipeline.metadata["file_path"] = file_path

        logger.info(f"Starting execution of step {module_id} with file: {file_path}")

        # Execute the single step
        self.is_running = True
        try:
            await self._execute_step(target_step)

            # Update pipeline status to completed after single step execution
            if target_step.status == PipelineStepStatus.COMPLETED:
                self.current_pipeline.status = PipelineStepStatus.COMPLETED
                self.current_pipeline.end_time = datetime.now()
                self.current_pipeline.current_step = None

                if self.current_pipeline.start_time:
                    duration = (self.current_pipeline.end_time - self.current_pipeline.start_time).total_seconds()
                    self.current_pipeline.total_duration = duration

                await self._broadcast_status_update()
                await self._broadcast_log("Single step execution completed successfully", LogLevel.INFO)
                logger.info(f"Pipeline status updated to completed after single step: {module_id}")
            elif target_step.status == PipelineStepStatus.FAILED:
                self.current_pipeline.status = PipelineStepStatus.FAILED
                self.current_pipeline.end_time = datetime.now()
                await self._broadcast_status_update()
                await self._broadcast_log("Single step execution failed", LogLevel.ERROR)
                logger.info(f"Pipeline status updated to failed after single step: {module_id}")

        finally:
            self.is_running = False

        logger.info(f"Single step {module_id} executed with file {file_path}")

    async def _execute_pipeline(self):
        """Execute the pipeline steps"""
        try:
            if not self.current_pipeline:
                return
            
            for step in self.current_pipeline.steps:
                if not self.is_running:
                    break
                
                # Update current step
                self.current_pipeline.current_step = step.id
                
                # Start step execution
                await self._execute_step(step)
                
                # Small delay between steps
                await asyncio.sleep(1)
            
            # Pipeline completed
            if self.is_running:
                self.current_pipeline.status = PipelineStepStatus.COMPLETED
                self.current_pipeline.end_time = datetime.now()
                self.current_pipeline.current_step = None
                
                if self.current_pipeline.start_time:
                    duration = (self.current_pipeline.end_time - self.current_pipeline.start_time).total_seconds()
                    self.current_pipeline.total_duration = duration
                
                await self._broadcast_status_update()
                await self._broadcast_log("Pipeline execution completed successfully", LogLevel.INFO)
                
                logger.info("Pipeline execution completed")

        except asyncio.CancelledError:
            logger.info("Pipeline execution cancelled")
        except Exception as e:
            logger.error(f"Pipeline execution error: {e}")
            
            if self.current_pipeline:
                self.current_pipeline.status = PipelineStepStatus.FAILED
                self.current_pipeline.end_time = datetime.now()
                
                await self._broadcast_status_update()
                await self._broadcast_log(f"Pipeline execution failed: {str(e)}", LogLevel.ERROR)
        
        finally:
            self.is_running = False

    async def _execute_step(self, step: PipelineStep):
        """Execute a single pipeline step"""
        logger.info(f"Executing step: {step.name}")

        # Update step status to running
        step.status = PipelineStepStatus.RUNNING
        step.start_time = datetime.now()
        step.progress = 0.0

        await self._broadcast_status_update()
        await self._broadcast_log(f"Starting step: {step.name}", LogLevel.INFO)

        try:
            # Try to execute actual module
            module = pipeline_registry.get_module(step.id)
            logger.info(f"🔍 Debug - module exists: {module is not None}")
            logger.info(f"🔍 Debug - current_pipeline exists: {self.current_pipeline is not None}")
            logger.info(f"🔍 Debug - document_id: {getattr(self.current_pipeline, 'document_id', 'NOT_SET') if self.current_pipeline else 'NO_PIPELINE'}")

            if module and self.current_pipeline and self.current_pipeline.document_id:
                logger.info(f"✅ All conditions met - executing real module")
                await self._execute_real_module(step, module)
            else:
                # Fallback to simulation if module not available
                logger.warning(f"❌ Conditions not met - falling back to simulation")
                await self._simulate_step_execution(step)

        except Exception as e:
            logger.error(f"Error executing step {step.name}: {e}")
            step.status = PipelineStepStatus.FAILED
            step.error_message = str(e)
            await self._broadcast_log(f"Step failed: {step.name} - {e}", LogLevel.ERROR, step.id)

        # Finalize step
        if step.status == PipelineStepStatus.RUNNING:
            step.status = PipelineStepStatus.COMPLETED

        step.end_time = datetime.now()
        if step.start_time:
            step.duration = (step.end_time - step.start_time).total_seconds()

        await self._broadcast_status_update()
        logger.info(f"Step completed: {step.name}")

    async def _execute_real_module(self, step: PipelineStep, module):
        """Execute actual pipeline module"""
        try:
            logger.info(f"Starting module execution: {step.name}")

            # Get file path from pipeline metadata
            file_path = None
            if hasattr(self.current_pipeline, 'metadata') and self.current_pipeline.metadata:
                file_path = self.current_pipeline.metadata.get("file_path")

            if not file_path:
                file_path = f"uploads/{self.current_pipeline.document_id}.pdf"  # Fallback

            logger.info(f"Input file: {file_path}")
            logger.info(f"Document ID: {self.current_pipeline.document_id}")

            # Prepare input data for module
            input_data = {
                "document_id": self.current_pipeline.document_id,
                "file_path": file_path
            }

            logger.info(f"Validating module input...")

            # Update progress to show module is starting
            step.progress = 10.0
            await self._broadcast_status_update()
            await self._broadcast_log(f"Executing module: {step.name}", LogLevel.INFO, step.id)
            logger.info(f"Starting module execution: {step.name}")
            logger.info(f"Input file: {file_path}")
            logger.info(f"Document ID: {self.current_pipeline.document_id}")

            # Execute the module
            logger.info(f"Calling module_manager.execute_module for: {step.id}")
            result = await module_manager.execute_module(step.id, input_data)
            logger.info(f"Module execution result: {result}")

            # Update progress based on result
            if result.get("success"):
                logger.info(f"Module execution completed successfully: {step.name}")
                step.progress = 100.0
                step.status = PipelineStepStatus.COMPLETED  # Explicitly set status to completed
                step.output_files = list(result.get("output_files", {}).values())
                await self._broadcast_status_update()  # Broadcast status update immediately
                await self._broadcast_log(f"Module completed: {result.get('message', 'Success')}", LogLevel.INFO, step.id)
                logger.info(f"Module {step.name} finished with success")

                # Flush any remaining log messages in queue
                flush_log_queue()
            else:
                logger.error(f"Module execution failed: {step.name}")
                logger.error(f"Error: {result.get('error', 'Unknown error')}")
                step.status = PipelineStepStatus.FAILED
                step.error_message = result.get("error", "Unknown error")
                await self._broadcast_status_update()  # Broadcast status update for failed case too
                await self._broadcast_log(f"Module failed: {step.error_message}", LogLevel.ERROR, step.id)

        except Exception as e:
            logger.error(f"Exception in real module execution: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Exception details: {str(e)}")
            step.status = PipelineStepStatus.FAILED
            step.error_message = str(e)
            await self._broadcast_status_update()  # Broadcast status update for exception case

    async def _simulate_step_execution(self, step: PipelineStep):
        """Simulate step execution (fallback)"""
        await self._broadcast_log(f"Simulating step: {step.name} (module not available)", LogLevel.WARNING, step.id)

        # Simulate step execution with progress updates
        total_duration = random.uniform(2, 4)  # Shorter duration for simulation
        steps_count = 10

        for i in range(steps_count + 1):
            if not self.is_running:
                step.status = PipelineStepStatus.CANCELLED
                break

            # Update progress
            step.progress = (i / steps_count) * 100
            self.current_pipeline.overall_progress = self.current_pipeline.calculate_overall_progress()

            # Broadcast progress update
            await self._broadcast_status_update()

            await asyncio.sleep(total_duration / steps_count)

        # Add demo output files
        step.output_files = [f"{step.id}_output.json", f"{step.id}_log.txt"]

    async def _broadcast_status_update(self):
        """Broadcast pipeline status update to all clients"""
        if not self.current_pipeline:
            return
        
        await self.websocket_manager.broadcast({
            "type": "pipeline_status",
            "data": self.current_pipeline.model_dump(mode='json')
        })

    async def _broadcast_log(self, message: str, level: LogLevel, step_id: Optional[str] = None):
        """Broadcast log message to all clients"""
        log_message = LogMessage(
            level=level,
            message=message,
            step_id=step_id,
            pipeline_id=self.current_pipeline.pipeline_id if self.current_pipeline else None
        )
        
        await self.websocket_manager.broadcast({
            "type": "pipeline_log",
            "data": log_message.model_dump(mode='json')
        })

    async def pause_pipeline(self):
        """Pause pipeline execution"""
        if not self.is_running:
            raise ValueError("No pipeline is currently running")
        
        # This is a simplified pause - in a real implementation,
        # you'd need more sophisticated state management
        self.is_running = False
        
        if self.current_pipeline and self.current_pipeline.current_step:
            self.current_pipeline.update_step_status(
                self.current_pipeline.current_step,
                PipelineStepStatus.PAUSED
            )
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution paused", LogLevel.WARNING)
        
        logger.info("Pipeline execution paused")

    async def resume_pipeline(self):
        """Resume pipeline execution"""
        if self.is_running:
            raise ValueError("Pipeline is already running")
        
        if not self.current_pipeline:
            raise ValueError("No pipeline to resume")
        
        self.is_running = True
        
        # Resume from current step
        if self.current_pipeline.current_step:
            self.current_pipeline.update_step_status(
                self.current_pipeline.current_step,
                PipelineStepStatus.RUNNING
            )
        
        # Restart execution task
        self.execution_task = asyncio.create_task(self._execute_pipeline())
        
        await self._broadcast_status_update()
        await self._broadcast_log("Pipeline execution resumed", LogLevel.INFO)
        
        logger.info("Pipeline execution resumed")

    async def reset_pipeline(self):
        """Reset pipeline to initial state"""
        if self.is_running:
            logger.warning("Cannot reset pipeline while it's running")
            return

        if self.current_pipeline:
            # Reset all steps to pending
            for step in self.current_pipeline.steps:
                step.status = PipelineStepStatus.PENDING
                step.progress = 0.0
                step.start_time = None
                step.end_time = None
                step.duration = None
                step.error_message = None
                step.output_files = []

            # Reset pipeline status
            self.current_pipeline.status = PipelineStepStatus.PENDING
            self.current_pipeline.current_step = None
            self.current_pipeline.start_time = None
            self.current_pipeline.end_time = None
            self.current_pipeline.total_duration = None
            self.current_pipeline.overall_progress = 0.0
            self.current_pipeline.error_count = 0
            self.current_pipeline.warning_count = 0

            logger.info("Pipeline reset to initial state")

            # Broadcast status update to frontend
            await self._broadcast_status_update()
        else:
            logger.info("No pipeline to reset")
