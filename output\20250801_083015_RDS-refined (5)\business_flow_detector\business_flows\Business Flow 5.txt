1. <PERSON> navigates to StaffDashboardPage
2. Staff selects 'View My Training Requests' option in StaffDashboardPage
3. <PERSON> clicks on a specific request to view details in RequestListPage
Business Process Context: `{"heading_number": "1.1", "title": "1.1 Staff", "Content": "{\n \n\"source\":\n \n\"requests\",\n \n\"target\":\n \n\"request_history\",\n \n\"type\":\n \n\"one-to-many\"\n \n}\n \n]\n \n}\n \nII.\n \nRequirement\n \nSpecifications\n \n \n1.\n \nUse\n \nCase\n \nDescription\n \n1.1\n \nStaff\n \na.Diagram\n\n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \nview\n \ntheir\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"SendRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nStaff\n \nto\n \ncreate\n \nnew\n \nrequests\n \n(e.g.,\n \nTraining\n \nor\n \nRole\n \nChange).\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \nif\n \ndirectly\n \naccessible\n \nand\n \ndistinct\n \nfor\n \nStaff\n \nflow\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nlist'\n \n(e.g.,\n \nUC006,\n \nUC009)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"SendRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Send\n \nnew\n \nrequest'\n \n(e.g.,\n \nUC005,\n \nUC-RC001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestListPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"SendRequestPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nor\n \nafter\n \nsuccessful\n \nsubmission\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"StaffDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nStaff\n \nuser.\"\n \n}\n \n \nb.\n \nDescriptions\n \n \n1.1.1\n \nUse\n \nCase:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \n \n \nTable\n \n12\n \nUse\n \ncase\n \ndescription\n \nof\n  \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-TR001\n \nUse\n \nCase\n \nName:\n \nStaff\n \nCreates\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nStaff\n \nSecondary\n \nActors:\n \nSýtem\n \nStaff,\n \nSystem\n \nTrigger:\n \nThe\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \n'Create\n \nTraining\n \nRequest'\n \nsection\n \nfrom\n \ntheir\n \ndashboard.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nStaff\n \nuser\n \nto\n \nview\n \na\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npreviously\n \nsubmitted,\n \nalong\n \nwith\n \ntheir\n \ncurrent\n \nstatus\n \nand\n \nkey\n \ndetails.\n \nPreconditions:\n \n-\n \nThe\n \nStaff\n \nuser\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nThe\n \nStaff\n \nuser\n \nhas\n \nthe\n \nrole/permission\n \nto\n \nview\n \ntheir\n \nown\n \ntraining\n \nrequests.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nStaff\n \nuser\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nof\n \ntheir\n \nsubmitted\n \ntraining\n \nrequests,\n \nincluding\n \ndetails\n \nlike\n \nRequest\n \nID,\n \nTraining\n \nCategory,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \n \n-\n \n**Failure\n \n(No\n \nRequests):**\n \nIf\n \nthe\n \nuser\n \nhas\n \nnot\n \nsubmitted\n \nany\n \ntraining\n \nrequests,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequests\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n\"The\n \nStaff\n \nuser\n \nis\n \nsuccessfully\n \npresented\n \nwith\n \nan\n \naccurate\n \nand\n \nup-to-date\n \nlist\n \nof\n \nall\n \ntraining\n \nrequests\n \nthey\n \nhave\n \npersonally\n \nsubmitted,\n \ndisplaying\n \nkey\n \ndetails\n \nsuch\n \nas\n \nRequest\n \nID,\n \nTraining\n \nCategory ,\n \nSubmission\n \nDate,\n \nand\n \nCurrent\n \nStatus.\n \nIf\n \nno\n \nrequests\n \nhave\n \nbeen\n \nsubmitted,\n \nan\n \nappropriate\n \nmessage\n \nis\n \ndisplayed.\n \nNormal\n \nFlow:\n \n1.\n \nStaff\n \nuser\n \nlogs\n \ninto\n \nthe\n \nsystem\n \n(references\n \nUC_1:\n \nLogin\n \nSystem).\n \n \n2.\n \nStaff\n \nuser\n \nnavigates\n \nto\n \nthe\n \ndashboard."}`
