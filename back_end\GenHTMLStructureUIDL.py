import json
import os
import google.generativeai as genai
from . import prompt_storing  # Assuming this exists
from .rotate_api_key import APIKeyRotator
from google.api_core.exceptions import ResourceExhausted
import traceback

class GenHTMLStructure:
    def __init__(self, json_file, output_directory, config_file="back_end/gemini_config.json"):
        self.json_file = json_file
        self.output_directory = output_directory
        self.base_filename = os.path.splitext(os.path.basename(json_file))[0]

        self.config = None
        self.rotator = None
        self.api_key = None
        self.model_name = None
        self.response_mime_type = None
        self.model = None
        self.initialization_ok = False

        try:
            self.config = self.load_config(config_file)
            if self.config:
                self.rotator = APIKeyRotator(config_path=config_file)
                if not self.rotator.api_keys:
                    print("Error: APIKeyRotator failed to load any keys.")
                    return

                self.api_key = self.rotator.get_api_key()
                gemini_config = self.config.get("config_gemini_model", {})
                self.model_name = gemini_config.get("model_name")
                self.response_mime_type = gemini_config.get("response_mime_type")

                if not self.model_name or not self.response_mime_type:
                    print("Error: Missing 'model_name' or 'response_mime_type' in config_gemini_model.")
                    return

                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(
                    self.model_name,
                    generation_config={"response_mime_type": self.response_mime_type}
                )
                self.initialization_ok = True
                print(f"Initialization successful. Using API key ending with: ...{self.api_key[-4:]}")
            else:
                print("Failed to load configuration. Initialization aborted.")

        except Exception as e:
            print(f"An error occurred during initialization: {e}")
            print(traceback.format_exc())

    def load_config(self, file_path="gemini_config.json"):
        """Loads configuration from a JSON file."""
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Configuration file {file_path} not found.")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {file_path}: {e}")
        except Exception as e:
            print(f"An unexpected error occurred loading config {file_path}: {e}")
        return None

    def _rotate_and_reconfigure_api(self):
        """Rotates the API key, reconfigures genai, and re-initializes the model."""
        print(f"Quota exceeded with key ending ...{self.api_key[-4:]}. Rotating API key...")
        try:
            self.rotator.rotate_api_key()
            self.api_key = self.rotator.get_api_key()
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(
                self.model_name,
                generation_config={"response_mime_type": self.response_mime_type}
            )
            print(f"Switched to new API key ending: ...{self.api_key[-4:]}")
            return True
        except Exception as e:
            print(f"Error during API key rotation or reconfiguration: {e}")
            print(traceback.format_exc())
            return False

    def _attempt_generate_content(self, prompt: str) -> str:
        """Attempts to generate content using the current model, handling retries and key rotation."""
        if not self.initialization_ok or not self.rotator or not self.model:
            return "Error: HTML Structure Generator not properly initialized."

        max_attempts = len(self.rotator.api_keys)
        for attempt in range(max_attempts):
            try:
                print(f"Attempt {attempt + 1}/{max_attempts} using key ending ...{self.api_key[-4:]}")
                response = self.model.generate_content(prompt)
                if response and hasattr(response, "text") and response.text:
                    try:
                        json.loads(response.text)
                        return response.text
                    except json.JSONDecodeError as e:
                        print(f"Error: Response is not valid JSON: {e}")
                        print(f"Raw response: {response.text}")
                        return f"Error: Invalid JSON response - {e}"
                elif response and hasattr(response, "prompt_feedback"):
                    print(f"Warning: Received response with feedback: {response.prompt_feedback}")
                    if response.text:
                        try:
                            json.loads(response.text)
                            return response.text
                        except json.JSONDecodeError as e:
                            print(f"Error: Response with feedback is not valid JSON: {e}")
                            print(f"Raw response: {response.text}")
                            return f"Error: Invalid JSON response with feedback - {e}"
                    return f"Error: Generation failed due to API feedback - {response.prompt_feedback}"
                else:
                    print(f"Warning: Received unexpected response format or no text: {response}")
                    return "Error: No valid response text generated."

            except ResourceExhausted as e:
                print(f"ResourceExhausted error on attempt {attempt + 1}.")
                if attempt < max_attempts - 1:
                    if not self._rotate_and_reconfigure_api():
                        return f"Error: Failed to rotate API key - {e}"
                else:
                    print(f"Error: Exhausted all {max_attempts} API keys.")
                    return f"Error: All API keys exhausted - {e}"

            except Exception as e:
                print(f"Unexpected error during API call on attempt {attempt + 1}: {e}")
                print(traceback.format_exc())
                return f"Error: An unexpected exception occurred - {e}"

        return "Error: HTML Structure Generation failed after all attempts."

    def _extract_variables_from_uidl(self, uidl):
        """Extracts variables from the UIDL format and generates valid/invalid values."""
        variables = {}
        
        def traverse_layout(layout):
            if "children" not in layout:
                return

            for child in layout["children"]:
                # Extract variables from elements with a 'variable' field
                if "variable" in child:
                    var_name = child["variable"]
                    variables[var_name] = {
                        "valid": child.get("valid_values", []),
                        "invalid": child.get("invalid_values", [])
                    }
                    # If valid/invalid values are not provided, infer from type and interactions
                    if not variables[var_name]["valid"] and not variables[var_name]["invalid"]:
                        if child["type"] == "input":
                            if child.get("input_type") == "email":
                                variables[var_name]["valid"] = ["<EMAIL>"]
                                variables[var_name]["invalid"] = ["", "invalid-email"]
                            elif child.get("input_type") == "password":
                                variables[var_name]["valid"] = ["Password123"]
                                variables[var_name]["invalid"] = ["", "short"]
                            elif child.get("input_type") == "checkbox":
                                variables[var_name]["valid"] = ["checked", "unchecked"]
                                variables[var_name]["invalid"] = []
                            elif child.get("input_type") == "number":
                                variables[var_name]["valid"] = ["1", "5"]
                                variables[var_name]["invalid"] = ["0", "-1"]
                        elif child["type"] == "button":
                            variables[var_name]["valid"] = [child.get("text", var_name)]
                            variables[var_name]["invalid"] = []
                        elif child["type"] == "link":
                            variables[var_name]["valid"] = [child.get("text", var_name)]
                            variables[var_name]["invalid"] = []

                # Recursively traverse children
                traverse_layout(child)

        # Start traversal from the root layout
        traverse_layout(uidl["screen"]["layout"])

        # Enhance variables using interactions
        for interaction in uidl.get("interactions", []):
            element_id = interaction["element_id"]
            # Find the variable associated with this element_id
            for var_name, var_data in variables.items():
                # Search for the element_id in the layout to confirm the variable
                def find_element(layout, target_id):
                    if layout.get("id") == target_id and "variable" in layout:
                        return layout["variable"]
                    for child in layout.get("children", []):
                        result = find_element(child, target_id)
                        if result:
                            return result
                    return None

                if find_element(uidl["screen"]["layout"], element_id) == var_name:
                    # Use interaction description to enhance valid/invalid values
                    description = interaction["description"]
                    if "navigates to" in description.lower():
                        var_data["valid"] = var_data["valid"] or [f"{var_name} triggers navigation"]
                    if "error" in description.lower():
                        var_data["invalid"] = var_data["invalid"] or [f"{var_name} fails validation"]
                    if "conditions" in interaction:
                        for condition in interaction["conditions"]:
                            if condition.get("failure"):
                                var_data["invalid"] = var_data["invalid"] or [f"{var_name} fails {condition['condition']}"]

        return variables

    def generate_uidl(self):
        """Generates UIDL for each screen in the input JSON."""
        if not self.initialization_ok:
            print("Cannot generate UIDL: Initialization failed.")
            return []

        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                screen_data = json.load(f)
            
            screens = screen_data.get('screen_definitions', screen_data if isinstance(screen_data, list) else [])
            if not screens:
                print(f"Error: No screens found in JSON file '{self.json_file}'.")
                return []

        except Exception as e:
            print(f"Error reading input JSON file: {e}")
            print(traceback.format_exc())
            return []

        all_uidls = []
        print(f"Processing {len(screens)} screens from '{self.json_file}'")

        for i, screen in enumerate(screens):
            screen_name = screen.get('screen_name', f"Screen_{i+1}")
            print(f"\n--- Generating UIDL for Screen {i+1}: {screen_name} ---")

            # Prepare the prompt for this screen
            prompt = (
                "You are an expert UI developer tasked with generating a User Interface Description Language (UIDL) in JSON format for a web application screen based on a JSON specification. "
                "The JSON specification contains a 'screen_definitions' array with screens, each having a 'screen_name' and 'variables' (listing interactable elements like inputs, buttons, links). "
                "The JSON may also include 'navigation_rules' defining transitions between screens. "
                "For the given screen, generate a UIDL that describes the UI structure, layout, and interactions. "
                "The UIDL should have the following structure:\n"
                "- 'screen': An object with 'name', 'id', and 'layout' (a hierarchical structure of elements with types, IDs, and layout properties like flex, direction, justify).\n"
                "- Each element in the 'layout' should have a 'type' (e.g., container, input, button, link), 'id', and relevant properties (e.g., text, href, placeholder).\n"
                "- Elements that correspond to variables in the specification should include a 'variable' field matching the variable name, and include 'valid_values' and 'invalid_values' from the specification.\n"
                "- 'interactions': An array of interaction objects with 'element_id', 'event' (e.g., click, change), 'description' (what happens), 'target_screen' (if navigation occurs), 'target_url', and optional 'conditions' (e.g., form validation).\n"
                "Use semantic layout properties (e.g., type: 'flex', direction: 'row', justify: 'space-between') to describe relative positions.\n"
                "Include a consistent header with a logo ('CYO Shop'), navigation links (SHOP, COLLABS, CONTACT, ABOUT US), a search bar, a cart icon, and user menu (with Profile, Orders, Logout, and role-based dashboards).\n"
                "Include a consistent footer with category links (Clothing, Accessories), social links (Facebook, Google, Twitter), and a copyright notice ('© 2023 SASUCare E-Commerce Platform. All rights reserved.').\n"
                "\n\n"
                "Here is the JSON specification for the entire application:\n"
                f"{json.dumps(screen_data, indent=2)}\n\n"
                f"Generate the UIDL for the screen named '{screen_name}' (index {i} in 'screen_definitions'). "
                "Since the API is configured to return responses in JSON format (response_mime_type='application/json'), you must return a valid JSON object as a string. "
                "Ensure all strings are properly escaped for JSON (e.g., use \\n for newlines, escape double quotes with \\). "
                "Return only the JSON object as a string, without any additional text, comments, or markdown code blocks (e.g., no ```json)."
            )

            result_text = self._attempt_generate_content(prompt)
            all_uidls.append((screen_name, result_text))

        print("\n--- UIDL generation finished ---")
        return all_uidls

    def execute(self):
        """Executes the UIDL generation, extracts variables, and saves the result."""
        if not self.initialization_ok:
            print("Execution skipped: Initialization was not successful.")
            return []

        all_uidl_responses = self.generate_uidl()

        updated_screens = []
        successful_generations = 0
        failed_parsing = 0
        api_errors = 0

        for i, (screen_name, response_text) in enumerate(all_uidl_responses):
            if isinstance(response_text, str) and response_text.startswith("Error:"):
                print(f"API Error for screen '{screen_name}' (index {i}): {response_text}")
                updated_screens.append({"screen_name": screen_name, "error": response_text})
                api_errors += 1
            else:
                try:
                    cleaned_response_text = response_text.strip()
                    if cleaned_response_text.startswith("```json"):
                        cleaned_response_text = cleaned_response_text[7:]
                    if cleaned_response_text.endswith("```"):
                        cleaned_response_text = cleaned_response_text[:-3]
                    cleaned_response_text = cleaned_response_text.strip()

                    uidl = json.loads(cleaned_response_text)
                    if not isinstance(uidl, dict) or 'screen' not in uidl:
                        raise ValueError("Response JSON does not contain 'screen' key.")

                    # Extract variables from the UIDL
                    variables = self._extract_variables_from_uidl(uidl)
                    updated_screens.append({
                        "screen_name": screen_name,
                        "uidl": uidl,
                        "variables": variables
                    })
                    successful_generations += 1
                except json.JSONDecodeError as e:
                    print(f"Failed to parse JSON response for screen '{screen_name}' (index {i}): {e}")
                    print(f"Received text (cleaned, first 300 chars): {cleaned_response_text[:300]}...")
                    updated_screens.append({"screen_name": screen_name, "error": f"Invalid JSON response after cleaning: {e}", "raw_response": response_text})
                    failed_parsing += 1
                except Exception as e:
                    print(f"Unexpected error processing response for screen '{screen_name}' (index {i}): {e}")
                    updated_screens.append({"screen_name": screen_name, "error": f"Unexpected processing error: {e}", "raw_response": response_text})
                    failed_parsing += 1

        total_attempted = len(all_uidl_responses)
        print(f"\n--- Execution Summary ---")
        print(f"Total screens processed: {total_attempted}")
        print(f"Successfully generated and parsed: {successful_generations}")
        print(f"Failed JSON parsing: {failed_parsing}")
        print(f"API/Generation errors: {api_errors}")
        print(f"-------------------------")

        if updated_screens:
            # Convert to the format compatible with screen_definitions
            final_output = {
                "screen_definitions": [
                    {
                        "screen_name": screen["screen_name"],
                        "variables": screen.get("variables", {}),
                        "uidl": screen.get("uidl", screen.get("error", "Error in UIDL generation"))
                    }
                    for screen in updated_screens
                ]
            }
            self.save_updated_definitions(final_output, self.output_directory)
        else:
            print("No UIDLs were generated or parsed successfully.")

        return updated_screens

    def save_updated_definitions(self, definitions, directory):
        """Saves the updated screen definitions with UIDL and variables as a JSON file."""
        os.makedirs(directory, exist_ok=True)
        output_filename = os.path.join(directory, f'{self.base_filename}_updated_definitions.json')
        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(definitions, f, indent=4, ensure_ascii=False)
            print(f"Updated screen definitions saved to: {output_filename}")
        except Exception as e:
            print(f"Error saving updated screen definitions to {output_filename}: {e}")
            print(traceback.format_exc())

if __name__ == "__main__":
    # Define paths
    json_directory = "back_end/output"
    output_directory = "back_end/html_structures/output"
    config_path = "back_end/document/gemini_config.json"

    # Process each JSON file in the directory
    for filename in os.listdir(json_directory):
        if filename.lower().endswith(".json"):
            generator = GenHTMLStructure(
                json_file=os.path.join(json_directory, filename),
                output_directory=output_directory,
                config_file=config_path
            )
            generator.execute()