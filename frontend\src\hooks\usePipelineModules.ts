/**
 * Hook for managing pipeline modules
 * Fetches and manages pipeline module data from the backend
 */

import { useState, useEffect, useCallback } from 'react';

export interface PipelineModule {
    id: string;
    name: string;
    description: string;
    type: string;
    version: string;
    dependencies: string[];
    input_types: string[];
    output_types: string[];
    metadata: Record<string, any>;
}

export interface PipelineModulesState {
    modules: PipelineModule[];
    executionOrder: string[];
    totalCount: number;
    loading: boolean;
    error: string | null;
    lastUpdated: Date | null;
}

export interface UsePipelineModulesReturn extends PipelineModulesState {
    refetch: () => Promise<void>;
    getModule: (moduleId: string) => PipelineModule | undefined;
    isModuleAvailable: (moduleId: string) => boolean;
}

const INITIAL_STATE: PipelineModulesState = {
    modules: [],
    executionOrder: [],
    totalCount: 0,
    loading: true,
    error: null,
    lastUpdated: null,
};

// Fallback modules if API is not available
const FALLBACK_MODULES: PipelineModule[] = [
    {
        id: "document_processor",
        name: "Document Processor",
        description: "Processes PDF documents and extracts structured content",
        type: "processor",
        version: "1.0.0",
        dependencies: [],
        input_types: ["pdf_file"],
        output_types: ["structured_json"],
        metadata: { supports_auto_toc: true }
    },
    {
        id: "business_flow_detector",
        name: "Business Flow Detector",
        description: "Detects business flows from processed documents",
        type: "analyzer",
        version: "1.0.0",
        dependencies: ["document_processor"],
        input_types: ["structured_json"],
        output_types: ["business_flows"],
        metadata: {}
    },
    {
        id: "extract_and_process_to_json",
        name: "Extract and Process to JSON",
        description: "Extracts and processes data to JSON format",
        type: "processor",
        version: "1.0.0",
        dependencies: ["business_flow_detector"],
        input_types: ["business_flows"],
        output_types: ["processed_json"],
        metadata: {}
    },
    {
        id: "gen_html_structure_uidl",
        name: "Generate HTML Structure UIDL",
        description: "Generates HTML structure and UIDL",
        type: "generator",
        version: "1.0.0",
        dependencies: ["extract_and_process_to_json"],
        input_types: ["processed_json"],
        output_types: ["html_structure", "uidl"],
        metadata: {}
    },
    {
        id: "gen_test_case",
        name: "Generate Test Case",
        description: "Generates test cases from UIDL",
        type: "generator",
        version: "1.0.0",
        dependencies: ["gen_html_structure_uidl"],
        input_types: ["uidl"],
        output_types: ["test_cases"],
        metadata: {}
    },
    {
        id: "test_case_evaluator",
        name: "Test Case Evaluator",
        description: "Evaluates and validates test cases",
        type: "evaluator",
        version: "1.0.0",
        dependencies: ["gen_test_case"],
        input_types: ["test_cases"],
        output_types: ["evaluation_results"],
        metadata: {}
    }
];

export const usePipelineModules = (): UsePipelineModulesReturn => {
    const [state, setState] = useState<PipelineModulesState>(INITIAL_STATE);

    const fetchModules = useCallback(async () => {
        setState(prev => ({ ...prev, loading: true, error: null }));

        try {
            const response = await fetch('/api/v1/modules/list');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success && data.data) {
                setState({
                    modules: data.data.modules || [],
                    executionOrder: data.data.execution_order || [],
                    totalCount: data.data.total_count || 0,
                    loading: false,
                    error: null,
                    lastUpdated: new Date(),
                });
            } else {
                throw new Error(data.message || 'Failed to fetch modules');
            }
        } catch (error) {
            console.warn('Failed to fetch pipeline modules from API, using fallback:', error);
            
            // Use fallback modules
            setState({
                modules: FALLBACK_MODULES,
                executionOrder: FALLBACK_MODULES.map(m => m.id),
                totalCount: FALLBACK_MODULES.length,
                loading: false,
                error: null, // Don't show error for fallback
                lastUpdated: new Date(),
            });
        }
    }, []);

    const getModule = useCallback((moduleId: string): PipelineModule | undefined => {
        return state.modules.find(module => module.id === moduleId);
    }, [state.modules]);

    const isModuleAvailable = useCallback((moduleId: string): boolean => {
        return state.modules.some(module => module.id === moduleId);
    }, [state.modules]);

    // Initial fetch
    useEffect(() => {
        fetchModules();
    }, [fetchModules]);

    return {
        ...state,
        refetch: fetchModules,
        getModule,
        isModuleAvailable,
    };
};
