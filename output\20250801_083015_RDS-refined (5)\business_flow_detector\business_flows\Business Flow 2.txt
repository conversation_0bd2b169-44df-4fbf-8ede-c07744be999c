1. User navigates to LoginPage
2. User enters username and password in LoginPage
3. User clicks 'Login' button in LoginPage
Business Process Context: `{"heading_number": "2.1", "title": "2.1 Use Case: Login System", "Content": "in\n \nthe\n \ntargeted\n \nusers'\n \npermissions\n \nand\n \naccess\n \ncapabilities,\n \nand\n \na\n \ncomprehensive\n \naudit\n \nlog\n \nof\n \nthese\n \nadministrative\n \nactions\n \nis\n \nmaintained.\n \nNormal\n \nFlow\n \n1.\n \nCEO\n \nlogs\n \nin\n \n(UC002).\n \n2.\n \nNavigates\n \nto\n \ndashboard\n \nor\n \napproval\n \npage.\n \n3.\n \nViews\n \nlist\n \nof\n \npending\n \nrequests.\n \n4.\n \nSelects\n \nrequest\n \nto\n \nreview.\n \n5.\n \nReviews\n \ndetails.\n \n6a.\n \nApproves:\n \nvalidates\n \ncomments,\n \nupdates\n \nstatus,\n \nupdates\n \nrole,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n6b.\n \nRejects:\n \nrequires\n \ncomments,\n \nupdates\n \nstatus,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n7.\n \nRequest\n \nremoved\n \nfrom\n \npending\n \nlist.\n \n8.\n \nCEO\n \nreturns\n \nto\n \nlist\n \nor\n \ndashboard.\n \nAlternative\n \nFlows\n \n-\n \nAF1:\n \nNo\n \npending\n \nrequests:\n \nSystem\n \nshows\n \n\"No\n \nrole\n \nchange\n \nrequests\n \nawaiting\n \napproval.\"\n \n-\n \nAF2:\n \nRequest\n \nalready\n \nprocessed:\n \nSystem\n \ninforms\n \nCEO\n \nand\n \nrefreshes\n \nlist.\n \nExceptions\n \nE1:\n \nSystem\n \nError\n \nDuring\n \nUpdate:\n \nLogs\n \nerror,\n \nrollbacks\n \npartial\n \nchanges,\n \nshows\n \nerror\n \nmessage,\n \nno\n \nchanges\n \npersisted.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nLow\n \nto\n \nModerate\n \nBusiness\n \nRules\n \n-\n \nBR-RC002-1:\n \nCEO\n \nacts\n \non\n \nonly\n \npending\n \nrequests\n \nassigned/visible\n \nto\n \nthem.\n \n-\n \nBR-RC002-2:\n \nComments\n \nmandatory\n \nwhen\n \nrejecting.\n \n-\n \nBR-RC002-3:\n \nSystem\n \nmust\n \nupdate\n \nuser's\n \nrole\n \non\n \napproval.\n \n-\n \nBR-RC002-4:\n \nAll\n \nactions\n \nlogged\n \nwith\n \nactor,\n \ntimestamp,\n \ncomments.\n \n-\n \nBR-RC002-5:\n \nMulti-level\n \napproval\n \nworkflows\n \napply\n \n(CEO\n \nstep\n \nspecific).\n \nRelated\n \nUse\n \nCases\n \nUC002:\n \nUser\n \nLogin,\n \nUC004:\n \nView\n \nDashboard,\n \nUC-RC001:\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nRelated\n \nApprove\n \nRoleChangeRequestScreen\n \nAssumptions\n \nCEO\n \nunderstands\n \nrole\n \nchange\n \nimplications;\n \nnotification\n \nmechanisms\n \nexist.\n \n \n \n \n2.\n \nCommon\n \nFunctions\n \n2.1\n \nUse\n \nCase:\n \nLogin\n \nSystem\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nUse\n \nCase\n \nDiagram\n \n-\n \nUnregistered\n \nUser\",\n \n  \n\"actors\":\n \n[\n \n    \n{\"name\":\n \n\"Unregistered\n \nUser\"}\n \n  \n],\n \n  \n\"useCases\":\n \n[\n\n{\"id\":\n \n\"UC001\",\n \n\"name\":\n \n\"Register\n \nNew\n \nAccount\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]},\n \n    \n{\"id\":\n \n\"UC002\",\n \n\"name\":\n \n\"User\n \nLogin\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]}\n \n  \n],\n \n  \n\"relationships\":\n \n[\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC001\"},\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC002\"}\n \n  \n]\n \n}\n \n \na.\n \nFunctional\n \nDescription\n \nTable\n \n15Use\n \ncase\n \ndescription\n \nof\n \nUC_1:\n \nLogin.\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC_1\n \nUse\n \nCase\n \nName:\n \nLogin\n \nPrimary\n \nActor:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO\n \n \nSecondary\n \nActors:\n \nSystem\n \nNone\n \nTrigger:\n \nA\n \nuser\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO)\n \ninitiates\n \nthe\n \nlogin\n \nprocess\n \nby\n \nnavigating\n \nto\n \nthe\n \nlogin\n \npage\n \nand\n \nentering\n \ntheir\n \ncredentials..\n \nDescription:\n \nThis\n \nuse\n \ncase\n \ndescribes\n \nthe\n \nprocess\n \nof\n \na\n \nregistered\n \nuser\n \nlogging\n \ninto\n \nthe\n \nRetailOnboardPro\n \nsystem\n \nusing\n \nvalid\n \ncredentials.\n \nThe\n \nsystem\n \nverifies\n \nthe\n \ncredentials\n \nand\n \ngrants\n \naccess\n \nto\n \nthe\n \nappropriate\n \ndashboard\n \nbased\n \non\n \nthe\n \nuser's\n \nrole.\n \n.\n \nPreconditions:\n \n-\n \nThe\n \nuser\n \nmust\n \nbe\n \na\n \nregistered\n \nand\n \nactive\n \nsystem\n \nuser\n \nwith\n \na\n \ndefined\n \nrole.\n \n \n-\n \nThe\n \nsystem\n \nmust\n \nbe\n \nonline\n \nand\n \nable\n \nto\n \nprocess\n \nauthentication\n \nrequests.\n \n \n-\n \nThe\n \nuser\n \nmust\n \nhave\n \nvalid\n \nlogin\n \ncredentials\n \n(username/email\n \nand\n \npassword).\n \n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nuser\n \nis\n \nauthenticated,\n \ntheir\n \nsession\n \nis\n \ninitiated,\n \nand\n \nthey\n \nare\n \nredirected\n \nto\n \ntheir\n \nrespective\n \ndashboard\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO).\n \n \n-\n \n**Failure:**\n \nIf\n \nauthentication\n \nfails,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nfailed\n \nattempt,\n \nnotifies\n \nthe\n \nuser,\n \nand\n \nthe\n \nuser\n \nremains\n \non\n \nthe\n \nlogin\n \npage.\n \nAccess\n \nis\n \ndenied.\n \n \nExpected\n \nResults\n\nNormal\n \nFlow\n \n1.\n \nUser\n \nnavigates\n \nto\n \nthe\n \nLoginPage.\n \n \n2.\n \nUser\n \nenters\n \ntheir\n \nusername\n \n(or\n \nemail)\n \nand\n \npassword\n \nin\n \nthe\n \ndesignated\n \nfields.\n \n \n3.\n \nUser\n \nclicks\n \nthe\n \n'Login'\n \nbutton.\n \n \n4.\n \nSystem\n \nvalidates\n \nthe\n \nformat\n \nof\n \nthe\n \nentered\n \ncredentials\n \n(e.g.,\n \nnon-empty).\n \n \n5.\n \nSystem\n \nsecurely\n \nverifies\n \nthe\n \nsubmitted\n \ncredentials\n \nagainst\n \nthe\n \n`users`\n \ntable\n \nin\n \nthe\n \ndatabase.\n \n<br>\n \n6.\n \nIf\n \ncredentials\n \nare\n \nvalid:\n \n \n \na.\n \nSystem\n \nestablishes\n \nan\n \nauthenticated\n \nsession\n \nfor\n \nthe\n \nuser.\n \n \n \nb.\n \nSystem\n \nlogs\n \nthe\n \nsuccessful\n \nlogin\n \nattempt,\n \nincluding\n \nuser\n \nID\n \nand\n \ntimestamp.\n \n \n \nc.\n \nSystem\n \nredirects\n \nthe\n \nuser\n \nto\n \ntheir\n \nrole-specific\n \ndashboard.\n \n \nAlternative\n \nFlows:\n \n**AF1:\n \nInvalid\n \nCredentials**\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \ndo\n \nnot\n \nmatch\n \na\n \nregistered\n \nand\n \nactive\n \nuser:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nlogin\n \nattempt\n \n(e.g.,\n \nusername,\n \nIP\n \naddress,\n \ntimestamp).\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \non\n \nthe\n \nLoginPage\n \n(e.g.,\n \n\"Invalid\n \nusername\n \nor\n \npassword.\n \nPlease\n \ntry\n \nagain.\").\n \n \nc.\n \nUser\n \nremains\n \non\n \nthe\n \nLoginPage\n \n(returns\n \nto\n \nstep\n \n2).\n \n \n**AF2:\n \nAccount\n \nLocked\n \n(Optional\n \n-\n \nif\n \nimplemented)**\n \n-\n \nIf\n \nthe\n \nsystem\n \nimplements\n \naccount\n \nlocking\n \nafter\n \nmultiple\n \nfailed\n \nattempts:\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \nare\n \nvalid\n \nbut\n \nthe\n \naccount\n \nis\n \nlocked:\n \n \na.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Your\n \naccount\n \nis\n \ntemporarily\n \nlocked.\n \nPlease\n \ntry\n \nagain\n \nlater\n \nor\n \ncontact\n \nsupport.\").\n \n \n-\n \nIf\n \ncredentials\n \nare\n \ninvalid\n \nand\n \nthis\n \nattempt\n \ntriggers\n \nan\n \naccount\n \nlock:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nattempt\n \nand\n \nlocks\n \nthe\n \naccount.\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Invalid\n \ncredentials.\n \nYour\n \naccount\n \nhas\n \nbeen\n \ntemporarily\n \nlocked\n \ndue\n \nto\n \nmultiple\n \nfailed\n \nattempts.\").\n \n \nExceptions:\n \n \n**E1:\n \nSystem\n \nUnavailable**\n \n \n-\n \nIf\n \nthe\n \nauthentication\n \nservice\n \nor\n \ndatabase\n \nis\n \nunavailable\n \nat\n \nstep\n \n5:\n \n<br>\n        \na.\n \nSystem\n \nlogs\n \nthe\n \ncritical\n \nerror.\n \n \nb.\n \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Login\n \nservice\n \nis\n \ntemporarily\n \nunavailable.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \n \nMultiple\n \ntimes\n \nper\n \nday\n \nper\n \nuser.\n \n \nBusiness\n \nRules:\n \nBR-LOGIN-1,\n \nBR-LOGIN-2\n \n \nUse\n \ncase\n \nrelated\n \nUC-TR001,\n \nUC-SR002,\n \nUC-MR001,\n \nUC-MR002,\n \nUC-DR001,\n \nUC-DR002,\n \nUC-CR001,\n \nUC-CR002,\n \nUC_2\n \n(Manage\n \nUser\n \nProfile)\n \n \nScreen\n \nrelated\n \nLoginPage,\n \nStaffDashboardPage,\n \nManagerDashboardPage,\n \nDirectorDashboardPage,\n \nCEODashboardPage\n\nAssumptions:\n \n \n-\n \nPassword\n \nencryption\n \nand\n \nsecurity\n \npolicies\n \n(e.g.,\n \ncomplexity,\n \nexpiry)\n \nare\n \nenforced\n \nby\n \nthe\n \nsystem\n \nduring\n \nregistration\n \nand\n \nlogin.\n \n \n-\n \nSession\n \nmanagement\n \n(creation,\n \ntimeout,\n \ntermination)\n \nis\n \nhandled\n \nsecurely.\n \n \n \n \nb.\n \nBusiness"}`
