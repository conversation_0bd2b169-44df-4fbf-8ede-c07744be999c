1. Manager navigates to ManagerDashboardPage
2. Manager selects 'View Training Request History' option in ManagerDashboardPage
3. Manager applies filters or sorts history in Training Request History Screen
4. Manager selects a specific historical request in Training Request History Screen
5. Manager closes history view and returns to ManagerDashboardPage or previous screen
Business Process Context: `{"heading_number": "1.2", "title": "1.2.Manager", "Content": "1.2.1.\n \nUse\n \nCase:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \n \nDiagram:\n \n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n\n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Dashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nand\n \nmanage\n \nrequests\n \nassigned\n \nto\n \nthem.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nManagers\n \nto\n \nview\n \nhistory\n \nof\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"UpdateStatusPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nor\n \nmodal\n \nfor\n \nManagers\n \nto\n \nupdate\n \nthe\n \nstatus\n \nof\n \na\n \nrequest.\"\n \n}\n \n    \n//\n \nPotentially\n \nadd\n \nProfilePage\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \n(UC002:\n \nUser\n \nLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \n/\n \nManage\n \nrequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"RequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nrequest\n \nhistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"UpdateStatusPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \n'Change\n \nrequest\n \nstatus'\n \naction\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageRequestPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nrelevant\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \nflow\n \nrepresents\n \nthe\n \nprimary\n \nnavigation\n \npaths\n \nfor\n \na\n \nManager\n \nuser.\"\n \n}\n \n \n \nUse\n \ncase\n \ndescription\n \nof\n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC-MR001\n\nUse\n \nCase\n \nName:\n \nManager\n \nManages\n \nTraining\n \nRequest\n \nPrimary\n \nActor:\n \nManager\n \nSecondary\n \nActors:\n \nSystem,\n \nStaff\n \n(as\n \ninitiator),\n \nDirector\n \n(as\n \nnext\n \napprover)\n \n \nTrigger:\n \nThe\n \nManager\n \nsuccessfully\n \nreviews\n \na\n \npending\n \ntraining\n \nrequest\n \nand\n \ntakes\n \nappropriate\n \naction\n \n(Approve\n \nor\n \nReject).\n \nThe\n \nrequest's\n \nstatus\n \nis\n \nupdated\n \nin\n \nthe\n \nsystem\n \nto\n \nreflect\n \nthe\n \ndecision\n \n(\nPending_Director\n \nor\n \nRejected_Manager\n),\n \nrelevant\n \nnotifications\n \nare\n \npotentially\n \nsent,\n \nand\n \nan\n \naudit\n \ntrail\n \nof\n \nthe\n \nmanager's\n \naction\n \n(including\n \nany\n \ncomments)\n \nis\n \nrecorded.\n \nDescription:\n \nThis\n \nuse\n \ncase\n \nallows\n \na\n \nManager\n \nto\n \nview\n \nthe\n \nhistorical\n \nrecords\n \nof\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \ntheir\n \nscope\n \n(e.g.,\n \ntheir\n \nteam/department,\n \nor\n \nall\n \nrequests\n \nif\n \nthey\n \nhave\n \nsufficient\n \npermissions).\n \nThis\n \nincludes\n \npast\n \nstatuses,\n \nactions\n \ntaken,\n \ntimestamps,\n \nand\n \ncomments.\n \nPreconditions:\n \n-\n \nManager\n \nis\n \nauthenticated\n \nand\n \nhas\n \nan\n \nactive\n \nsession.\n \n(Screen:\n \nLogin)\n \n \n-\n \nManager\n \nhas\n \nthe\n \nnecessary\n \npermissions\n \nto\n \naccess\n \ntraining\n \nrequest\n \nhistorical\n \nrecords.\n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nManager\n \nis\n \npresented\n \nwith\n \na\n \nlist\n \nor\n \ndetailed\n \nview\n \nof\n \nhistorical\n \ntraining\n \nrequests,\n \nwith\n \noptions\n \nto\n \nfilter\n \nor\n \nsort.\n \n \n-\n \n**Failure\n \n(No\n \nHistory):**\n \nIf\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests\n \nare\n \nfound,\n \na\n \nmessage\n \nindicating\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \nfound\"\n \nis\n \ndisplayed.\n \nExpected\n \nResults\n \n \nNormal\n \nFlow:\n \n1.\n \nManager\n \nlogs\n \nin\n \nthe\n \nsystem.\n \n \n2.\n \nManager\n \nnavigates\n \nto\n \ntheir\n \ndashboard.\n \n<br>\n \n3.\n \nManager\n \nselects\n \nthe\n \noption\n \nto\n \n'View\n \nTraining\n \nRequest\n \nHistory'.\n \n \n4.\n \nSystem\n \nretrieves\n \nhistorical\n \ntraining\n \nrequest\n \ndata\n \nrelevant\n \nto\n \nthe\n \nManager's\n \nscope\n \n(e.g.,\n \nrequests\n \nsubmitted\n \nby\n \ntheir\n \nteam,\n \nrequests\n \nthey\n \nmanaged).\n \nThis\n \nincludes\n \nrequest\n \ndetails,\n \nstatus\n \nchange\n \nhistory,\n \napprover/rejecter\n \ndetails,\n \ntimestamps,\n \nand\n \ncomments.\n \n \n5.\n \nSystem\n \ndisplays\n \nthe\n \ntraining\n \nrequest\n \nhistory,\n \ntypically\n \nin\n \na\n \nlist\n \nformat,\n \nshowing\n \nkey\n \ndetails\n \nfor\n \neach\n \nhistorical\n \nentry\n \nor\n \nrequest.\n \n \n6.\n \nManager\n \ncan\n \napply\n \nfilters\n \n(e.g.,\n \nby\n \nStaff\n \nmember,\n \ndate\n \nrange,\n \nstatus,\n \ntraining\n \ncategory)\n \nor\n \nsort\n \nthe\n \nhistory.\n\n7.\n \nManager\n \ncan\n \nselect\n \na\n \nspecific\n \nhistorical\n \nrequest\n \nto\n \nview\n \nits\n \ncomplete\n \naudit\n \ntrail\n \nand\n \ndetails.\n \n \n8.\n \nManager\n \ncan\n \nclose\n \nthe\n \nhistory\n \nview\n \nand\n \nreturn\n \nto\n \nthe\n \ndashboard\n \nor\n \nprevious\n \nscreen.\n \nAlternative\n \nFlows:\n \n**AF1:\n \nNo\n \nHistorical\n \nData\n \nFound:**\n \n \n4a.\n \nIf\n \nthe\n \nsystem\n \nfinds\n \nno\n \nrelevant\n \nhistorical\n \ntraining\n \nrequests:\n \n \n4a.i.\n \nSystem\n \ndisplays\n \na\n \nmessage\n \nlike\n \n\"No\n \ntraining\n \nrequest\n \nhistory\n \navailable\n \nfor\n \nthe\n \nselected\n \ncriteria.\"\n \n \n**AF2:\n \nFiltering/Sorting\n \nApplied:**\n \n \n6a.\n \nManager\n \napplies\n \nfilters\n \nor\n \nsorting\n \ncriteria.\n \n \n6b.\n \nSystem\n \nre-queries\n \nand\n \ndisplays\n \nthe\n \nhistorical\n \ndata\n \naccording\n \nto\n \nthe\n \napplied\n \ncriteria.\n \nExceptions:\n \n**E1:\n \nSystem\n \nError:**\n \nIf\n \na\n \ndatabase\n \nerror\n \nor\n \nunexpected\n \nsystem\n \nissue\n \noccurs\n \nwhile\n \nretrieving\n \nhistory,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nerror\n \nand\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage.\n \n \n**E2:\n \nSession\n \nTimeout:**\n \nIf\n \nthe\n \nManager’s\n \nsession\n \nexpires,\n \nthey\n \nare\n \nprompted\n \nto\n \nlog\n \nin\n \nagain.\n \nPriority:\n \nMedium\n \nFrequency\n \nof\n \nUse:\n \nPeriodically,\n \nfor\n \nreview,\n \naudit,\n \nor\n \ntracking\n \npurposes.\n \nBusiness\n \nRules:\n \nBR-MR002-1,\n \nBR-MR002-2,\n \nBR-MR002-3,\n \nBR-MR002-4\n \nRelated\n \nUse\n \nCases:\n \n \nUC-MR001:\n \nManager\n \nManages\n \nTraining\n \nRequest,\n \nUC-TR001:\n \nStaff\n \nCreates\n \nTraining\n \nRequest,\n \nUC-SR002:\n \nStaff\n \nViews\n \nOwn\n \nTraining\n \nRequests\n \nScreen\n \nrelated:\n \nManagerDashboardPage,\n \nTraining\n \nRequest\n \nHistory\n \nScreen\n \n(Manager)\n \nAssumptions:\n \n \n-\n \nThe\n \nscope\n \nof\n \nhistorical\n \ndata\n \nvisible\n \nto\n \na\n \nManager\n \nis\n \ndefined\n \nby\n \ntheir\n \nrole\n \nand\n \npermissions.\n \n \n-\n \nHistorical\n \ndata\n \nis\n \naccurately\n \nlogged\n \nand\n \nmaintained\n \nby"}`
