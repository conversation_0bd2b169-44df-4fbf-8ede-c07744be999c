#!/usr/bin/env python3
"""
Phoenix Pipeline Server - Application Runner
Production-ready server startup script
"""

import sys
import os
from pathlib import Path

try:
    import uvicorn
except ImportError:
    print("❌ uvicorn not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "uvicorn"])
    import uvicorn

# Add app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

# Import settings AFTER adding path
from app.core.config import settings

def main():
    """Main startup function"""
    print("🚀 Starting Phoenix Pipeline Server...")
    print("=" * 60)
    print(f"📱 App: {settings.APP_NAME} v{settings.APP_VERSION}")
    print(f"🔧 Config loaded from: .env")
    print(f"�📡 Server: http://{settings.HOST}:{settings.PORT}")
    print(f"🔌 WebSocket: ws://{settings.HOST}:{settings.PORT}/ws")
    print(f"📊 Health: http://{settings.HOST}:{settings.PORT}/api/v1/health")
    print(f"📚 Docs: http://{settings.HOST}:{settings.PORT}/docs")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        # Start the server
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.RELOAD,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True,
            ws_ping_interval=settings.WS_PING_INTERVAL,
            ws_ping_timeout=settings.WS_PING_TIMEOUT,
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
