[{"Section_Heading": "1", "Section_Title": "1. Overall Description", "Keywords": ["Overall description", "Document overview"], "Summary": "This section serves as a top-level placeholder for the overall description of the technical document. No specific content is provided within this section."}, {"Section_Heading": "1.1", "Section_Title": "1.1 Functional Requirements Design", "Keywords": ["Functional requirements", "Requirements design"], "Summary": "This section introduces the functional requirements design of the system. It acts as a parent section to more detailed functional requirements outlined in subsequent subsections."}, {"Section_Heading": "1.1.1", "Section_Title": "1.1.1 User Management Requirements", "Keywords": ["User management", "User registration", "Authentication", "Role management", "Profile management", "Password reset", "Session management"], "Summary": "This section outlines core functional requirements for user management within the system. It covers user registration with email uniqueness and password strength rules, user authentication for access, administrator-level role and account management, and individual user profile updates and password resets. Additionally, it defines requirements for secure user session management."}, {"Section_Heading": "1.1.2", "Section_Title": "1.1.2 Product Management Requirements", "Keywords": ["Product management", "Product creation", "Product editing", "Product search", "Category management", "Image management", "Product approval", "Inventory management"], "Summary": "This section details the functional requirements for product management. It describes capabilities for sellers to create, edit, and manage products, including uploading images and managing inventory levels. The system also supports product search for customers and administrative functions like product approval and category organization."}, {"Section_Heading": "1.1.3", "Section_Title": "1.1.3 Shopping and Order Requirements", "Keywords": ["Shopping cart", "Checkout process", "Order management", "Order history", "Address management", "Order tracking", "Payment processing"], "Summary": "This section specifies functional requirements related to the shopping and order processes. It covers the shopping cart functionality, allowing customers to add and manage items, and the checkout process for completing purchases. Requirements include order management for sellers, order history and tracking for customers, address management, and secure payment processing."}, {"Section_Heading": "1.1.4", "Section_Title": "1.1.4 Booking System Requirements", "Keywords": ["Booking system", "Booking creation", "Booking management", "Booking status", "Booking history", "Booking calendar", "Booking notifications"], "Summary": "This section defines the functional requirements for the booking system. It includes requirements for customers to create and view their service bookings, as well as for sellers to manage their received bookings and update their status. Key aspects cover booking history, calendar display, and the generation of booking notifications."}, {"Section_Heading": "1.1.5", "Section_Title": "1.1.5 Content Management Requirements", "Keywords": ["Content management", "Company information", "Contact information", "Partnership information", "Site navigation", "Search functionality"], "Summary": "This section outlines the functional requirements for content management. It specifies the display and management of static content such as company information, contact details, and partnership information. Additionally, it covers requirements for consistent site navigation and a global search functionality for products and general content."}, {"Section_Heading": "1.1.6", "Section_Title": "1.1.6 Administrative Requirements", "Keywords": ["Administrative dashboard", "System monitoring", "Sale code management", "Business analytics", "System configuration", "Data export", "Audit trail"], "Summary": "This section details the functional requirements for system administration. It includes the provision of an administrative dashboard for platform oversight and system monitoring. Key administrative tasks covered are the creation and management of promotional sale codes, generation of business analytics and reports, system configuration, and data export functionalities. The section also emphasizes the need for an audit trail to track system changes and user activities."}, {"Section_Heading": "1.1.7", "Section_Title": "1.1.7 System and Security Requirements", "Keywords": ["System requirements", "Security requirements", "Access control", "Error handling", "Data security", "Session security", "Input validation", "System backup"], "Summary": "This section focuses on system-level and security functional requirements. It mandates robust access control based on roles, graceful error handling, and comprehensive data security measures including encryption. Requirements also cover secure user sessions, thorough input validation for data integrity and security, and reliable system backup procedures."}, {"Section_Heading": "1.2", "Section_Title": "1.2 User Roles and Permissions", "Keywords": ["User roles", "Permissions", "Access levels", "Anonymous user", "Customer", "<PERSON><PERSON>", "Administrator"], "Summary": "This section defines the distinct user roles within the system: Anonymous User, Customer, Seller, and Administrator. For each role, it specifies their primary functions by referencing relevant functional requirements (FR-IDs) and outlines their respective access levels. This structure clarifies who can perform which actions on the platform."}, {"Section_Heading": "1.3", "Section_Title": "1.3 Use Case Design", "Keywords": ["Use case design", "System use cases"], "Summary": "This section introduces the use case design for the system. It serves as a parent section that will contain detailed descriptions of individual use cases in its subsections."}, {"Section_Heading": "1.3.1", "Section_Title": "1.3.1 Register", "Keywords": ["User registration", "New account creation", "Email verification", "Password strength", "Terms acceptance", "UC-101"], "Summary": "This use case (UC-101) details the user registration process, allowing anonymous users to create new accounts (<PERSON>er or Seller). It outlines the steps for providing personal information, email verification, and system validation of inputs. Key business rules enforce email uniqueness, password strength requirements, email verification protocols, and mandatory acceptance of terms and conditions."}, {"Section_Heading": "1.3.2", "Section_Title": "1.3.2 <PERSON><PERSON>", "Keywords": ["User login", "Authentication", "User session", "Role-based redirection", "<PERSON>gin attempts", "Account verification", "UC-102"], "Summary": "This use case (UC-102) describes how registered users authenticate to access the SASUCare platform. It covers the process of entering credentials, system validation, session creation, and redirection to a role-specific dashboard. Business rules govern account verification, limit failed login attempts, define session timeouts, and dictate role-based navigation after successful authentication."}, {"Section_Heading": "1.3.3", "Section_Title": "1.3.3 Add Product to Cart", "Keywords": ["Add to cart", "Shopping cart", "Product availability", "Stock quantity", "Cart session persistence", "Price consistency", "UC-203"], "Summary": "This use case (UC-203) outlines the process for customers to add selected products to their shopping cart. The system validates product availability and stock quantity before updating the cart. Business rules ensure real-time stock checks, enforce maximum quantity limits per item, manage cart session persistence for both guests and registered users, and maintain price consistency."}, {"Section_Heading": "1.3.4", "Section_Title": "1.3.4 View Shopping Cart", "Keywords": ["View cart", "Shopping cart management", "Cart item update", "Price change notifications", "Cart expiration", "Guest cart", "UC-204"], "Summary": "This use case (UC-204) describes how customers view and manage the contents of their shopping cart. Users can see product details, quantities, and total costs, with options to modify quantities or remove items. Business rules manage cart session data, notify users of price changes, define cart expiration policies for both guests and registered users, and handle cart migration upon registration."}, {"Section_Heading": "1.3.5", "Section_Title": "1.3.5 Booking and Place Order", "Keywords": ["Place order", "Checkout process", "Shipping information", "Payment processing", "Inventory reservation", "Order confirmation", "UC-207"], "Summary": "This use case (UC-207) details the process for customers to finalize a purchase or booking. It involves providing shipping information, selecting a payment method, and confirming the order. Business rules govern address validation, temporary inventory reservation during checkout, payment processing validation (including <PERSON><PERSON> algorithm for credit cards), and the requirement for an order confirmation email to be sent promptly."}, {"Section_Heading": "1.3.6", "Section_Title": "1.3.6 View Order History", "Keywords": ["Order history", "Order status", "Shipment tracking", "Order details", "Privacy protection", "Access restrictions", "UC-208"], "Summary": "This use case (UC-208) enables customers to view their complete order history. Users can see current order statuses, track shipments, access detailed order information, and initiate reorders. Business rules restrict access to only a user's own orders, define how order statuses are displayed and updated, and ensure privacy protection for sensitive data within the order history."}, {"Section_Heading": "1.3.7", "Section_Title": "1.3.7 Manage Seller Profile/Shop Information", "Keywords": ["Seller profile", "Shop information", "Business details", "Image upload", "Shop name uniqueness", "Profile approval", "UC-301"], "Summary": "This use case (UC-301) allows sellers to manage and update their profile and shop information, including business name, description, contact details, and branding images. The system validates inputs and ensures changes are reflected across the platform. Business rules enforce shop name uniqueness, specify image file restrictions, require essential profile information, and define an administrative approval process for major profile changes."}, {"Section_Heading": "1.3.8", "Section_Title": "1.3.8 Manage Products", "Keywords": ["Product management", "Product creation", "Product editing", "Product deletion", "Inventory management", "Pricing rules", "UC-302"], "Summary": "This use case (UC-302) empowers sellers to perform comprehensive product management operations. Sellers can create new products, edit existing ones, upload images, set pricing, and manage inventory. Business rules govern product information completeness, image format and size restrictions, validate pricing rules (including discounts and admin approvals), and define inventory management requirements like low stock alerts and real-time updates."}, {"Section_Heading": "1.3.9", "Section_Title": "1.3.9 Manage Orders Received", "Keywords": ["Order management", "Seller orders", "Order status update", "Tracking information", "Customer notification", "Status transition validation", "UC-305"], "Summary": "This use case (UC-305) enables sellers to view and process customer orders containing their products. Sellers can update order statuses, add tracking information, and communicate with customers about order progress. Business rules enforce valid order status transitions, specify customer notification requirements (email, SMS), mandate tracking information for shipped orders, and restrict sellers to viewing only their own product orders."}, {"Section_Heading": "1.3.10", "Section_Title": "1.3.10 Manage Categories", "Keywords": ["Category management", "Product classification", "Category hierarchy", "Category deletion", "Product reassignment", "Admin role", "UC-404"], "Summary": "This use case (UC-404) allows administrators to create, edit, and organize the hierarchical structure of product categories on the platform. It ensures proper product classification and user navigation. Business rules enforce category name uniqueness, validate category hierarchy depth and prevent circular references, require products to be reassigned before category deletion, and define restrictions for deleting categories with active products or subcategories."}, {"Section_Heading": "1.3.11", "Section_Title": "1.3.11 Manage All Products", "Keywords": ["Admin product management", "Product approval", "Product rejection", "Product editing", "Product deletion", "Audit logging", "Seller notification", "UC-405"], "Summary": "This use case (UC-405) provides administrators with oversight and management capabilities for all products on the platform. Admins can approve, reject, edit, or remove products from any seller to maintain quality and compliance. Business rules define admin permission validation, mandate logging of all product actions for audit purposes, require seller notifications for any administrative changes to their products, and outline specific restrictions for product deletion, favoring soft deletes."}, {"Section_Heading": "1.3.12", "Section_Title": "1.3.12 View Booking History & Status", "Keywords": ["Booking history", "Booking status", "Service bookings", "Upcoming appointments", "Access restrictions", "Privacy protection", "UC-211"], "Summary": "This use case (UC-211) enables customers to view their comprehensive service booking history, including current and past bookings. Users can access real-time status updates and manage upcoming appointments. Business rules restrict access to a user's own bookings, specify requirements for displaying booking status (including real-time updates), and ensure privacy protection for personal and payment details within the booking history."}, {"Section_Heading": "1.3.13", "Section_Title": "1.3.13 Cancel Booking", "Keywords": ["Cancel booking", "Cancellation policy", "Refund processing", "Service provider notification", "Cancellation deadline", "UC-212"], "Summary": "This use case (UC-212) allows customers to cancel existing service bookings. The system processes cancellations according to predefined cancellation policies, automatically handling refunds and notifying the service provider. Business rules detail the cancellation policy (e.g., free cancellation period, refund percentages), enforce cancellation deadlines, specify immediate seller notification requirements, and outline the rules for refund processing, including timing and fees."}, {"Section_Heading": "2", "Section_Title": "2. External Interface Requirements", "Keywords": ["External interfaces", "Interface requirements"], "Summary": "This section serves as a high-level overview for external interface requirements. It acts as a parent section to detailed specifications regarding user interface design and other external interactions."}, {"Section_Heading": "2.1", "Section_Title": "2.1 User Interface Design", "Keywords": ["User interface design", "UI design", "Screen design"], "Summary": "This section introduces the user interface design specifications for the system. It outlines the overall approach to UI and precedes detailed descriptions of individual screen designs and common UI components."}, {"Section_Heading": "2.1.1", "Section_Title": "2.1.1 Screen Design Specifications", "Keywords": ["Screen design", "UI specifications", "User interface"], "Summary": "This section focuses on the detailed screen design specifications. It provides an overview of how individual screens are designed and accessed within the system, setting the stage for more granular descriptions in subsequent subsections."}, {"Section_Heading": "*******", "Section_Title": "******* Screen Access Summary", "Keywords": ["Screen access", "Access levels", "User roles", "Public access", "Customer access", "Seller access", "Admin access"], "Summary": "This section provides a summary of all key screens within the platform, detailing their purpose and associated access levels. Screens are categorized by the user roles that can access them, including Public, Authenticated, Customer, Seller, Admin, and System levels, outlining the permissions for each."}, {"Section_Heading": "*******", "Section_Title": "******* Screen Categories", "Keywords": ["Screen categorization", "UI categories", "Access categories"], "Summary": "This section introduces the categorization of screens based on their access levels. It serves as a high-level heading, preceding detailed descriptions of screens accessible by different user roles."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Public Access Screens", "Keywords": ["Public access screens", "Homepage", "Product detail page", "Search results", "About Us page", "Login page", "Registration pages"], "Summary": "This section lists and describes screens that are accessible to all users without requiring authentication. Key public screens include the Homepage, Product Detail Page, Search Results Page, About Us, Contact, and Collaborations pages. It also covers the Login and Customer/Seller Registration pages, facilitating initial user interaction with the platform."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Customer Access Screens", "Keywords": ["Customer access screens", "Shopping cart", "Customer bookings", "Booking details", "Create booking", "User orders"], "Summary": "This section details the screens that require customer-level authentication for access. These screens include the Shopping Cart Page for managing items before checkout, Customer Bookings Page for an overview of service bookings, and dedicated pages for viewing Booking Details and creating New Bookings. The User Orders Page for order history and tracking is also listed."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Seller Access Screens", "Keywords": ["Seller access screens", "Seller dashboard", "Product catalog management", "Order processing", "Service booking management", "Business monitoring"], "Summary": "This section outlines the screens accessible specifically to sellers, requiring seller-level authentication. Key screens include the Seller Dashboard for business performance monitoring, Seller Products Page for managing the product catalog, and Edit Product Page for detailed product and image management. It also covers Seller Orders and Bookings pages for processing customer transactions and managing service appointments."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 Admin Access Screens", "Keywords": ["Admin access screens", "Admin dashboard", "System-wide management", "Product approval", "Category management", "User account management", "Order oversight", "Promotional codes"], "Summary": "This section describes screens that are exclusive to administrators and require admin-level authentication. These include the Admin Dashboard for overall platform oversight and pages for system-wide management of products, pending product approvals, categories, user accounts, and orders. Additionally, administrators can manage promotional sale codes through dedicated screens."}, {"Section_Heading": "*******.5", "Section_Title": "*******.5 System Access Screens", "Keywords": ["System access screens", "Access denied page", "Error page", "Unauthorized access", "Error handling"], "Summary": "This section defines screens related to system-level access and error handling. It includes the Access Denied Page, which is displayed for unauthorized access attempts, and the Error Page, used to handle various system errors gracefully and provide feedback to users."}, {"Section_Heading": "2.1.2", "Section_Title": "2.1.2 Common UI Components", "Keywords": ["Common UI components", "User interface elements"], "Summary": "This section acts as an introductory placeholder for the description of common user interface components used across the platform. It precedes more detailed breakdowns of specific UI elements."}, {"Section_Heading": "*******", "Section_Title": "******* Header Component", "Keywords": ["Header component", "Site logo", "Main navigation", "Search bar", "User account menu", "Shopping cart icon", "UI elements"], "Summary": "This section describes the elements comprising the site's header component. It includes the clickable site logo, the primary navigation menu (SHOP, COLLABS, CONTACT, ABOUT US), a global search bar, a user account dropdown for authenticated users, and a shopping cart icon with an item count indicator, all providing public access."}, {"Section_Heading": "*******", "Section_Title": "******* Footer Component", "Keywords": ["Footer component", "Company information", "Quick links", "Social media links", "Newsletter signup", "Copyright notice", "UI elements"], "Summary": "This section describes the elements included in the site's footer component. It contains essential company information, quick links to important pages, social media links, a newsletter signup form for marketing communications, and the standard copyright notice with legal disclaimers. This component provides consistent navigation and information at the bottom of all pages."}, {"Section_Heading": "2.1.3.3", "Section_Title": "2.1.3.3 Form Components", "Keywords": ["Form components", "Text input fields", "Password fields", "Dropdown selects", "Checkboxes", "Radio buttons", "File upload", "Submit buttons", "UI elements"], "Summary": "This section details the various common form components utilized throughout the platform. It includes standard text input and secure password fields with validation, dropdown selects for categories or countries, and checkboxes for terms agreement or feature toggles. Radio buttons for single-choice selections, file upload interfaces for images, and primary/secondary submit buttons with loading states are also described."}, {"Section_Heading": "2.1.3.4", "Section_Title": "2.1.3.4 Notification Components", "Keywords": ["Notification components", "Success messages", "Error messages", "Warning messages", "Info messages", "Toast notifications", "Feedback UI"], "Summary": "This section describes the different notification components used to provide user feedback. It covers success messages (green styling) for confirming actions, error messages (red styling) for validation failures, and warning messages (yellow/orange styling) for important cautions. General information messages (blue styling) and temporary toast notifications for quick feedback are also defined, ensuring clear communication of system status."}, {"Section_Heading": "2.1.3.5", "Section_Title": "2.1.3.5 Data Display Components", "Keywords": ["Data display components", "Data tables", "Product cards", "Pagination", "Loading spinners", "Status badges", "Progress bars", "UI elements"], "Summary": "This section outlines the standard components used for displaying data across the user interface. These include data tables for structured information, product cards for consistent product presentation, and pagination for navigating large datasets. Visual indicators like loading spinners, color-coded status badges, and progress bars are also detailed for enhancing user experience during data operations."}, {"Section_Heading": "2.1.3", "Section_Title": "2.1.3 Detailed UI Elements by Screen", "Keywords": ["Detailed UI elements", "Screen-specific UI", "User interface breakdown"], "Summary": "This section introduces the detailed breakdown of UI elements, specifying components for each screen. It serves as a parent heading for subsequent sections that will outline the unique UI elements present on various pages across the platform."}, {"Section_Heading": "*******", "Section_Title": "******* Public Pages", "Keywords": ["Public pages", "UI elements", "Guest access", "Unauthenticated screens"], "Summary": "This section categorizes and describes the user interface elements found on pages accessible to all users without requiring authentication. It provides a general overview before delving into the specific elements of individual public pages."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Home Page Elements", "Keywords": ["Home page UI", "Hero banner", "Category filter", "Product grid", "Search functionality", "Site header", "Site footer"], "Summary": "This section details the key UI elements present on the Home Page. It includes the standard site header and footer, a prominent hero banner for promotions, and a category filter for browsing. The core display features a product grid with interactive product cards, complemented by a global search bar, enabling users to efficiently explore featured content and products."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Product Detail Page Elements", "Keywords": ["Product detail page UI", "Product images", "Product information", "Quantity selector", "Add to cart button", "Stock status", "Seller information"], "Summary": "This section outlines the UI elements found on the Product Detail Page. Key elements include the main product image with a thumbnail gallery, comprehensive product information (name, price, description, specifications), and seller details. An interactive quantity selector, an 'Add to Cart' button, and a stock status indicator are also present, facilitating customer purchasing decisions."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Search Results Page Elements", "Keywords": ["Search results page UI", "Search query display", "Filter options", "Results grid", "Sort controls", "No results message"], "Summary": "This section describes the UI elements specific to the Search Results Page. It features a display of the current search query and the number of results found, along with comprehensive filter options (category, price range) and sorting controls. The search results are presented in a grid layout with pagination, and a clear message is shown when no products match the criteria."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 About Us Page Elements", "Keywords": ["About Us page UI", "Page title", "Company information", "Team section", "Contact information"], "Summary": "This section details the UI elements for the About Us Page. It includes a main page title, text content describing the company's mission, vision, and values, and a section for team information or company history. Basic contact details and links are also provided, offering users insights into the organization."}, {"Section_Heading": "*******.5", "Section_Title": "*******.5 Contact Page Elements", "Keywords": ["Contact page UI", "Contact form", "Contact information", "Submit button", "Success/error messages", "Map integration"], "Summary": "This section outlines the UI elements of the Contact Page. It features a contact form for user inquiries, along with displayed company address, phone number, and email. A submit button handles form submissions, with success or error messages providing feedback. An embedded map showing the company's location is also included for user convenience."}, {"Section_Heading": "*******.6", "Section_Title": "*******.6 Collaborations Page Elements", "Keywords": ["Collaborations page UI", "Partnership information", "Partner showcase", "Contact for partnerships", "Page title"], "Summary": "This section describes the UI elements found on the Collaborations Page. It includes a dedicated page title, detailed information about collaboration opportunities and partnerships, and a visual showcase of current partners or examples. Information on how to initiate partnership discussions is also provided, facilitating potential collaborations."}, {"Section_Heading": "*******", "Section_Title": "******* Authentication Pages", "Keywords": ["Authentication pages", "<PERSON><PERSON>", "Registration", "Account security"], "Summary": "This section provides an overview of the user interface elements specific to authentication-related pages. It serves as a category for screens where users log in or register for new accounts."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Login Page Elements", "Keywords": ["Login page UI", "Login form", "Email input", "Password input", "Remember me", "Forgot password link", "Registration links", "Social login"], "Summary": "This section details the UI elements of the Login Page. It features a login form with email and password fields, a 'Remember Me' checkbox, and a 'Forgot Password' link. Links to customer and seller registration pages are provided, along with options for social login. Error messages offer feedback on failed login attempts."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Customer Registration Page Elements", "Keywords": ["Customer registration UI", "Personal information form", "Password fields", "Terms agreement", "Register button", "Password strength indicator", "Form validation"], "Summary": "This section describes the UI elements for the Customer Registration Page. It includes a form for personal information, secure password fields with a strength indicator, and a mandatory checkbox for agreeing to terms and privacy policy. A 'Register' button submits the form, supported by real-time form validation and a link back to the login page."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Seller Registration Page Elements", "Keywords": ["Seller registration UI", "Personal information", "Shop information", "Password fields", "Terms agreement", "Register button", "Form validation"], "Summary": "This section outlines the UI elements specific to the Seller Registration Page. It includes forms for both personal information and shop details, secure password fields with a strength indicator, and a checkbox for agreeing to seller terms and conditions. A primary 'Register' button submits the comprehensive form, with real-time validation and a link to the existing user login page."}, {"Section_Heading": "*******", "Section_Title": "******* Customer Pages", "Keywords": ["Customer pages", "Authenticated customer UI", "User-specific screens"], "Summary": "This section provides an overview of the user interface elements specific to pages accessible by authenticated customers. It acts as a category for screens tailored to a customer's personal interactions and data."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Shopping Cart Page Elements", "Keywords": ["Shopping cart UI", "Cart items list", "Quantity controls", "Remove items", "Seller grouping", "Subtotal display", "Checkout button", "Empty cart message"], "Summary": "This section describes the UI elements of the Shopping Cart Page. It displays a list of cart items with images, names, and prices, grouped by seller for multi-vendor support. Users can adjust quantities, remove items, and view subtotals. Buttons for 'Continue Shopping' and 'Checkout' are provided, along with a message for an empty cart."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Customer Bookings Page Elements", "Keywords": ["Customer bookings UI", "Bookings list", "Booking status indicators", "Booking details link", "Filter options", "Create new booking", "Search functionality"], "Summary": "This section outlines the UI elements for the Customer Bookings Page. It presents a list of the customer's service bookings, complete with visual status indicators (pending, confirmed, etc.) and links to detailed booking information. Filter options and a search bar facilitate navigation, alongside a button to initiate new service bookings."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Booking Details Page Elements", "Keywords": ["Booking details UI", "Booking information", "Status display", "Service provider info", "Customer information", "Special instructions", "Action buttons"], "Summary": "This section details the UI elements of the Booking Details Page. It provides comprehensive booking information, including service, date, time, and location, along with the current status. Information about the service provider and customer, plus any special instructions, are displayed. Action buttons for managing the booking (e.g., cancel) and a link to return to the bookings list are also present."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 Create Booking Page Elements", "Keywords": ["Create booking UI", "Service selection", "Address form", "Date/time picker", "Special instructions", "Booking summary", "Submit booking button"], "Summary": "This section describes the UI elements for the Create Booking Page. It features a service selection interface, an address form for the service location, and a date/time picker for appointment scheduling. Users can add special instructions and select from saved addresses. A booking summary provides an overview before the primary 'Submit Booking' button finalizes the creation."}, {"Section_Heading": "*******", "Section_Title": "******* Account Management Pages", "Keywords": ["Account management pages", "User profile", "Settings", "Password change"], "Summary": "This section provides an overview of the user interface elements specific to account management pages. It serves as a category for screens where authenticated users can manage their personal information and settings."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 User Profile Page Elements", "Keywords": ["User profile UI", "Profile picture", "Personal information", "Account statistics", "Account settings navigation"], "Summary": "This section details the UI elements of the User Profile Page. It displays the user's profile picture with an upload option, personal information (name, email, contact details), and a summary of account activity or membership duration. Navigation links to various account management options are also provided, enabling users to access settings easily."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Edit Profile Page Elements", "Keywords": ["Edit profile UI", "Profile form", "Image upload", "Save changes button", "Form validation", "Success messages", "Preview image"], "Summary": "This section outlines the UI elements for the Edit Profile Page. It features an editable form for personal and contact information, an interface for uploading and previewing profile images, and 'Save Changes' and 'Cancel' buttons. Real-time form validation and success messages provide essential user feedback during the profile update process."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Change Password Page Elements", "Keywords": ["Change password UI", "Current password", "New password", "Password strength indicator", "Update password button", "Form validation"], "Summary": "This section describes the UI elements for the Change Password Page. It includes secure input fields for the current, new, and confirmed new passwords, along with a real-time password strength indicator. A primary 'Change Password' button facilitates the update, supported by form validation to ensure secure password changes."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 User Orders Page Elements", "Keywords": ["User orders page UI", "Orders list", "Order status indicators", "Order details link", "Filter options", "Search functionality", "Pagination"], "Summary": "This section outlines the UI elements for the User Orders Page. It presents a list of the user's purchase orders with basic information and visual status indicators. Links to detailed order information, filter options (by status, date, seller), and a search bar are provided. Pagination supports navigation through large order lists, along with a brief order summary."}, {"Section_Heading": "*******", "Section_Title": "******* <PERSON><PERSON> Pages", "Keywords": ["Seller pages", "<PERSON>ller UI", "Business management screens"], "Summary": "This section provides an overview of the user interface elements specific to pages accessible by authenticated sellers. It serves as a category for screens where sellers manage their products, orders, and business operations."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Seller Dashboard Elements", "Keywords": ["Seller dashboard UI", "Dashboard overview", "Recent orders", "Product statistics", "Sales metrics", "Inventory alerts", "Quick actions", "Navigation menu"], "Summary": "This section describes the UI elements of the Seller Dashboard. It provides a comprehensive overview with summary cards displaying key metrics, recent customer orders, and product performance statistics. Sales metrics and inventory alerts keep sellers informed, while quick action buttons and a dedicated navigation menu facilitate access to core seller functions and shop information."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Seller Products Page Elements", "Keywords": ["Seller products page UI", "Products list", "Add product button", "Product actions", "Search/filter", "Product status", "Stock levels", "Bulk actions"], "Summary": "This section outlines the UI elements for the Seller Products Page. It displays a comprehensive list of the seller's products in a table or grid view, along with key information. A prominent 'Add Product' button, product-specific action buttons (edit, delete, status change), and search/filter tools are available. Visual indicators for product status and stock levels, plus bulk action options, facilitate efficient product catalog management."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Edit Product Page Elements", "Keywords": ["Edit product UI", "Product form", "Image upload", "Category selection", "Inventory management", "Pricing information", "Product status", "Save/update button"], "Summary": "This section describes the UI elements on the Edit Product Page. It features a comprehensive form for entering and updating product details, including name, description, and pricing information. Interfaces for image upload, category selection, and inventory management are provided. Sellers can also set the product's status, with 'Save/Update' and 'Cancel' buttons, and real-time form validation for feedback."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 Seller Orders Page Elements", "Keywords": ["Seller orders page UI", "Orders list", "Order status filter", "Order details link", "Status update actions", "Customer information", "Order value", "Export options"], "Summary": "This section outlines the UI elements for the Seller Orders Page. It presents a table displaying orders related to the seller's products, with an order status filter. Links to detailed order information and buttons for status updates are provided. Basic customer details, order value, search functionality, and options to export order data are also included, facilitating efficient order processing."}, {"Section_Heading": "*******.5", "Section_Title": "*******.5 Seller Order Details Page Elements", "Keywords": ["Seller order details UI", "Order summary", "Customer details", "Shipping address", "Order items", "Payment information", "Status timeline", "Action buttons"], "Summary": "This section describes the UI elements on the Seller Order Details Page. It provides a complete order summary, including order number, date, and current status. Detailed customer, shipping, and payment information are displayed, along with a list of ordered items. A visual status timeline tracks order progression, and action buttons allow the seller to manage the order, with a link to return to the orders list."}, {"Section_Heading": "*******.6", "Section_Title": "*******.6 Seller Bookings Page Elements", "Keywords": ["Seller bookings page UI", "Bookings list", "Booking status filter", "Booking details link", "Status update actions", "Customer information", "Service details", "Calendar view"], "Summary": "This section outlines the UI elements for the Seller Bookings Page. It displays a table of service bookings for the seller, complete with a status filter and links to detailed booking information. Buttons for status updates (confirm, complete, cancel), customer contact details, and service information are included. An optional calendar view allows for date-based booking management."}, {"Section_Heading": "*******.7", "Section_Title": "*******.7 Seller Booking Details Page Elements", "Keywords": ["Seller booking details UI", "Booking information", "Customer details", "Service location", "Special instructions", "Status management", "Communication tools"], "Summary": "This section describes the UI elements found on the Seller Booking Details Page. It provides comprehensive booking information, including service, date, time, and location, along with full customer details and service delivery address. Any special instructions are displayed, and action buttons are available for status management and communication with the customer. A link to return to the bookings list is also provided."}, {"Section_Heading": "*******", "Section_Title": "******* Admin Pages", "Keywords": ["Admin pages", "Administrative UI", "Platform management screens"], "Summary": "This section provides an overview of the user interface elements specific to pages accessible by administrators. It serves as a category for screens where administrators oversee and manage the entire platform."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Admin Dashboard Elements", "Keywords": ["Admin dashboard UI", "System overview", "Recent activity", "User statistics", "Sales analytics", "Product statistics", "Order management", "System alerts", "Quick actions"], "Summary": "This section describes the UI elements of the Admin Dashboard. It offers a high-level system overview with key metrics, recent activities, and visual statistics on users, sales, and products. Administrators can monitor order management, access quick action buttons for common tasks, and view important system alerts. A dedicated navigation menu provides access to all administrative functions."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 Admin Products Page Elements", "Keywords": ["Admin products page UI", "Products list", "Search and filter", "Product actions", "Bulk actions", "Product status", "Seller information", "Category management links", "Export options"], "Summary": "This section outlines the UI elements for the Admin Products Page. It displays a comprehensive table of all products in the system, with advanced search and filtering options. Administrators can perform actions like approve, reject, edit, or delete products, including bulk operations. Visual indicators show product approval status, and seller information is displayed, alongside links to category management and export tools."}, {"Section_Heading": "*******.3", "Section_Title": "*******.3 Admin Pending Products Page Elements", "Keywords": ["Admin pending products UI", "Pending products list", "Product preview", "Approval actions", "Rejection reasons", "Seller information", "Bulk approval", "Review comments"], "Summary": "This section describes the UI elements for the Admin Pending Products Page. It displays a list of products awaiting administrative approval, with a quick preview of product details and images. Administrators have action buttons to approve or reject products, with options to provide specific rejection reasons. Seller information is displayed, and functionalities for bulk approval and adding review comments are included."}, {"Section_Heading": "*******.4", "Section_Title": "*******.4 Admin Categories Page Elements", "Keywords": ["Admin categories page UI", "Categories list", "Add category form", "Category actions", "Category hierarchy", "Product count", "Category status", "Bulk management"], "Summary": "This section outlines the UI elements for the Admin Categories Page. It presents a table of all product categories, alongside a form to create new ones and action buttons for editing, deleting, or modifying existing categories. A visual display of the category hierarchy is included, showing product counts and active/inactive statuses for each. Options for bulk category operations are also provided."}, {"Section_Heading": "*******.5", "Section_Title": "*******.5 Admin Users Page Elements", "Keywords": ["Admin users page UI", "Users list", "User role filter", "User actions", "User details", "Search functionality", "Account status", "Role management", "Export options"], "Summary": "This section describes the UI elements for the Admin Users Page. It features a comprehensive table of all system users, with filtering options by role. Administrators can perform actions like activating, deactivating, or modifying user accounts, and view detailed user information including registration date and activity. Search functionality, visual account status indicators, role management options, and data export tools are also available."}, {"Section_Heading": "*******.6", "Section_Title": "*******.6 Admin Orders Page Elements", "Keywords": ["Admin orders page UI", "Orders list", "Order status filter", "Order details link", "Status management", "Customer information", "Seller information", "Order value", "Search and filter", "Analytics tools"], "Summary": "This section outlines the UI elements for the Admin Orders Page. It presents a comprehensive table of all system orders, with filtering options by status. Links to detailed order information are provided, along with administrative controls for status updates. Information about customer and seller, order value, advanced search options, and links to order analytics and reporting tools are also included."}, {"Section_Heading": "*******.7", "Section_Title": "*******.7 Admin Order Details Page Elements", "Keywords": ["Admin order details UI", "Order summary", "Customer information", "Seller information", "Order items", "Payment details", "Shipping information", "Order timeline", "Admin actions"], "Summary": "This section describes the UI elements found on the Admin Order Details Page. It provides a complete order summary, detailed customer and seller profiles, a breakdown of ordered items, payment details, and shipping information. A visual order timeline tracks status progression, and various administrative controls allow for comprehensive management of the order."}, {"Section_Heading": "*******.8", "Section_Title": "*******.8 Admin Sale Codes Page Elements", "Keywords": ["Admin sale codes UI", "Promotional codes", "Discount management", "Code creation", "Code management actions", "Usage statistics", "Expiration date", "Bulk management"], "Summary": "This section outlines the UI elements for the Admin Sale Codes Page. It displays a table of all promotional codes and discounts, along with a form for creating new ones. Administrators have action buttons to edit, activate, deactivate, or delete codes. Information on usage statistics, code details (discount, expiration, limits), search/filter options, and bulk management capabilities are also provided."}, {"Section_Heading": "*******", "Section_Title": "******* Error and System Pages", "Keywords": ["Error pages", "System pages", "Access control", "Error handling"], "Summary": "This section introduces a category for error and system-level pages. It serves as a parent heading for descriptions of specific UI elements found on pages dedicated to handling unauthorized access or general system errors."}, {"Section_Heading": "*******.1", "Section_Title": "*******.1 Access Denied Page Elements", "Keywords": ["Access denied page UI", "Error message", "Error code", "Navigation options", "Login suggestion", "Contact support"], "Summary": "This section describes the UI elements of the Access Denied Page. It displays a clear error message explaining the access restriction, along with the HTTP 403 error code. Navigation options are provided to help users return to accessible areas, and suggestions for logging in with appropriate credentials or contacting support for access issues are included."}, {"Section_Heading": "*******.2", "Section_Title": "*******.2 <PERSON><PERSON><PERSON> Page Elements", "Keywords": ["Error page UI", "User-friendly error message", "HTTP error code", "Suggested actions", "Home page link", "Search functionality", "Contact support"], "Summary": "This section outlines the UI elements for the general Error Page. It presents a user-friendly error message explaining the issue, along with the relevant HTTP error code. Suggested actions are provided to help users resolve the error, and links to the home page or contact support are available. A search bar is also included to assist users in finding what they were looking for before the error occurred."}, {"Section_Heading": "3", "Section_Title": "3. <PERSON>ppendi<PERSON>", "Keywords": ["Appendices", "Document version", "Business flow", "Customer-seller interaction", "Use case lifecycle", "SASUCare platform"], "Summary": "This section serves as an appendix, providing meta-information about the document, including its version and last update. Crucially, it describes the comprehensive business flow between a Customer and a Seller on the SASUCare platform. This narrative details the continuous lifecycle from seller setup and product listing, through customer registration, product purchase, order fulfillment, and service booking, highlighting the interconnectedness of various use cases."}]