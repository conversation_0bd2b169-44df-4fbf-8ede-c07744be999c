{"graph": {"nodes": [{"id": "homepage", "name": "Homepage", "type": "screen"}, {"id": "loginpage", "name": "LoginPage", "type": "screen"}, {"id": "registrationpage", "name": "RegistrationPage", "type": "screen"}, {"id": "staffdashboardpage", "name": "StaffDashboardPage", "type": "screen"}, {"id": "managerdashboardpage", "name": "ManagerDashboardPage", "type": "screen"}, {"id": "directordashboardpage", "name": "DirectorDashboardPage", "type": "screen"}, {"id": "ceodashboardpage", "name": "CEODashboardPage", "type": "screen"}, {"id": "requestlistscreen_training", "name": "RequestListScreen_Training", "type": "screen"}, {"id": "requestdetailscreen_training", "name": "RequestDetailScreen_Training", "type": "screen"}, {"id": "createtrainingrequestscreen", "name": "CreateTrainingRequestScreen", "type": "screen"}, {"id": "rolechangerequestscreen_staffview", "name": "RoleChangeRequestScreen_StaffView", "type": "screen"}, {"id": "approverolechangerequestscreen", "name": "ApproveRoleChangeRequestScreen", "type": "screen"}, {"id": "profilescreen", "name": "ProfileScreen", "type": "screen"}, {"id": "rolechangerequestdetailsmodal", "name": "RoleChangeRequestDetailsModal", "type": "screen"}, {"id": "managetrainingrequestslistpage", "name": "ManageTrainingRequestsListPage", "type": "screen"}, {"id": "trainingrequesthistorypage", "name": "TrainingRequestHistoryPage", "type": "screen"}], "edges": [{"source": "homepage", "target": "loginpage", "action": "Clicks 'Login' link/button", "element": "loginButton"}, {"source": "homepage", "target": "registrationpage", "action": "Clicks 'Register' link/button", "element": "registerButton"}, {"source": "loginpage", "target": "registrationpage", "action": "Clicks 'Register here' link", "element": "registerLink"}, {"source": "registrationpage", "target": "loginpage", "action": "Successful registration submission", "element": "submitRegistration"}, {"source": "registrationpage", "target": "loginpage", "action": "Clicks 'Already have an account? Login' link", "element": "loginLink"}, {"source": "loginpage", "target": "staffdashboardpage", "action": "Successful login as STAFF", "element": "submitLogin"}, {"source": "loginpage", "target": "managerdashboardpage", "action": "Successful login as MANAGER", "element": "submitLogin"}, {"source": "loginpage", "target": "directordashboardpage", "action": "Successful login as DIRECTOR", "element": "submitLogin"}, {"source": "loginpage", "target": "ceodashboardpage", "action": "Successful login as CEO", "element": "submitLogin"}, {"source": "staffdashboardpage", "target": "requestlistscreen_training", "action": "Clicks 'View My Requests'", "element": "trainingRequestsLink"}, {"source": "staffdashboardpage", "target": "createtrainingrequestscreen", "action": "Clicks 'Create New Request'", "element": "createTrainingRequestLink"}, {"source": "staffdashboardpage", "target": "rolechangerequestscreen_staffview", "action": "Clicks 'Create Role Change Request'", "element": "roleChangeRequestsLink"}, {"source": "staffdashboardpage", "target": "profilescreen", "action": "Clicks 'Profile' link", "element": "profileLink"}, {"source": "staffdashboardpage", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "requestlistscreen_training", "target": "requestdetailscreen_training", "action": "Clicks 'View Details' on a training request", "element": "viewDetailAction"}, {"source": "requestlistscreen_training", "target": "staffdashboardpage", "action": "Navigates back or via sidebar", "element": "dashboardLink"}, {"source": "requestlistscreen_training", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "createtrainingrequestscreen", "target": "requestlistscreen_training", "action": "Successful submission of training request", "element": "submitTrainingRequest"}, {"source": "createtrainingrequestscreen", "target": "staffdashboardpage", "action": "Clicks 'Cancel' or 'Back'", "element": "cancelCreateRequest"}, {"source": "createtrainingrequestscreen", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "requestdetailscreen_training", "target": "requestlistscreen_training", "action": "Clicks 'Back to List'", "element": "backButton"}, {"source": "profilescreen", "target": "staffdashboardpage", "action": "Navigates back or via sidebar", "element": "dashboardLink"}, {"source": "profilescreen", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "managerdashboardpage", "target": "managetrainingrequestslistpage", "action": "Clicks 'Manage Training Requests'", "element": "trainingRequestsLink"}, {"source": "managerdashboardpage", "target": "trainingrequesthistorypage", "action": "Clicks 'View Training Request History'", "element": null}, {"source": "managerdashboardpage", "target": "profilescreen", "action": "Clicks 'Profile' link", "element": "profileLink"}, {"source": "managerdashboardpage", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "managetrainingrequestslistpage", "target": "requestdetailscreen_training", "action": "Selects a request to review/action", "element": "viewDetailAction"}, {"source": "managetrainingrequestslistpage", "target": "managerdashboardpage", "action": "Navigates back or via sidebar", "element": "dashboardLink"}, {"source": "requestdetailscreen_training", "target": "managetrainingrequestslistpage", "action": "After action (Approve/Reject) or Clicks 'Back to List'", "element": "backButton"}, {"source": "trainingrequesthistorypage", "target": "managerdashboardpage", "action": "Navigates back or via sidebar", "element": "dashboardLink"}, {"source": "directordashboardpage", "target": "requestlistscreen_training", "action": "Clicks 'Manage Training Requests' link", "element": "trainingRequestsLink"}, {"source": "directordashboardpage", "target": "profilescreen", "action": "Clicks 'Profile' link", "element": "profileLink"}, {"source": "directordashboardpage", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "requestlistscreen_training", "target": "directordashboardpage", "action": "Clicks 'Back to Dashboard' link", "element": "dashboardLink"}, {"source": "requestdetailscreen_training", "target": "requestlistscreen_training", "action": "After action (Approve/Reject) or Clicks 'Back to List'", "element": "backButton"}, {"source": "profilescreen", "target": "directordashboardpage", "action": "Clicks 'Back to Dashboard' link", "element": "dashboardLink"}, {"source": "ceodashboardpage", "target": "requestlistscreen_training", "action": "Clicks 'Manage Training Requests' link", "element": "trainingRequestsLink"}, {"source": "ceodashboardpage", "target": "approverolechangerequestscreen", "action": "Clicks 'Approve Role Change Requests' link", "element": "approveRoleChangesLink"}, {"source": "ceodashboardpage", "target": "profilescreen", "action": "Clicks 'Profile' link", "element": "profileLink"}, {"source": "ceodashboardpage", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "requestlistscreen_training", "target": "ceodashboardpage", "action": "Clicks 'Back to Dashboard' link", "element": "dashboardLink"}, {"source": "approverolechangerequestscreen", "target": "rolechangerequestdetailsmodal", "action": "Clicks 'Process' on a role change request", "element": "processRequestButton"}, {"source": "approverolechangerequestscreen", "target": "ceodashboardpage", "action": "Clicks 'Back to Dashboard' link", "element": "dashboardLink"}, {"source": "approverolechangerequestscreen", "target": "loginpage", "action": "Clicks 'Logout'", "element": "logoutLink"}, {"source": "rolechangerequestdetailsmodal", "target": "approverolechangerequestscreen", "action": "Submits decision or Cancels modal", "element": "submitReviewDecision"}, {"source": "profilescreen", "target": "ceodashboardpage", "action": "Clicks 'Back to Dashboard' link", "element": "dashboardLink"}]}}