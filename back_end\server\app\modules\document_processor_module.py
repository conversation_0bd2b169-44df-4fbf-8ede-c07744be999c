"""
Document Processor Pipeline Module
Wrapper for the existing document_processor functionality
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path

from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

# Add back_end directory to path for importing document_processor
# Current file: back_end/server/app/modules/document_processor_module.py
# Go up to back_end: modules -> app -> server -> back_end
back_end_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(back_end_path))

logger = logging.getLogger(__name__)

class DocumentProcessorModule(PipelineModule):
    """Pipeline module wrapper for document processor"""
    
    def __init__(self):
        config = PipelineModuleConfig(
            id="document_processor",
            name="Document Processor",
            description="Processes PDF documents and extracts structured content using AI",
            module_type=PipelineModuleType.PROCESSOR,
            version="1.0.0",
            dependencies=[],
            input_types=["pdf_file"],
            output_types=["structured_json", "analyzed_json"],
            metadata={
                "supports_auto_toc": True,
                "max_file_size": "10MB",
                "supported_formats": ["PDF"],
                "ai_powered": True
            }
        )
        super().__init__(config)
        
        # Initialize paths
        self.uploads_dir = Path("uploads")
        self.output_base_dir = Path("output")

        # Set up config file path - use absolute path
        # From: back_end/server/app/modules/document_processor_module.py
        # To:   back_end/document/gemini_config_flash.json
        back_end_path = Path(__file__).parent.parent.parent.parent  # Go up 4 levels to back_end
        self.config_file = back_end_path / "document" / "gemini_config_pro.json"
        logger.info(f"Config file path: {self.config_file}")
        logger.info(f"Config file absolute path: {self.config_file.absolute()}")
        logger.info(f"Config file exists: {self.config_file.exists()}")
        
        # Ensure directories exist
        self.uploads_dir.mkdir(exist_ok=True)
        self.output_base_dir.mkdir(exist_ok=True)
    
    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data for document processor"""
        try:
            # Check required fields
            if "document_id" not in input_data:
                logger.error("Missing required field: document_id")
                return False

            if "file_path" not in input_data:
                logger.error("Missing required field: file_path")
                return False

            # Check if file exists
            file_path = Path(input_data["file_path"])
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return False

            # Check file type
            if not file_path.suffix.lower() == ".pdf":
                logger.error(f"Unsupported file type: {file_path.suffix}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Input validation error: {e}")
            return False
    
    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute document processing"""
        try:
            logger.info("DocumentProcessor.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing file: {file_path}")
            logger.info(f"Document ID: {document_id}")
            
            # Create output directory for this document
            doc_output_dir = self.output_base_dir / document_id / "document_processor"
            doc_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Define output file paths
            merged_json = doc_output_dir / "merged_output.json"
            analyzed_json = doc_output_dir / "analyzed_output.json"
            
            # Import and initialize document processor
            try:
                logger.info("Importing DocumentProcessor...")

                # Add back_end directory to sys.path to handle imports
                import sys
                import importlib.util

                back_end_dir = str(back_end_path)
                if back_end_dir not in sys.path:
                    sys.path.insert(0, back_end_dir)
                    logger.info(f"Added to sys.path: {back_end_dir}")

                # Change working directory temporarily to back_end for relative imports
                import os
                original_cwd = os.getcwd()
                os.chdir(back_end_dir)
                logger.info(f"Changed working directory to: {back_end_dir}")

                try:
                    # Import DocumentProcessor from the actual file
                    from document_processor import DocumentProcessor
                    logger.info("DocumentProcessor imported successfully")
                finally:
                    # Restore original working directory
                    os.chdir(original_cwd)
                    logger.info(f"Restored working directory to: {original_cwd}")

                logger.info("Initializing DocumentProcessor...")
                processor = DocumentProcessor(
                    pdf_path=str(file_path),
                    config_file=str(self.config_file) if self.config_file.exists() else None,
                    output_dir=str(doc_output_dir),
                    merged_json=str(merged_json),
                    analyzed_json=str(analyzed_json),
                    use_auto_toc=True,
                    count_token=False,
                    enable_caching=True
                )
                logger.info("DocumentProcessor initialized")

                # Execute processing in thread pool to avoid blocking
                logger.info("Starting document processing in thread pool...")
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    processor.execute,
                    True,  # verbose
                    False  # skip_llm
                )
                logger.info("Document processing completed")
                
                # Check if output files were created
                outputs = {}
                merged_exists = merged_json.exists()
                analyzed_exists = analyzed_json.exists()

                if merged_exists:
                    outputs["merged_json"] = str(merged_json)
                    logger.info(f"Created merged JSON: {merged_json}")
                else:
                    logger.error(f"Missing merged JSON: {merged_json}")

                if analyzed_exists:
                    outputs["analyzed_json"] = str(analyzed_json)
                    logger.info(f"Created analyzed JSON: {analyzed_json}")
                else:
                    logger.warning(f"Missing analyzed JSON: {analyzed_json}")

                # Get processing summary from actual processor
                summary = {
                    "sections_extracted": len(processor.extracted_sections) if hasattr(processor, 'extracted_sections') else 0,
                    "toc_items": len(processor.manual_sections) if hasattr(processor, 'manual_sections') else 0,
                    "processing_mode": "auto_toc" if processor.use_auto_toc else "manual_sections"
                }

                # Determine success based on expected outputs
                # For now, require both merged_json and analyzed_json
                success = merged_exists and analyzed_exists

                if success:
                    message = f"Successfully processed document with {summary['sections_extracted']} sections"
                    logger.info(f"Document processing fully completed: {document_id}")
                else:
                    missing_files = []
                    if not merged_exists:
                        missing_files.append("merged_json")
                    if not analyzed_exists:
                        missing_files.append("analyzed_json")
                    message = f"Processing incomplete - missing files: {', '.join(missing_files)}"
                    logger.error(f"Document processing incomplete: {message}")

                result = {
                    "success": success,
                    "document_id": document_id,
                    "input_file": str(file_path),
                    "output_directory": str(doc_output_dir),
                    "output_files": outputs,
                    "summary": summary,
                    "message": message
                }
                
                logger.info(f"Document processing completed successfully: {document_id}")
                return result

            except ImportError as e:
                logger.error(f"Failed to import DocumentProcessor: {e}")
                return {
                    "success": False,
                    "error": f"DocumentProcessor not available: {e}",
                    "document_id": document_id
                }

        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": input_data.get("document_id", "unknown")
            }
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current module status"""
        base_status = await super().get_status()
        
        # Add module-specific status
        base_status.update({
            "uploads_dir": str(self.uploads_dir),
            "output_dir": str(self.output_base_dir),
            "config_available": self.config_file.exists() if self.config_file else False,
            "directories_ready": all([
                self.uploads_dir.exists(),
                self.output_base_dir.exists()
            ])
        })
        
        return base_status

# Factory function to create and register the module
def create_document_processor_module() -> DocumentProcessorModule:
    """Create and return document processor module"""
    return DocumentProcessorModule()
