{"benchmarkTitle": "Comprehensive Test Suite Coverage Checklist - E-Commerce System (Use Case Oriented)", "benchmarkVersion": "1.1", "purpose": "This checklist evaluates test coverage for the E-Commerce application based on specific Use Cases. It maps existing test criteria from the original feature-based structure to their corresponding Use Cases without modification.", "instructionsForAI": "For the given Use Case, determine if test cases exist that cover each specific criterion below. A single test case might cover multiple criteria. Update the 'compliance' and 'comments' fields accordingly.", "evaluationSections": [{"sectionId": "UC-101_USER_REGISTRATION", "title": "Use Case: UC-101 - User Registration", "criteria": [{"id": "COV-REG-BR-01", "description": "Mandatory fields (<PERSON><PERSON> tên, <PERSON><PERSON>, <PERSON><PERSON><PERSON> đ<PERSON>, <PERSON><PERSON><PERSON> khẩu, <PERSON><PERSON><PERSON> nhận mật khẩu) are required (BR-REG-01)", "compliance": true, "comments": "Covered by TC-REG-MANDATORY-001"}, {"id": "COV-REG-BR-02", "description": "User must agree to terms and policy (BR-REG-02)", "compliance": true, "comments": "Covered by TC-REG-MANDATORY-001"}, {"id": "COV-REG-BR-03", "description": "Password must be at least 8 characters (BR-REG-03)", "compliance": true, "comments": "Covered by TC-REG-PASSWORD-LEN-001, TC-REG-PASSWORD-LEN-002"}, {"id": "COV-REG-BR-04", "description": "Confirmation password must match the password (BR-REG-04)", "compliance": true, "comments": "Covered by TC-REG-PASSWORD-CONFIRM-001, TC-REG-PASSWORD-LEN-002"}, {"id": "COV-REG-BR-05", "description": "Email must be unique in the system (BR-REG-05)", "compliance": true, "comments": "Covered by TC-REG-EMAIL-UNIQUE-001"}, {"id": "COV-REG-BR-06", "description": "Username must be unique in the system (BR-REG-06)", "compliance": true, "comments": "Covered by TC-REG-USERNAME-UNIQUE-001"}, {"id": "COV-REG-BR-07", "description": "Email must have a valid format (e.g., a@b.c) (BR-REG-07)", "compliance": true, "comments": "Covered by TC-REG-EMAIL-FORMAT-001"}, {"id": "COV-REG-BR-08", "description": "New account is created with ROLE_USER (BR-REG-08)", "compliance": true, "comments": "Covered by TC-REG-SUCCESS-001, TC-REG-PASSWORD-LEN-002"}, {"id": "COV-REG-BR-09", "description": "Fields do not exceed 255 characters (BR-REG-09)", "compliance": true, "comments": "Covered by TC-REG-MAXLEN-NAME-001"}, {"id": "COV-REG-ST-SUCCESS", "description": "Successful registration redirects to LoginPage (ST-REG-SUCCESS)", "compliance": true, "comments": "Covered by TC-REG-SUCCESS-001"}, {"id": "COV-REG-ST-FAIL", "description": "Validation failure keeps user on RegisterPage with errors (ST-REG-FAIL-VALIDATION)", "compliance": true, "comments": "Covered by TC-REG-MANDATORY-001, TC-REG-PASSWORD-LEN-001, TC-REG-PASSWORD-CONFIRM-001, TC-REG-EMAIL-UNIQUE-001, TC-REG-USERNAME-UNIQUE-001, TC-REG-EMAIL-FORMAT-001, TC-REG-MAXLEN-NAME-001"}, {"id": "COV-REG-BC", "description": "Boundary value tests for all fields (empty, 1 char, 255 chars, 256 chars, etc.)", "compliance": true, "comments": "Covered by TC-REG-MANDATORY-001 (empty), TC-REG-MAXLEN-NAME-001 (256 chars)"}]}, {"sectionId": "UC-102_USER_LOGIN", "title": "Use Case: UC-102 - User Login", "criteria": [{"id": "COV-LOG-BR-01", "description": "Email and Password fields are mandatory (BR-LOG-01)", "compliance": true, "comments": "Covered by TC-LOG-MANDATORY-001"}, {"id": "COV-LOG-BR-02", "description": "Email must be registered in the system (BR-LOG-02)", "compliance": true, "comments": "Covered by TC-LOG-EMAIL-NOT-REGISTERED-001, TC-LOG-SUCCESS-001"}, {"id": "COV-LOG-BR-03", "description": "Password must match the registered password (BR-LOG-03)", "compliance": true, "comments": "Covered by TC-LOG-PASSWORD-MISMATCH-001, TC-LOG-SUCCESS-001"}, {"id": "COV-LOG-BR-04", "description": "Email must have a valid format (e.g., a@b.c) (BR-LOG-04)", "compliance": true, "comments": "Covered by TC-LOG-EMAIL-FORMAT-001"}, {"id": "COV-LOG-BR-05", "description": "Successful login redirects to Homepage (BR-LOG-05)", "compliance": true, "comments": "Covered by TC-LOG-SUCCESS-001"}, {"id": "COV-LOG-BR-06", "description": "Failed login displays error message (BR-LOG-06)", "compliance": true, "comments": "Covered by TC-LOG-MANDATORY-001, TC-LOG-EMAIL-FORMAT-001, TC-LOG-EMAIL-NOT-REGISTERED-001, TC-LOG-PASSWORD-MISMATCH-001"}, {"id": "COV-LOG-BR-07", "description": "Fields do not exceed 255 characters (BR-LOG-07)", "compliance": true, "comments": "Covered by TC-LOG-MAXLEN-EMAIL-001"}, {"id": "COV-LOG-ST-SUCCESS", "description": "Successful login flow (LoginPage -> Homepage) (ST-LOG-SUCCESS)", "compliance": true, "comments": "Covered by TC-LOG-SUCCESS-001"}, {"id": "COV-LOG-ST-FAIL", "description": "Failed login flow (remains on LoginPage with error) (ST-LOG-FAIL-VALIDATION)", "compliance": true, "comments": "Covered by TC-LOG-MANDATORY-001, TC-LOG-EMAIL-FORMAT-001, TC-LOG-EMAIL-NOT-REGISTERED-001, TC-LOG-PASSWORD-MISMATCH-001"}, {"id": "COV-LOG-BC-Email", "description": "Boundary value tests for email (empty,invalid email format)", "compliance": true, "comments": "Covered by TC-LOG-MANDATORY-001 (empty), TC-LOG-EMAIL-FORMAT-001 (invalid format), TC-LOG-MAXLEN-EMAIL-001 (256 chars)"}, {"id": "COV-LOG-BC-Password", "description": "Boundary value tests for password (empty,incorrect password)", "compliance": true, "comments": "Covered by TC-LOG-MANDATORY-001 (empty), TC-LOG-EMAIL-FORMAT-001 (invalid format), TC-LOG-MAXLEN-EMAIL-001 (256 chars)"}]}, {"sectionId": "UC_PRODUCT_BROWSING", "title": "Use Case: Product Browsing (Anonymous & Customer)", "criteria": [{"id": "COV-PROD-BR-01", "description": "No login required to view product list or details (BR-LIST-01, BR-PRD-01)", "compliance": true, "comments": "Covered by TC-LIST-VIEW-001, TC-PRD-VIEW-001"}, {"id": "COV-PROD-BR-02", "description": "Only APPROVED products are displayed in list and details (BR-LIST-02, BR-PRD-03)", "compliance": true, "comments": "Covered by TC-LIST-VIEW-002, TC-PRD-VIEW-002"}, {"id": "COV-PROD-BR-03", "description": "Product list displays name, price, category (BR-LIST-03)", "compliance": true, "comments": "Covered by TC-LIST-VIEW-001"}, {"id": "COV-PROD-BR-04", "description": "Product details display name, price, stock, category (BR-PRD-02)", "compliance": true, "comments": "Covered by TC-PRD-VIEW-001"}]}, {"sectionId": "UC-203_ADD_TO_CART", "title": "Use Case: UC-203 - Add Product to Cart", "criteria": [{"id": "COV-PROD-BR-05", "description": "Adding to cart requires login (BR-PRD-04)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-LOGIN-001"}, {"id": "COV-PROD-BR-06", "description": "Quantity added to cart must be positive integer and not exceed stock (BR-PRD-05)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-FAIL-001, TC-PRD-ADD-TO-CART-FAIL-002"}, {"id": "COV-PROD-ST-ADD-TO-CART", "description": "Successful add-to-cart redirects to ShoppingCartPage (ST-PRD-ADD-TO-CART)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-001"}, {"id": "COV-PROD-ST-ADD-FAIL", "description": "Invalid add-to-cart attempt remains on ProductDetailPage with error (ST-PRD-ADD-TO-CART-FAIL)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-FAIL-001, TC-PRD-ADD-TO-CART-FAIL-002"}, {"id": "COV-PROD-ST-ADD-LOGIN", "description": "Add-to-cart without login redirects to LoginPage (ST-PRD-ADD-TO-CART-LOGIN)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-LOGIN-001"}, {"id": "COV-PROD-BC", "description": "Boundary value tests for quantity (0, 1, stock, stock + 1)", "compliance": true, "comments": "Covered by TC-PRD-ADD-TO-CART-FAIL-002 (0), TC-PRD-ADD-TO-CART-001 (1), TC-PRD-ADD-TO-CART-FAIL-001 (stock + 1)"}]}, {"sectionId": "UC-204_205_SHOPPING_CART", "title": "Use Case: UC-204 & UC-205 - Shopping Cart Management", "criteria": [{"id": "COV-CART-BR-01", "description": "Login required to use shopping cart (BR-CART-01)", "compliance": true, "comments": "Covered by TC-CART-LOGIN-001"}, {"id": "COV-CART-BR-02", "description": "Products can be added to cart from ProductDetailPage (BR-CART-02)", "compliance": true, "comments": "Covered by TC-CART-ADD-001"}, {"id": "COV-CART-BR-03", "description": "Quantity in cart must be positive integer (BR-CART-03)", "compliance": true, "comments": "Covered by TC-CART-QUANTITY-001"}, {"id": "COV-CART-BR-04", "description": "Quantity cannot exceed stock (BR-CART-04)", "compliance": true, "comments": "Covered by TC-CART-ADD-002"}, {"id": "COV-CART-BR-05", "description": "User can update or delete items in cart (BR-CART-05)", "compliance": true, "comments": "Covered by TC-CART-UPDATE-001, TC-CART-DELETE-001"}, {"id": "COV-CART-BR-06", "description": "Total price is calculated based on product price and quantity (BR-CART-06)", "compliance": true, "comments": "Covered by TC-CART-UPDATE-001"}, {"id": "COV-CART-ST-ADD", "description": "Successful add-to-cart redirects to ShoppingCartPage (ST-CART-ADD)", "compliance": true, "comments": "Covered by TC-CART-ADD-001"}, {"id": "COV-CART-ST-UPDATE", "description": "Quantity update refreshes cart with new total (ST-CART-UPDATE)", "compliance": true, "comments": "Covered by TC-CART-UPDATE-001"}, {"id": "COV-CART-ST-DELETE", "description": "Item deletion updates cart (ST-CART-DELETE)", "compliance": true, "comments": "Covered by TC-CART-DELETE-001"}, {"id": "COV-CART-ST-CHECKOUT", "description": "Checkout navigation to CheckoutPage (ST-CART-CHECKOUT)", "compliance": true, "comments": "Covered by TC-CART-CHECKOUT-001"}, {"id": "COV-CART-BC", "description": "Boundary value tests for quantity (0, 1, stock, stock + 1)", "compliance": true, "comments": "Covered by TC-CART-QUANTITY-001 (0), TC-CART-ADD-001 (1), TC-CART-ADD-002 (stock + 1)"}]}, {"sectionId": "UC-207_CHECKOUT", "title": "Use Case: UC-207 - Checkout", "criteria": [{"id": "COV-CHK-BR-01", "description": "Login required to checkout (BR-CHK-01)", "compliance": true, "comments": "Covered by TC-CHK-LOGIN-001"}, {"id": "COV-CHK-BR-02", "description": "Cart must have at least one product (BR-CHK-02)", "compliance": true, "comments": "Covered by TC-CHK-EMPTY-CART-001"}, {"id": "COV-CHK-BR-03", "description": "Valid shipping address is required (BR-CHK-03)", "compliance": true, "comments": "Covered by TC-CHK-ADDRESS-001"}, {"id": "COV-CHK-BR-04", "description": "Valid payment method is required (BR-CHK-04)", "compliance": true, "comments": "Covered by TC-CHK-PAYMENT-001"}, {"id": "COV-CHK-BR-05", "description": "Total order value is correctly calculated (BR-CHK-05)", "compliance": true, "comments": "Covered by TC-CHK-SUCCESS-001"}, {"id": "COV-CHK-BR-06", "description": "Order is created with PENDING status upon successful checkout (BR-CHK-06)", "compliance": true, "comments": "Covered by TC-CHK-SUCCESS-001"}, {"id": "COV-CHK-BR-07", "description": "Optional special instructions can be added (BR-CHK-07)", "compliance": true, "comments": "Covered by TC-CHK-NOTE-001"}, {"id": "COV-CHK-ST-SUCCESS", "description": "Successful checkout redirects to OrderConfirmationPage (ST-CHK-SUCCESS)", "compliance": true, "comments": "Covered by TC-CHK-SUCCESS-001"}, {"id": "COV-CHK-ST-FAIL", "description": "Validation failure keeps user on CheckoutPage with errors (ST-CHK-FAIL-VALIDATION)", "compliance": true, "comments": "Covered by TC-CHK-ADDRESS-001, TC-CHK-PAYMENT-001"}, {"id": "COV-CHK-BC", "description": "Boundary value tests for address and notes (empty, 255 chars, 256 chars)", "compliance": true, "comments": "Covered by TC-CHK-ADDRESS-001 (empty)"}]}, {"sectionId": "UC_ORDER_MANAGEMENT", "title": "Use Case: Order Management (<PERSON>er, Seller, Admin)", "criteria": [{"id": "COV-ORD-BR-01", "description": "Login required to manage orders (BR-ORD-01)", "compliance": true, "comments": "Covered by TC-ORD-LOGIN-001"}, {"id": "COV-ORD-BR-02", "description": "Customers can only view and cancel their own orders (BR-ORD-02)", "compliance": true, "comments": "Covered by TC-ORD-VIEW-CUSTOMER-001"}, {"id": "COV-ORD-BR-03", "description": "Orders can only be canceled if in PENDING or CONFIRMED status (BR-ORD-03)", "compliance": true, "comments": "Covered by TC-ORD-CANCEL-CUSTOMER-001, TC-ORD-CANCEL-FAIL-001"}, {"id": "COV-ORD-BR-04", "description": "Sellers can only manage orders related to their products (BR-ORD-04)", "compliance": true, "comments": "Covered by TC-ORD-VIEW-SELLER-001"}, {"id": "COV-ORD-BR-05", "description": "Sellers can update order status (e.g., from PENDING to SHIPPED) (BR-ORD-05)", "compliance": true, "comments": "Covered by TC-ORD-UPDATE-SELLER-001"}, {"id": "COV-ORD-BR-06", "description": "Admins can view and manage all orders (BR-ORD-06)", "compliance": true, "comments": "Covered by TC-ORD-VIEW-ADMIN-001"}, {"id": "COV-ORD-BR-07", "description": "Notifications are sent when order status changes (BR-ORD-07)", "compliance": true, "comments": "Covered by TC-ORD-CANCEL-CUSTOMER-001, TC-ORD-UPDATE-SELLER-001"}, {"id": "COV-ORD-ST-CANCEL", "description": "Successful order cancellation updates status to CANCELLED (ST-ORD-CANCEL-CUSTOMER)", "compliance": true, "comments": "Covered by TC-ORD-CANCEL-CUSTOMER-001"}, {"id": "COV-ORD-ST-CANCEL-FAIL", "description": "Failed cancellation attempt shows error (ST-ORD-CANCEL-FAIL)", "compliance": true, "comments": "Covered by TC-ORD-CANCEL-FAIL-001"}, {"id": "COV-ORD-ST-UPDATE-SELLER", "description": "Seller updates order status successfully (ST-ORD-UPDATE-SELLER)", "compliance": true, "comments": "Covered by TC-ORD-UPDATE-SELLER-001"}, {"id": "COV-ORD-BC", "description": "Boundary value tests for order status and number of orders (0, 1, large numbers)", "compliance": true, "comments": "Covered by TC-ORD-VIEW-CUSTOMER-001 (1)"}]}, {"sectionId": "UC-212_CANCEL_ORDER", "title": "Use Case: UC-212 - Cancel Order", "criteria": [{"id": "COV-CANCEL-BR-01", "description": "ROLE_USER login required to cancel order (BR-CANCEL-01)", "compliance": true, "comments": "Covered by TC-CANCEL-LOGIN-001"}, {"id": "COV-CANCEL-BR-02", "description": "Orders can only be canceled if in PENDING or CONFIRMED status (BR-CANCEL-02)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001, TC-CANCEL-SUCCESS-002, TC-CANCEL-FAIL-001"}, {"id": "COV-CANCEL-BR-03", "description": "Order status updates to CANCELLED upon successful cancellation (BR-CANCEL-03)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001"}, {"id": "COV-CANCEL-BR-04", "description": "Notification is sent to seller upon cancellation (BR-CANCEL-04)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001"}, {"id": "COV-CANCEL-BR-05", "description": "Order history is updated after cancellation (BR-CANCEL-05)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001"}, {"id": "COV-CANCEL-ST-SUCCESS", "description": "Successful cancellation updates order status (ST-CANCEL-SUCCESS)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001"}, {"id": "COV-CANCEL-ST-FAIL", "description": "Failed cancellation attempt shows error (ST-CANCEL-FAIL)", "compliance": true, "comments": "Covered by TC-CANCEL-FAIL-001"}, {"id": "COV-CANCEL-BC", "description": "Boundary value tests for order status (PENDING, CONFIRMED, SHIPPED, etc.)", "compliance": true, "comments": "Covered by TC-CANCEL-SUCCESS-001 (PENDING), TC-CANCEL-SUCCESS-002 (CONFIRMED), TC-CANCEL-FAIL-001 (SHIPPED)"}]}, {"sectionId": "UC-PROFILE_MANAGEMENT", "title": "Use Case: Profile Management (All Users)", "criteria": [{"id": "COV-PROF-BR-01", "description": "Login required to manage profile (BR-PROF-01)", "compliance": true, "comments": "Covered by TC-PROF-LOGIN-001"}, {"id": "COV-PROF-BR-02", "description": "Mandatory fields for profile update: <PERSON><PERSON>ê<PERSON>, <PERSON><PERSON> (BR-PROF-02)", "compliance": true, "comments": "Covered by TC-PROF-EDIT-FAIL-001"}, {"id": "COV-PROF-BR-03", "description": "Email must be unique and valid (BR-PROF-03)", "compliance": true, "comments": "Covered by TC-PROF-EMAIL-UNIQUE-001"}, {"id": "COV-PROF-BR-04", "description": "New password must be at least 8 characters (BR-PROF-04)", "compliance": true, "comments": "Covered by TC-PROF-CHANGE-PASSWORD-001"}, {"id": "COV-PROF-BR-05", "description": "Confirmation password must match new password (BR-PROF-05)", "compliance": true, "comments": "Covered by TC-PROF-CHANGE-PASSWORD-FAIL-001"}, {"id": "COV-PROF-BR-06", "description": "Fields do not exceed 255 characters (BR-PROF-06)", "compliance": true, "comments": "Covered by TC-PROF-MAXLEN-NAME-001"}, {"id": "COV-PROF-BR-07", "description": "Profile updates are saved to database (BR-PROF-07)", "compliance": true, "comments": "Covered by TC-PROF-EDIT-001, TC-PROF-CHANGE-PASSWORD-001"}, {"id": "COV-PROF-BR-08", "description": "User can view order history from profile (BR-PROF-08)", "compliance": true, "comments": "Covered by TC-PROF-NAV-ORDERS-001"}, {"id": "COV-PROF-ST-EDIT", "description": "Successful profile edit updates information (ST-PROF-EDIT)", "compliance": true, "comments": "Covered by TC-PROF-EDIT-001"}, {"id": "COV-PROF-ST-EDIT-FAIL", "description": "Failed profile edit shows error (ST-PROF-EDIT-FAIL)", "compliance": true, "comments": "Covered by TC-PROF-EDIT-FAIL-001"}, {"id": "COV-PROF-ST-CHANGE-PASSWORD", "description": "Successful password change (ST-PROF-CHANGE-PASSWORD)", "compliance": true, "comments": "Covered by TC-PROF-CHANGE-PASSWORD-001"}, {"id": "COV-PROF-ST-CHANGE-PASSWORD-FAIL", "description": "Failed password change shows error (ST-PROF-CHANGE-PASSWORD-FAIL)", "compliance": true, "comments": "Covered by TC-PROF-CHANGE-PASSWORD-FAIL-001"}, {"id": "COV-PROF-BC", "description": "Boundary value tests for fields (empty, 255 chars, 256 chars)", "compliance": true, "comments": "Covered by TC-PROF-EDIT-FAIL-001 (empty), TC-PROF-MAXLEN-NAME-001 (256 chars)"}]}, {"sectionId": "UC-302_405_PRODUCT_MANAGEMENT", "title": "Use Case: UC-302 & UC-405 - Product Management (Seller & Admin)", "criteria": [{"id": "COV-PROD-MGMT-BR-01", "description": "Seller must be logged in to manage products (BR-PROD-01)", "compliance": true, "comments": "Covered by TC-PROD-LOGIN-SELLER-001"}, {"id": "COV-PROD-MGMT-BR-02", "description": "Mandatory fields for product creation: Name, Price, Stock, Category (BR-PROD-02)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-FAIL-001"}, {"id": "COV-PROD-MGMT-BR-03", "description": "Product price must be positive (BR-PROD-03)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-001"}, {"id": "COV-PROD-MGMT-BR-04", "description": "Stock quantity must be non-negative integer (BR-PROD-04)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-001"}, {"id": "COV-PROD-MGMT-BR-05", "description": "New products have PENDING APPROVAL status and are not visible (BR-PROD-05)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-001"}, {"id": "COV-PROD-MGMT-BR-06", "description": "Admin must be logged in to approve/reject products (BR-PROD-06)", "compliance": true, "comments": "Covered by TC-PROD-LOGIN-ADMIN-001"}, {"id": "COV-PROD-MGMT-BR-07", "description": "Rejected or deleted products are not visible (BR-PROD-07)", "compliance": true, "comments": "Covered by TC-PROD-DELETE-001, TC-PROD-REJECT-001"}, {"id": "COV-PROD-MGMT-BR-08", "description": "Product name must not exceed 255 characters (BR-PROD-08)", "compliance": true, "comments": "Covered by TC-PROD-MAXLEN-NAME-001"}, {"id": "COV-PROD-MGMT-ST-CREATE", "description": "Successful product creation with PENDING APPROVAL (ST-PROD-CREATE)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-001"}, {"id": "COV-PROD-MGMT-ST-CREATE-FAIL", "description": "Failed product creation shows error (ST-PROD-CREATE-FAIL)", "compliance": true, "comments": "Covered by TC-PROD-CREATE-FAIL-001"}, {"id": "COV-PROD-MGMT-ST-APPROVE", "description": "Admin approves product, status changes to APPROVED (ST-PROD-APPROVE)", "compliance": true, "comments": "Covered by TC-PROD-APPROVE-001"}, {"id": "COV-PROD-MGMT-ST-REJECT", "description": "Admin rejects product, status changes to REJECTED (ST-PROD-REJECT)", "compliance": true, "comments": "Covered by TC-PROD-REJECT-001"}]}, {"sectionId": "UC-310_409_DASHBOARD", "title": "Use Case: UC-310 & UC-409 - Report Dashboard (Seller & Admin)", "criteria": [{"id": "COV-RPT-BR-01", "description": "Seller must be logged in to view Seller Dashboard (BR-RPT-01)", "compliance": true, "comments": "Covered by TC-RPT-LOGIN-SELLER-001"}, {"id": "COV-RPT-BR-02", "description": "Admin must be logged in to view Admin Dashboard (BR-RPT-02)", "compliance": true, "comments": "Covered by TC-RPT-LOGIN-ADMIN-001"}, {"id": "COV-RPT-BR-03", "description": "Seller Dashboard shows seller-specific metrics (BR-RPT-03)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-SELLER-001"}, {"id": "COV-RPT-BR-04", "description": "Admin Dashboard shows system-wide metrics (BR-RPT-04)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-ADMIN-001"}, {"id": "COV-RPT-BR-05", "description": "Data is updated in real-time or near real-time (BR-RPT-05)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-SELLER-001, TC-RPT-VIEW-ADMIN-001"}, {"id": "COV-RPT-BR-06", "description": "Seller can only access Products and Orders links from dashboard (BR-RPT-06)", "compliance": true, "comments": "Covered by TC-RPT-NAV-PRODUCTS-001, TC-RPT-NAV-ORDERS-001"}, {"id": "COV-RPT-BR-07", "description": "Displayed data matches database (BR-RPT-07)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-SELLER-001, TC-RPT-VIEW-ADMIN-001"}, {"id": "COV-RPT-ST-VIEW-SELLER", "description": "Seller views dashboard with metrics (ST-RPT-VIEW-SELLER)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-SELLER-001"}, {"id": "COV-RPT-ST-VIEW-ADMIN", "description": "Admin views dashboard with system-wide metrics (ST-RPT-VIEW-ADMIN)", "compliance": true, "comments": "Covered by TC-RPT-VIEW-ADMIN-001"}, {"id": "COV-RPT-BC", "description": "Boundary value tests for metrics (0, 1, large numbers)", "compliance": true, "comments": "Covered by TC-RPT-ZERO-DATA-SELLER-001 (0), TC-RPT-VIEW-SELLER-001 (1)"}]}, {"sectionId": "UC-404_MANAGE_CATEGORIES", "title": "Feature: UC-404 - Manage Categories (Admin)", "criteria": [{"id": "COV-CAT-BR-01", "description": "Admin login is required to access category management (BR-CAT-01)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BR-02", "description": "Category Name is a mandatory field for creation and editing (BR-CAT-02)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BR-03", "description": "Category Name must be unique within the system (BR-CAT-03)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BR-04", "description": "Admin can create, edit, and view a list of categories (BR-CAT-04)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BR-05", "description": "A category cannot be deleted if it has products assigned to it (BR-CAT-05)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BR-06", "description": "A category cannot be deleted if it has sub-categories (BR-CAT-06)", "compliance": false, "comments": ""}, {"id": "COV-CAT-ST-SUCCESS", "description": "Successful create/edit/delete shows a success message and updates the category list (ST-CAT-SUCCESS)", "compliance": false, "comments": ""}, {"id": "COV-CAT-ST-FAIL", "description": "Validation failure (e.g., duplicate name) shows an error message on the form (ST-CAT-FAIL)", "compliance": false, "comments": ""}, {"id": "COV-CAT-ST-DELETE-FAIL", "description": "Attempting to delete a category with associated products shows a specific error message (ST-CAT-DELETE-FAIL)", "compliance": false, "comments": ""}, {"id": "COV-CAT-BC", "description": "Boundary value tests for Category Name field (empty, 255 chars, 256 chars)", "compliance": false, "comments": ""}]}, {"sectionId": "UC-211_VIEW_BOOKING_HISTORY", "title": "Feature: UC-211 - View Booking/Order History & Status", "criteria": [{"id": "COV-BKH-BR-01", "description": "Customer login is required to view order history (BR-BKH-01)", "compliance": false, "comments": ""}, {"id": "COV-BKH-BR-02", "description": "Customer can only see their own orders, not others' (BR-BKH-02)", "compliance": false, "comments": ""}, {"id": "COV-BKH-BR-03", "description": "Order history list displays essential info: Order ID, Date, Total, and Status (BR-BKH-03)", "compliance": false, "comments": ""}, {"id": "COV-BKH-BR-04", "description": "A 'View Details' link is available for each order, navigating to the correct order detail page (BR-BKH-04)", "compliance": false, "comments": ""}, {"id": "COV-BKH-BR-05", "description": "If a customer has no orders, a clear message is displayed (BR-BKH-05)", "compliance": false, "comments": ""}, {"id": "COV-BKH-ST-NAV", "description": "Successful navigation from main menu/profile to Order History page (ST-BKH-NAV)", "compliance": false, "comments": ""}]}, {"sectionId": "UC-MANAGE_USERS", "title": "Feature: Manage Users (Admin)", "criteria": [{"id": "COV-USR-BR-01", "description": "Admin login is required to access user management (BR-USR-01)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-02", "description": "Admin can view a paginated list of all users (<PERSON><PERSON>, Se<PERSON>, Customer) (BR-USR-02)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-03", "description": "Admin can filter users by role or search by email/name (BR-USR-03)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-04", "description": "Admin can change a user's role (e.g., Customer to Seller) (BR-USR-04)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-05", "description": "Admin can activate or deactivate a user's account (BR-USR-05)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-06", "description": "Admin cannot delete a user with associated orders or products (deactivation is the correct action) (BR-USR-06)", "compliance": false, "comments": ""}, {"id": "COV-USR-BR-07", "description": "Admin cannot deactivate or change their own role (BR-USR-07)", "compliance": false, "comments": ""}, {"id": "COV-USR-ST-UPDATE-SUCCESS", "description": "Successful update of role/status reflects immediately in the user list with a success message (ST-USR-UPDATE-SUCCESS)", "compliance": false, "comments": ""}, {"id": "COV-USR-ST-UPDATE-FAIL", "description": "Attempting to perform a forbidden action (e.g., deleting self) shows an error message (ST-USR-UPDATE-FAIL)", "compliance": false, "comments": ""}, {"id": "COV-USR-BC", "description": "Boundary value tests for user list (0 users, 1 user, enough users for pagination)", "compliance": false, "comments": ""}]}, {"sectionId": "UC-MANAGE_ADDRESSES", "title": "Feature: Manage Addresses (Customer)", "criteria": [{"id": "COV-ADR-BR-01", "description": "Customer login is required to manage addresses (BR-ADR-01)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-02", "description": "Customer can add a new shipping address from their profile page (BR-ADR-02)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-03", "description": "Mandatory fields for address: Recipient Name, Phone, Street, City, Country (BR-ADR-03)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-04", "description": "Customer can edit an existing shipping address (BR-ADR-04)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-05", "description": "Customer can delete a shipping address (BR-ADR-05)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-06", "description": "Customer can set one address as the 'Default' address (BR-ADR-06)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BR-07", "description": "Setting a new default address unsets the previous default (BR-ADR-07)", "compliance": false, "comments": ""}, {"id": "COV-ADR-ST-SUCCESS", "description": "Successful add/edit/delete/set-default shows a success message and updates the address list (ST-ADR-SUCCESS)", "compliance": false, "comments": ""}, {"id": "COV-ADR-ST-FAIL", "description": "Validation failure on mandatory fields shows an error on the form (ST-ADR-FAIL)", "compliance": false, "comments": ""}, {"id": "COV-ADR-BC", "description": "Boundary value tests for address fields (empty, max length) and number of addresses (0, 1, many)", "compliance": false, "comments": ""}]}]}