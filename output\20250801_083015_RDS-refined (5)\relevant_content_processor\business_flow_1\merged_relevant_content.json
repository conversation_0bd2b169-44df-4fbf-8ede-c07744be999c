[{"heading_number": "1.2", "title": "1.2 Actors", "Content": "about\n \nrequest\n \nstatus\n \n(optional,\n \nnot\n \nexplicitly\n \nimplemented\n \nyet).\",\n \n      \n\"interactions\":\n \n[\n \n        \n\"Receives\n \nnotification\n \ntriggers\n \nfrom\n \nRetailOnboardPro\"\n \n      \n]\n \n    \n}\n \n  \n]\n \n}\n \n \n1.2\n \nActors\n \nTable\n \n1:\n \nActor\n \n#\n \nActor\n \n \nDescription\n \n1\n \nManager\n \n \nTake\n \nresponsible\n \nto\n \nmanage\n \nrequest,\n \nchange\n \nstatus\n \n2\n \nStaff\n \n \nCreate\n \nand\n \nsend\n \nrequest\n \nto\n \nmanager\n\n3\n \nDirector\n \n \nReviews\n \nand\n \napproves/rejects\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers\n \nor\n \nrequiring\n \n4\n \nCEO\n \n \nHighest\n \nlevel\n \nof\n \napproval\n \nfor\n \nspecific\n \nor\n \nhigh-impact\n \ntraining\n \nrequests.\n \n \n2.\n \nOverall\n \nFunctionalities\n \n2.1\n \nScreens\n \nFlow\n \n2.1.0)\n \n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \n-\n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"Homepage\"\n,\n \n\"description\"\n:\n \n\"Initial\n \nlanding\n \npage\n \nfor\n \nall\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RegistrationPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n}\n \n    \n//\n \nRole-specific\n \ndashboards\n \nare\n \nlisted\n \nin\n \ntheir\n \nrespective\n \nflows\n \nbut\n \nare\n \ntargets\n \nhere\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Login'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register\n \nhere'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RegistrationPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nregistration\n \nsubmission\n \n(UC001_RegisterNewAccount)\n \nOR\n \nClicks\n \n'Already\n \nhave\n \nan\n \naccount?\n \nLogin'\n \nlink\"\n \n},", "match_type": "exact", "matched_with": "1.2 Actors"}, {"heading_number": "2.1", "title": "2.1 Screens Flow", "Content": "2.\n \nOverall\n \nFunctionalities\n \n2.1\n \nScreens\n \nFlow\n \n2.1.0)\n \n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \n-\n \nAuthentication\n \nand\n \nUnregistered\n \nUser\n \nFlow\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"Homepage\"\n,\n \n\"description\"\n:\n \n\"Initial\n \nlanding\n \npage\n \nfor\n \nall\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"RegistrationPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nnew\n \nusers\n \nto\n \ncreate\n \nan\n \naccount.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n}\n \n    \n//\n \nRole-specific\n \ndashboards\n \nare\n \nlisted\n \nin\n \ntheir\n \nrespective\n \nflows\n \nbut\n \nare\n \ntargets\n \nhere\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n{\n \n\"from\"\n:\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Login'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"Homepage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register'\n \nlink/button\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"RegistrationPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Register\n \nhere'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RegistrationPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nregistration\n \nsubmission\n \n(UC001_RegisterNewAccount)\n \nOR\n \nClicks\n \n'Already\n \nhave\n \nan\n \naccount?\n \nLogin'\n \nlink\"\n \n},\n\n//\n \nLogin\n \nsuccess\n \ntransitions\n \nare\n \nrole-specific,\n \ndetailed\n \nbelow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nSTAFF\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nMANAGER\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nDIRECTOR\n \n(UC002_UserLogin)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"LoginPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Successful\n \nlogin\n \nAND\n \nuser\n \nrole\n \nis\n \nCEO\n \n(UC002_UserLogin)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"This\n \ndescribes\n \nthe\n \ninitial\n \naccess\n \nand\n \nauthentication\n \npathways.\n \nPost-login\n \nflows\n \nare\n \ndetailed\n \nper\n \nrole.\"\n \n}\n \n2.1.1)\n \nStaff\n \nscreen\n \nflow\n \n \nFigur e\n \n1:\n \nStaff\n \nScreen\n \nFlow\n \ndiagram\n \n•\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nStaff\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nStaffDashboard,\n \nRequestList,\n \nSendRequest\n  \n \no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \nrequest\n \nlist,\n \nBack,\n \nSend\n \nnew\n \nrequest,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nStaffDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nStaffDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \nrequest\n \nlist\"\n \nor\n \n\"Send\n \nnew\n \nrequest\".\n  \n \n5.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nStaffDashboard\n \nfrom\n \neither\n \nRequestList\n \nor\n \nSendRequest.\n\n6.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nstaff\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nSTAFF\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"StaffDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nStaff\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"RequestListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \nStaff's\n \nown\n \ntraining\n \nand\n \nrole\n \nchange\n \nrequests.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewTrainingRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"CreateNewRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Form\n \nfor\n \nStaff\n \nto\n \ncreate\n \na\n \nnew\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest.\"\n \n},\n \n    \n//\n \nAdd\n \nRoleChangeRequestDetailsPage\n \nif\n \nit\n's\n \na\n \nseparate\n \nscreen\n \nfor\n \nviewing\n \nrole\n \nrequest\n \ndetails\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\",\n \n\"description\":\n \n\"Staff'\ns\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\n\"\n \n},\n \n    \n{\n \n\"\nname\n\":\n \n\"\nLoginPage\n\",\n \n\"\ndescription\n\":\n \n\"\nTarget\n \npage\n \nafter\n \nlogout\n.\n\"\n \n}\n \n  \n],\n \n  \n\"\ntransitions\n\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nMy\n \nRequests'\n \nor\n \nsimilar\n \nlink\n \n(UC006,\n \nUC009)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'CreateNewTrainingRequestPage'\n \n(UC005)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Create\n \nRole\n \nChange\n \nRequest'\n \n(UC-RC001)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nProfilePage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Profile'\n \nlink\n \n(UC012)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \ntraining\n \nrequest\n\"\n \n},\n \n    \n//\n \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nRoleChangeRequestDetailsPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'View\n \nDetails'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \ntraining\n \nrequest\n \n(UC005)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nSuccessful\n \nsubmission\n \nof\n \nrole\n \nchange\n \nrequest\n \n(UC-RC001)\n\"\n \n},\n \n//\n \nOr\n \nto\n \nDashboard\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewRoleChangeRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Cancel'\n \nor\n \n'Back'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nTrainingRequestDetailsPage\n\",\n \n\"\nto\n\":\n \n\"\nRequestListPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\ncondition\n\":\n \n\"\nNavigates\n \nback\n \nor\n \nvia\n \nsidebar\n\"\n \n},\n \n    \n//\n \nLogout\n \nfrom\n \nvarious\n \nstaff\n \npages\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nStaffDashboardPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nRequestListPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nCreateNewTrainingRequestPage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"\nProfilePage\n\",\n \n\"\nto\n\":\n \n\"\nLoginPage\n\",\n \n\"\ncondition\n\":\n \n\"\nClicks\n \n'Logout'\n \n(UC003_UserLogout)\n\"\n \n}\n \n  \n],\n \n  \n\"\nnotes\n\":\n \n\"\nPrimary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nStaff\n \nuser.\n\"\n \n}\n \n \n2.1.2)\n \nManager\n \nscreen\n \nflow\n \n \nFigur e\n \n2:\n \nManager\n \nScreen\n \nFlow\n \ndiagram\n \nPurpose:\n \nTo\n \nillustrate\n \nthe\n \nscreen\n \nflow\n \nspecifically\n \nfor\n \nthe\n \nManager\n \nrole.\n  \n \n•\n \nElements:\n \n \no\n \nStart\n \nPoint:\n \nBlack\n \ncircle\n  \n \no\n \nEnd\n \nPoint:\n \nBlack\n \ncircle\n \nwith\n \na\n \nwhite\n \ndot\n \nin\n \nthe\n \ncenter\n  \n \no\n \nScreens:\n \nLogin,\n \nManagerDashboard,\n \nManageRequest,\n \nRequestHistory,\n \nUpdateStatus\n\no\n \nActions:\n \nOpen\n \napplication,\n \nLogin\n \nsuccessful,\n \nView\n \n/\n \nManage\n \nrequests,\n \nView\n \nrequest\n \nhistory,\n \nBack,\n \nChange\n \nrequest\n \nstatus,\n \nLogout\n \n/\n \nExit,\n \nCancel\n \n/\n \nExit\n  \n \n•\n \nFlow:\n \n \n1.\n \nUser\n \nopens\n \nthe\n \napplication.\n  \n \n2.\n \nUser\n \nlogs\n \nin.\n  \n \n3.\n \nUpon\n \nsuccessful\n \nlogin,\n \nuser\n \nis\n \ntaken\n \nto\n \nthe\n \nManagerDashboard.\n  \n \n4.\n \nFrom\n \nthe\n \nManagerDashboard,\n \nthe\n \nuser\n \ncan\n \nchoose\n \nto\n \n\"View\n \n/\n \nManage\n \nrequests\"\n \nor\n \n\"View\n \nrequest\n \nhistory\".\n  \n \n5.\n \nWithin\n \n\"Manage\n \nrequests\",\n \nthe\n \nuser\n \ncan\n \n\"Change\n \nrequest\n \nstatus\",\n \nleading\n \nto\n \nthe\n \nUpdateStatus\n \nscreen.\n  \n \n6.\n \nThe\n \nuser\n \ncan\n \nnavigate\n \nback\n \nto\n \nthe\n \nManagerDashboard\n \nfrom\n \neither\n \nManageRequest\n \nor\n \nRequestHistory.\n  \n \n7.\n \nThe\n \nuser\n \ncan\n \n\"Logout\n \n/\n \nExit\"\n \nor\n \n\"Cancel\n \n/\n \nExit\"\n \nthe\n \napplication.\n  \n \nDescription\n \nof\n \nManager\n \nscreen\n \nflow\n \nin\n \njson:\n \n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nMANAGER\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n{\n \n\"name\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Primary\n \ndashboard\n \nfor\n \nManager\n \nusers.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Lists\n \ntraining\n \nrequests\n \npending\n \nManager's\n \naction\n \nor\n \nrelevant\n \nto\n \ntheir\n \nteam.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \ndetails\n \nof\n \na\n \nspecific\n \ntraining\n \nrequest\n \nfor\n \nManager\n \nreview/action.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"description\"\n:\n \n\"Displays\n \nhistorical\n \ntraining\n \nrequests\n \nrelevant\n \nto\n \nthe\n \nManager.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Manager's\n \nuser\n \nprofile\n \nmanagement\n \nscreen.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Target\n \npage\n \nafter\n \nlogout.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'View\n \nTraining\n \nRequest\n \nHistory'\n \n(UC-MR002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012)\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"ManageTrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\n \n(UC-MR001)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestHistoryPage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"ManagerDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Navigates\n \nback\n \nor\n \nvia\n \nsidebar\"\n \n},\n \n    \n//\n \nLogout\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ManagerDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003_UserLogout)\"\n \n}\n \n    \n//\n \nAdd\n \nother\n \nlogout\n \ntransitions\n \n  \n],\n \n  \n\"notes\":\n \n\"Primary\n \nnavigation\n \npaths\n \nfor\n \nan\n \nauthenticated\n \nManager\n \nuser.\"\n \n}\n \n2.1.3)\n \nDirector\n \nscreen\n \nflow\n \n \n{\n \n  \n\"diagramName\"\n:\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nDIRECTOR\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nDIRECTOR,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-DR001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \nif\n \ndifferent\n \nfrom\n \nManager's\n \nlist\n \n    \n{\n \n\"name\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nDIRECTOR\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n \n    \n{\n \n\"name\"\n:\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nDIRECTOR's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\"\n:\n \n[\n\n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-DR001:\n \nDirector\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \n(Approve/Reject)\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"DirectorDashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"from\"\n:\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\"\n:\n \n\"The\n \nDIRECTOR\n \ncan\n \nview\n \nand\n \nreview\n \ntraining\n \nrequests\n \nescalated\n \nfrom\n \nManagers.\n \nThey\n \ntypically\n \ndo\n \nnot\n \nmanage\n \nrole\n \nchange\n \nrequests\n \ndirectly,\n \nwhich\n \nare\n \nhandled\n \nby\n \nthe\n \nCEO.\"\n \n}\n \n2.1.4)\n \nCEO\n \nScreen\n \nFlow\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nScreen\n \nFlow\n \n-\n \nCEO\n \nUser\"\n,\n \n  \n\"screens\"\n:\n \n[\n \n    \n//\n \nUse\n \ncanonical\n \nnames\n \nfrom\n \nyour\n \nAppendix\n \nA\n \n    \n{\n \n\"name\"\n:\n \n\"LoginPage\"\n,\n \n\"description\"\n:\n \n\"Entry\n \nscreen\n \nfor\n \nuser\n \nauthentication.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"CEODashboardPage\"\n,\n \n\"description\"\n:\n \n\"Role-specific\n \ndashboard\n \nfor\n \nCEO,\n \nproviding\n \naccess\n \nto\n \ntraining\n \nrequests,\n \nrole\n \nrequests,\n \nand\n \nprofile.\"\n \n},\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \ntraining\n \nrequests\n \nescalated\n \nto\n \nthem\n \n(UC-CE001).\"\n \n},\n \n//\n \nMay\n \nneed\n \na\n \ndistinct\n \nname\n \n    \n{\n \n\"name\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nto\n \nview\n \ndetails\n \nand\n \naction\n \na\n \ntraining\n \nrequest.\"\n \n},\n\n{\n \n\"name\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nCEO\n \nfor\n \nviewing\n \nand\n \nreviewing\n \npending\n \nrole\n \nchange\n \nrequests\n \n(UC-RC002).\"\n \n},\n \n//\n \nCorresponds\n \nto\n \nscreen\n \non\n \np64\n \n    \n{\n \n\"name\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"description\"\n:\n \n\"Modal\n \nwithin\n \nApproveRoleChangeRequestPage\n \nfor\n \nCEO\n \nto\n \naction\n \na\n \nspecific\n \nrole\n \nchange\n \nrequest.\"\n \n},\n \n//\n \nFrom\n \np66\n \n    \n{\n \n\"name\":\n \n\"ProfilePage\"\n,\n \n\"description\"\n:\n \n\"Screen\n \nfor\n \nviewing\n \nthe\n \nCEO's\n \nprofile.\"\n \n}\n \n  \n],\n \n  \n\"transitions\":\n \n[\n \n    \n//\n \nEntry\n \nfrom\n \nLogin\n \nis\n \nin\n \nAuthentication\n \nFlow\n \n    \n{\n \n\"from\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Manage\n \nTraining\n \nRequests'\n \nlink\n \n(UC-CE001:\n \nCEO\n \nManages\n \nTraining\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Approve\n \nRole\n \nChange\n \nRequests'\n \nlink\n \n(UC-RC002:\n \nCEO\n \nManages\n \nRole\n \nChange\n \nRequest)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"ProfilePage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Profile'\n \nlink\n \n(UC012:\n \nView\n \nProfile)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"CEODashboardPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"condition\"\n:\n \n\"Selects\n \na\n \ntraining\n \nrequest\n \nto\n \nreview/action\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestsListPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"TrainingRequestDetailsPage\"\n,\n \n\"to\"\n:\n \n\"TrainingRequestsListPage\"\n,\n \n\"condition\"\n:\n \n\"After\n \naction\n \nor\n \nClicks\n \n'Back\n \nto\n \nList'\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Process'\n \non\n \na\n \nrole\n \nchange\n \nrequest\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"RoleChangeRequestDetailsModal\"\n,\n \n\"to\"\n:\n \n\"ApproveRoleChangeRequestPage\"\n,\n \n\"condition\"\n:\n \n\"Submits\n \ndecision\n \nor\n \nCancels\n \nmodal\n \n(UC-RC002)\"\n \n},\n \n    \n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"CEODashboardPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Back\n \nto\n \nDashboard'\n \nlink\"\n \n},\n\n{\n \n\"\nfrom\n\":\n \n\"ProfilePage\"\n,\n \n\"to\"\n:\n \n\"LoginPage\"\n,\n \n\"condition\"\n:\n \n\"Clicks\n \n'Logout'\n \n(UC003:\n \nUser\n \nLogout)\"\n \n}\n \n  \n],\n \n  \n\"notes\":\n \n\"The\n \nCEO\n \nhas\n \norganization-wide\n \nauthority\n \nfor\n \nfinal\n \napproval\n \nof\n \ntraining\n \nrequests\n \nand\n \nmanages", "match_type": "exact", "matched_with": "2.1 Screens Flow"}, {"heading_number": "2.1", "title": "2.1 Use Case: Login System", "Content": "in\n \nthe\n \ntargeted\n \nusers'\n \npermissions\n \nand\n \naccess\n \ncapabilities,\n \nand\n \na\n \ncomprehensive\n \naudit\n \nlog\n \nof\n \nthese\n \nadministrative\n \nactions\n \nis\n \nmaintained.\n \nNormal\n \nFlow\n \n1.\n \nCEO\n \nlogs\n \nin\n \n(UC002).\n \n2.\n \nNavigates\n \nto\n \ndashboard\n \nor\n \napproval\n \npage.\n \n3.\n \nViews\n \nlist\n \nof\n \npending\n \nrequests.\n \n4.\n \nSelects\n \nrequest\n \nto\n \nreview.\n \n5.\n \nReviews\n \ndetails.\n \n6a.\n \nApproves:\n \nvalidates\n \ncomments,\n \nupdates\n \nstatus,\n \nupdates\n \nrole,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n6b.\n \nRejects:\n \nrequires\n \ncomments,\n \nupdates\n \nstatus,\n \nnotifies\n \nuser,\n \nshows\n \nsuccess\n \nmessage.\n \n7.\n \nRequest\n \nremoved\n \nfrom\n \npending\n \nlist.\n \n8.\n \nCEO\n \nreturns\n \nto\n \nlist\n \nor\n \ndashboard.\n \nAlternative\n \nFlows\n \n-\n \nAF1:\n \nNo\n \npending\n \nrequests:\n \nSystem\n \nshows\n \n\"No\n \nrole\n \nchange\n \nrequests\n \nawaiting\n \napproval.\"\n \n-\n \nAF2:\n \nRequest\n \nalready\n \nprocessed:\n \nSystem\n \ninforms\n \nCEO\n \nand\n \nrefreshes\n \nlist.\n \nExceptions\n \nE1:\n \nSystem\n \nError\n \nDuring\n \nUpdate:\n \nLogs\n \nerror,\n \nrollbacks\n \npartial\n \nchanges,\n \nshows\n \nerror\n \nmessage,\n \nno\n \nchanges\n \npersisted.\n \nPriority\n \nHigh\n \nFrequency\n \nof\n \nUse\n \nLow\n \nto\n \nModerate\n \nBusiness\n \nRules\n \n-\n \nBR-RC002-1:\n \nCEO\n \nacts\n \nonly\n \non\n \npending\n \nrequests\n \nassigned/visible\n \nto\n \nthem.\n \n-\n \nBR-RC002-2:\n \nComments\n \nmandatory\n \nwhen\n \nrejecting.\n \n-\n \nBR-RC002-3:\n \nSystem\n \nmust\n \nupdate\n \nuser's\n \nrole\n \non\n \napproval.\n \n-\n \nBR-RC002-4:\n \nAll\n \nactions\n \nlogged\n \nwith\n \nactor,\n \ntimestamp,\n \ncomments.\n \n-\n \nBR-RC002-5:\n \nMulti-level\n \napproval\n \nworkflows\n \napply\n \n(CEO\n \nstep\n \nspecific).\n \nRelated\n \nUse\n \nCases\n \nUC002:\n \nUser\n \nLogin,\n \nUC004:\n \nView\n \nDashboard,\n \nUC-RC001:\n \nStaff\n \nCreates\n \nRole\n \nChange\n \nRequest\n \nScreen\n \nRelated\n \nApprove\n \nRoleChangeRequestScreen_StaffView\n \n \nAssumptions\n \nCEO\n \nunderstands\n \nrole\n \nchange\n \nimplications;\n \nnotification\n \nmechanisms\n \nexist.\n \n \n \n \n2.\n \nCommon\n \nFunctions\n \n2.1\n \nUse\n \nCase:\n \nLogin\n \nSystem\n \n{\n \n  \n\"diagramName\":\n \n\"RetailOnboardPro\n \nUse\n \nCase\n \nDiagram\n \n-\n \nUnregistered\n \nUser\",\n \n  \n\"actors\":\n \n[\n \n    \n{\"name\":\n \n\"Unregistered\n \nUser\"}\n \n  \n],\n \n  \n\"useCases\":\n \n[\n\n{\"id\":\n \n\"UC001\",\n \n\"name\":\n \n\"Register\n \nNew\n \nAccount\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]},\n \n    \n{\"id\":\n \n\"UC002\",\n \n\"name\":\n \n\"User\n \nLogin\",\n \n\"actors\":\n \n[\"Unregistered\n \nUser\"]}\n \n  \n],\n \n  \n\"relationships\":\n \n[\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC001\"},\n \n    \n{\"type\":\n \n\"association\",\n \n\"from\":\n \n\"Unregistered\n \nUser\",\n \n\"to\":\n \n\"UC002\"}\n \n  \n]\n \n}\n \n \na.\n \nFunctional\n \nDescription\n \nTable\n \n15Use\n \ncase\n \ndescription\n \nof\n \nUC_1:\n \nLogin.\n \nUC\n \nID\n \nand\n \nName:\n \nUse\n \nCase\n \nID:\n \nUC_1\n \nUse\n \nCase\n \nName:\n \nLogin\n \nPrimary\n \nActor:\n \nStaff,\n \nManager,\n \nDirector,\n \nCEO\n \n \nSecondary\n \nActors:\n \nSystem\n \nNone\n \nTrigger:\n \nA\n \nuser\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO)\n \ninitiates\n \nthe\n \nlogin\n \nprocess\n \nby\n \nnavigating\n \nto\n \nthe\n \nlogin\n \npage\n \nand\n \nentering\n \ntheir\n \ncredentials..\n \nDescription:\n \nThis\n \nuse\n \ncase\n \ndescribes\n \nthe\n \nprocess\n \nof\n \na\n \nregistered\n \nuser\n \nlogging\n \ninto\n \nthe\n \nRetailOnboardPro\n \nsystem\n \nusing\n \nvalid\n \ncredentials.\n \nThe\n \nsystem\n \nverifies\n \nthe\n \ncredentials\n \nand\n \ngrants\n \naccess\n \nto\n \nthe\n \nappropriate\n \ndashboard\n \nbased\n \non\n \nthe\n \nuser's\n \nrole.\n \n.\n \nPreconditions:\n \n-\n \nThe\n \nuser\n \nmust\n \nbe\n \na\n \nregistered\n \nand\n \nactive\n \nsystem\n \nuser\n \nwith\n \na\n \ndefined\n \nrole.\n \n \n-\n \nThe\n \nsystem\n \nmust\n \nbe\n \nonline\n \nand\n \nable\n \nto\n \nprocess\n \nauthentication\n \nrequests.\n \n \n-\n \nThe\n \nuser\n \nmust\n \nhave\n \nvalid\n \nlogin\n \ncredentials\n \n(username/email\n \nand\n \npassword).\n \n \nPostconditions:\n \n-\n \n**Success:**\n \nThe\n \nuser\n \nis\n \nauthenticated,\n \ntheir\n \nsession\n \nis\n \ninitiated,\n \nand\n \nthey\n \nare\n \nredirected\n \nto\n \ntheir\n \nrespective\n \ndashboard\n \n(Staff,\n \nManager,\n \nDirector,\n \nor\n \nCEO).\n \n \n-\n \n**Failure:**\n \nIf\n \nauthentication\n \nfails,\n \nthe\n \nsystem\n \nlogs\n \nthe\n \nfailed\n \nattempt,\n \nnotifies\n \nthe\n \nuser,\n \nand\n \nthe\n \nuser\n \nremains\n \non\n \nthe\n \nlogin\n \npage.\n \nAccess\n \nis\n \ndenied.\n \n \nExpected\n \nResults\n\nNormal\n \nFlow\n \n1.\n \nUser\n \nnavigates\n \nto\n \nthe\n \nLoginPage.\n \n \n2.\n \nUser\n \nenters\n \ntheir\n \nusername\n \n(or\n \nemail)\n \nand\n \npassword\n \ninto\n \nthe\n \ndesignated\n \nfields.\n \n \n3.\n \nUser\n \nclicks\n \nthe\n \n'Login'\n \nbutton.\n \n \n4.\n \nSystem\n \nvalidates\n \nthe\n \nformat\n \nof\n \nthe\n \nentered\n \ncredentials\n \n(e.g.,\n \nnon-empty).\n \n \n5.\n \nSystem\n \nsecurely\n \nverifies\n \nthe\n \nsubmitted\n \ncredentials\n \nagainst\n \nthe\n \n`users`\n \ntable\n \nin\n \nthe\n \ndatabase.\n \n<br>\n \n6.\n \nIf\n \ncredentials\n \nare\n \nvalid:\n \n \n \na.\n \nSystem\n \nestablishes\n \nan\n \nauthenticated\n \nsession\n \nfor\n \nthe\n \nuser.\n \n \n \nb.\n \nSystem\n \nlogs\n \nthe\n \nsuccessful\n \nlogin\n \nattempt,\n \nincluding\n \nuser\n \nID\n \nand\n \ntimestamp.\n \n \n \nc.\n \nSystem\n \nredirects\n \nthe\n \nuser\n \nto\n \ntheir\n \nrole-specific\n \ndashboard.\n \n \nAlternative\n \nFlows:\n \n**AF1:\n \nInvalid\n \nCredentials**\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \ndo\n \nnot\n \nmatch\n \na\n \nregistered\n \nand\n \nactive\n \nuser:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nlogin\n \nattempt\n \n(e.g.,\n \nusername,\n \nIP\n \naddress,\n \ntimestamp).\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \non\n \nthe\n \nLoginPage\n \n(e.g.,\n \n\"Invalid\n \nusername\n \nor\n \npassword.\n \nPlease\n \ntry\n \nagain.\").\n \n \nc.\n \nUser\n \nremains\n \non\n \nthe\n \nLoginPage\n \n(returns\n \nto\n \nstep\n \n2).\n \n \n**AF2:\n \nAccount\n \nLocked\n \n(Optional\n \n-\n \nif\n \nimplemented)**\n \n-\n \nIf\n \nthe\n \nsystem\n \nimplements\n \naccount\n \nlocking\n \nafter\n \nmultiple\n \nfailed\n \nattempts:\n \n \n-\n \nAt\n \nstep\n \n5,\n \nif\n \ncredentials\n \nare\n \nvalid\n \nbut\n \nthe\n \naccount\n \nis\n \nlocked:\n \n \na.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Your\n \naccount\n \nis\n \ntemporarily\n \nlocked.\n \nPlease\n \ntry\n \nagain\n \nlater\n \nor\n \ncontact\n \nsupport.\").\n \n \n-\n \nIf\n \ncredentials\n \nare\n \ninvalid\n \nand\n \nthis\n \nattempt\n \ntriggers\n \nan\n \naccount\n \nlock:\n \n \na.\n \nSystem\n \nlogs\n \nthe\n \nfailed\n \nattempt\n \nand\n \nlocks\n \nthe\n \naccount.\n \n \nb.\n \nSystem\n \ndisplays\n \nan\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Invalid\n \ncredentials.\n \nYour\n \naccount\n \nhas\n \nbeen\n \ntemporarily\n \nlocked\n \ndue\n \nto\n \nmultiple\n \nfailed\n \nattempts.\").\n \n \nExceptions:\n \n \n**E1:\n \nSystem\n \nUnavailable**\n \n \n-\n \nIf\n \nthe\n \nauthentication\n \nservice\n \nor\n \ndatabase\n \nis\n \nunavailable\n \nat\n \nstep\n \n5:\n \n<br>\n        \na.\n \nSystem\n \nlogs\n \nthe\n \ncritical\n \nerror.\n \n \nb.\n \nSystem\n \ndisplays\n \na\n \ngeneric\n \nerror\n \nmessage\n \n(e.g.,\n \n\"Login\n \nservice\n \nis\n \ntemporarily\n \nunavailable.\n \nPlease\n \ntry\n \nagain\n \nlater.\").\n \n \nPriority:\n \nHigh\n \nFrequency\n \nof\n \nUse:\n \n \nMultiple\n \ntimes\n \nper\n \nday\n \nper\n \nuser.\n \n \nBusiness\n \nRules:\n \nBR-LOGIN-1,\n \nBR-LOGIN-2\n \n \nUse\n \ncase\n \nrelated\n \nUC-TR001,\n \nUC-SR002,\n \nUC-MR001,\n \nUC-MR002,\n \nUC-DR001,\n \nUC-DR002,\n \nUC-CR001,\n \nUC-CR002,\n \nUC_2\n \n(Manage\n \nUser\n \nProfile)\n \n \nScreen\n \nrelated\n \nLoginPage,\n \nStaffDashboardPage,\n \nManagerDashboardPage,\n \nDirectorDashboardPage,\n \nCEODashboardPage\n\nAssumptions:\n \n \n-\n \nPassword\n \nencryption\n \nand\n \nsecurity\n \npolicies\n \n(e.g.,\n \ncomplexity,\n \nexpiry)\n \nare\n \nenforced\n \nby\n \nthe\n \nsystem\n \nduring\n \nregistration\n \nand\n \nlogin.\n \n \n-\n \nSession\n \nmanagement\n \n(creation,\n \ntimeout,\n \ntermination)\n \nis\n \nhandled\n \nsecurely.\n \n \n \n \nb.\n \nBusiness", "match_type": "exact", "matched_with": "2.1 Use Case: Login System"}, {"heading_number": "1.1", "title": "1.1 Screen: Homepage", "Content": "1.1\n \nScreen:\n \nHomepage\n \n \n{\n \n  \n\"screenName\":\n \n\"Homepage\"\n,\n\n\"title\"\n:\n \n\"RetailOnboardPro\n \n-\n \nH\nệ\n \nth\nố\nng\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"SingleColumnWithHeaderFooter\"\n,\n \n    \n\"header\"\n:\n \n{\n \n      \n\"logo\"\n:\n \n\"RetailOnboardPro\"\n,\n \n      \n\"navLinks\"\n:\n \n[\n\"T<PERSON>h\n \nnăng\"\n,\n \n\"Quy\n \ntrình\"\n,\n \n\"V\nề\n \nchúng\n \ntôi\"\n,\n \n\"H\nỗ\n \ntr\nợ\n\"\n],\n \n      \n\"actions\"\n:\n \n[\n \n        \n{\n\"id\"\n:\n \n\"loginButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n},\n \n        \n{\"id\":\n \n\"registerButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n \n      \n]\n \n    \n},\n \n    \n\"mainContent\":\n \n{\n \n      \n\"heroSection\"\n:\n \n{\n \n        \n\"title\"\n:\n \n\"H\nệ\n \nth\nố\nng\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n        \n\"subtitle\"\n:\n \n\"Qu\nả\nn\n \nlý\n \nvà\n \nphê\n \nduy\nệ\nt\n \nyêu\n \nc\n<PERSON>\nu\n \nđào\n \nt\nạ\no\n \nnhân\n \nviên\n \nm\nộ\nt\n \ncách\n \nhi\nệ\nu\n \nqu\nả\n...\"\n,\n \n        \n\"ctaButtons\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"heroLoginButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n},\n \n          \n{\"id\":\n \n\"heroRegisterButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n \n        \n],\n \n        \n\"image\":\n \n\"decorative_image_of_flowchart\"\n \n      \n},\n \n      \n\"featuresSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"Tính\n \nnăng\n \nchính\"\n,\n \n        \n\"items\"\n:\n \n[\n \n          \n{\n\"icon\"\n:\n \n\"approval_process_icon\"\n,\n \n\"title\"\n:\n \n\"Quy\n \ntrình\n \nphê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"request_management_icon\"\n,\n \n\"title\"\n:\n \n\"Qu\nả\nn\n \nlý\n \nyêu\n \nc\nầ\nu\n \nđào\n \nt\nạ\no\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"role_upgrade_icon\"\n,\n \n\"title\"\n:\n \n\"Yêu\n \nc\nầ\nu\n \nnâng\n \nc\nấ\np\n \nvai\n \ntrò\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n\n{\"\nicon\n\":\n \n\"permissions_icon\"\n,\n \n\"title\"\n:\n \n\"Phân\n \nquy\nề\nn\n \nrõ\n \nràng\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"dashboard_icon\"\n,\n \n\"title\"\n:\n \n\"Dashboard\n \nth\nố\nng\n \nkê\"\n,\n \n\"description\"\n:\n \n\"...\"\n},\n \n          \n{\"\nicon\n\":\n \n\"history_icon\"\n,\n \n\"title\"\n:\n \n\"L\nị\nch\n \ns\nử\n \nphê\n \nduy\nệ\nt\"\n,\n \n\"description\"\n:\n \n\"...\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"workflowSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"Quy\n \ntrình\n \nlàm\n \nvi\nệ\nc\"\n,\n \n        \n\"steps\"\n:\n \n[\n \n          \n{\n\"stepNumber\"\n:\n \n1\n,\n \n\"title\"\n:\n \n\"T\nạ\no\n \nyêu\n \nc\nầ\nu\"\n,\n \n\"actor\"\n:\n \n\"Nhân\n \nviên\n \n(Staff)\"\n},\n \n          \n{\"stepNumber\":\n \n2\n,\n \n\"title\"\n:\n \n\"Manager\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"Qu\nả\nn\n \nlý\n \n(Manager)\"\n},\n \n          \n{\"stepNumber\":\n \n3\n,\n \n\"title\"\n:\n \n\"Director\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"Giám\n \nđ\nố\nc\n \n(Director)\"\n},\n \n          \n{\"stepNumber\":\n \n4\n,\n \n\"title\"\n:\n \n\"CEO\n \nduy\nệ\nt\"\n,\n \n\"actor\"\n:\n \n\"CEO\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"aboutSection\":\n \n{\n \n        \n\"title\"\n:\n \n\"V\nề\n \nRetailOnboardPro\"\n,\n \n        \n\"text\"\n:\n \n\"Chúng\n \ntôi\n \nhi\nể\nu\n \nr\nằ\nng\n \nvi\nệ\nc\n \nqu\nả\nn\n \nlý\n \nvà\n \nphê\n \nduy\nệ\nt...\"\n \n      \n}\n \n    \n},\n \n    \n\"\nfooter\n\":\n \n{\n \n      \n\"logo\"\n:\n \n\"RetailOnboardPro\"\n,\n \n      \n\"links\"\n:\n \n{\n \n        \n\"features\"\n:\n \n[\n\"Qu\nả\nn\n \nlý\n \nyêu\n \nc\nầ\nu\"\n,\n \n\"Phê\n \nduy\nệ\nt\n \nnhi\nề\nu\n \nc\nấ\np\"\n,\n \n\"Thông\n \nkê\n \nbáo\n \ncáo\"\n],\n \n        \n\"support\"\n:\n \n[\n\"H\nướ\nng\n \nd\nẫ\nn\n \ns\nử\n \nd\nụ\nng\"\n,\n \n\"FAQ\"\n,\n \n\"Liên\n \nh\nệ\n\"\n]\n \n      \n},\n \n      \n\"copyright\":\n \n\"©\n \n2024\n \nRetailOnboardPro.\n \nAll\n \nrights\n \nreserved.\"\n,\n \n      \n\"cta\"\n:\n \n{\n\"id\"\n:\n \n\"footerRegisterButton\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n}\n\n}\n \n  \n}\n \n}\n \n \n \nElement", "match_type": "exact", "matched_with": "1.1 Screen: Homepage"}, {"heading_number": "1.2", "title": "1.2 Screen: LoginPage & RegisterPage", "Content": "Selector\n \nPurpose\n \nDescription\n \nWelcome\n \nHeading\n \nh1\n \nWelcomes\n \nthe\n \nuser\n \nto\n \nthe\n \nplatform.\n \n\"Welcome\n \nto\n \nRetailOnboardPro\"\n \nGo\n \nto\n \nDashboard\n \nButton\n \na\n \n(href=\"/dashboar\nd\")\n \nProvides\n \na\n \nquick\n \nlink\n \nto\n \nthe\n \nmain\n \ndashboard.\n \nStyled\n \nas\n \na\n \nprominent\n \ncall-to-action\n \nbutton.\n \nFeature\n \nCard\n \n(Training\n \nRequests)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nTraining\n \nRequests\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nFeature\n \nCard\n \n(Role\n \nManagement)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nRole\n \nManagement\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nFeature\n \nCard\n \n(User\n \nAdmin)\n \ndiv.bg-white.p-6.r\nounded-lg\n \nHighlights\n \nthe\n \nUser\n \nAdministration\n \nfeature.\n \nContains\n \nan\n \nicon,\n \ntitle,\n \nand\n \nbrief\n \ndescription.\n \nMain\n \nFooter\n \nfooter.bg-gray-80\n0\n \nDisplays\n \ncopyright\n \nand\n \nsecondary\n \nlinks.\n \nContains\n \ncopyright\n \ninfo,\n \nlinks\n \nto\n \nPrivacy\n \nPolicy,\n \nTerms\n \nof\n \nService,\n \nContact.\n \n \n1.2\n \nScreen:\n \nLoginPage\n \n&\n \nRegisterPage\n \nThis\n \nscreen\n \nallows\n \nusers\n \nto\n \nbe\n \nauthenticated\n \nto\n \nthe\n \nsystem\n \nscreens/functionalities.\n \nRelated\n \nuse\n \ncases:\n \n· \n \n \n \n \n \n \n \nUI\n \nDesign\n\n{\n \n  \n\"screenName\"\n:\n \n\"LoginPage\"\n,\n \n  \n\"title\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"CenteredCard\"\n \n  \n},\n \n  \n\"components\"\n:\n \n[\n \n    \n{\n \n      \n\"type\"\n:\n \n\"Card\"\n,\n \n      \n\"id\"\n:\n \n\"loginCard\"\n,\n \n      \n\"header\"\n:\n \n{\n \n        \n\"icon\"\n:\n \n\"app_logo_icon\"\n,\n \n        \n\"title\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n\n\"subtitle\"\n:\n \n\"Chào\n \nm\nừ\nng\n \ntr\nở\n \nl\nạ\ni\n \nv\nớ\ni\n \nRetailOnboardPro\"\n \n      \n},\n \n      \n\"form\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"loginForm\"\n,\n \n        \n\"action\"\n:\n \n\"/login\"\n,\n \n        \n\"method\"\n:\n \n\"POST\"\n,\n \n        \n\"fields\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"username\"\n,\n \n\"label\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\"\n,\n \n\"placeholder\"\n:\n \n\"Nh\nậ\np\n \ntên\n \nđăng\n \nnh\nậ\np\"\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"password\"\n,\n \n\"label\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\"\n,\n \n\"placeholder\"\n:\n \n\"Nh\nậ\np\n \nm\nậ\nt\n \nkh\nẩ\nu\"\n},\n \n          \n{\n\"type\"\n:\n \n\"Checkbox\"\n,\n \n\"id\"\n:\n \n\"rememberMe\"\n,\n \n\"label\"\n:\n \n\"Ghi\n \nnh\nớ\n \nđăng\n \nnh\nậ\np\"\n}\n \n        \n],\n \n        \n\"actions\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"SubmitButton\"\n,\n \n\"id\"\n:\n \n\"submitLogin\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nnh\nậ\np\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC002_UserLogin\"\n}\n \n        \n],\n \n        \n\"links\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"registerLink\"\n,\n \n\"text\"\n:\n \n\"Ch\nư\na\n \ncó\n \ntài\n \nkho\nả\nn?\n \nĐăng\n \nký\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"RegistrationPage\"\n},\n \n          \n{\n\"id\"\n:\n \n\"forgotPasswordLink\"\n,\n \n\"text\"\n:\n \n\"(Forgot\n \nPassword?)\"\n,\n \n\"navigatesTo\"\n:\n \n\"ResetPasswordScreen\"\n}\n \n//\n \nAssuming\n \n        \n]\n \n      \n},\n \n      \n\"footerLink\"\n:\n \n{\n\"id\"\n:\n \n\"backToHomeLink\"\n,\n \n\"text\"\n:\n \n\"←\n \nV\nề\n \ntrang\n \nch\nủ\n\"\n,\n \n\"navigatesTo\"\n:\n \n\"Homepage\"\n}\n \n    \n},\n \n    \n{\n \n      \n\"type\"\n:\n \n\"ErrorMessage\"\n,\n \n\"id\"\n:\n \n\"loginError\"\n,\n \n\"condition\"\n:\n \n\"loginFailed\"\n,\n \n\"text\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\n \nho\nặ\nc\n \nm\nậ\nt\n \nkh\nẩ\nu\n \nkhông\n \nđúng!\"\n \n    \n}\n \n  \n]\n \n}\n \nScreen\n \n1:\n \nScreen\n \nLogin\n\nTable\n \n17Element\n \ndescription\n \nof\n \nthe\n \nLoginPage\n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nLogin\n \nCard\n \nContainer\n \ndiv.login-ca\nrd\n \nMain\n \ncontainer\n \nfor\n \nlogin\n \nform\n \nand\n \nrelated\n \nelements\n \nStyled\n \ncard\n \nfor\n \nfocused\n \nlogin\n \nexperience\n \nLogin\n \nError\n \nMessage\n \ndiv\n \n(th:if=\"${pa\nram.error}\")\n \nDisplays\n \nerror\n \nif\n \nlogin\n \nfails\n \nMessage:\n \n\"Tên\n \nđăng\n \nnhập\n \nhoặc\n \nmật\n \nkhẩu\n \nkhông\n \nđúng!\"\n \nLogin\n \nForm\n \n(Dedicated\n \nPage)\n \nform\n \n(action=\"@\n{/login}\")\n \nAllows\n \nexisting\n \nusers\n \nto\n \nlog\n \nin\n \nFields:\n \nUsername,\n \nPassword,\n \nRemember\n \nme;\n \nlike\n \nindex.html\n \nform\n \nRegister\n \nLink\n \n(Login\n \nPage)\n \na\n \n(th:href=\"@\n{/register}\")\n \nNavigates\n \nto\n \nregistration\n \npage\n \nText:\n \n\"Đăng\n \nký\n \nngay\"\n \n \n \n.Screen\n \n2:\n \nRegister\n\n{\n \n  \n\"screenName\"\n:\n \n\"RegistrationPage\"\n,\n \n  \n\"title\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n  \n\"layout\"\n:\n \n{\n \n    \n\"type\"\n:\n \n\"CenteredCard\"\n \n  \n},\n \n  \n\"components\"\n:\n \n[\n \n    \n{\n \n      \n\"type\"\n:\n \n\"Card\"\n,\n \n      \n\"id\"\n:\n \n\"registerCard\"\n,\n \n      \n\"header\"\n:\n \n{\n \n        \n\"icon\"\n:\n \n\"add_user_icon\"\n,\n \n        \n\"title\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n        \n\"subtitle\"\n:\n \n\"T\nạ\no\n \ntài\n \nkho\nả\nn\n \nm\nớ\ni\n \nv\nớ\ni\n \nquy\nề\nn\n \nSTAFF\"\n\n},\n \n      \n\"form\"\n:\n \n{\n \n        \n\"id\"\n:\n \n\"registerForm\"\n,\n \n        \n\"action\"\n:\n \n\"/register\"\n,\n \n        \n\"method\"\n:\n \n\"POST\"\n,\n \n        \n\"fields\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"fullName\"\n,\n \n\"label\"\n:\n \n\"H\nọ\n \nvà\n \ntên\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputText\"\n,\n \n\"id\"\n:\n \n\"username\"\n,\n \n\"label\"\n:\n \n\"Tên\n \nđăng\n \nnh\nậ\np\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputEmail\"\n,\n \n\"id\"\n:\n \n\"email\"\n,\n \n\"label\"\n:\n \n\"Email\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"password\"\n,\n \n\"label\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n,\n \n\"helperText\"\n:\n \n\"M\nậ\nt\n \nkh\nẩ\nu\n \nph\nả\ni\n \ncó\n \nít\n \nnh\nấ\nt\n \n8\n \nký\n \nt\nự\n\"\n},\n \n          \n{\n\"type\"\n:\n \n\"InputPassword\"\n,\n \n\"id\"\n:\n \n\"confirmPassword\"\n,\n \n\"label\"\n:\n \n\"Xác\n \nnh\nậ\nn\n \nm\nậ\nt\n \nkh\nẩ\nu\n \n*\"\n,\n \n\"required\"\n:\n \ntrue\n}\n \n        \n],\n \n        \n\"infoBox\"\n:\n \n{\n \n          \n\"text\"\n:\n \n\"L\nư\nu\n \ný:\n \nTài\n \nkho\nả\nn\n \nm\nớ\ni\n \ns\nẽ\n \nđ\nượ\nc\n \nt\nạ\no\n \nv\nớ\ni\n \nquy\nề\nn\n \nSTAFF.\n \nĐ\nể\n \nthay\n \nđ\nổ\ni\n \nquy\nề\nn\n \nh\nạ\nn,\n \nb\nạ\nn\n \nc\nầ\nn\n \ng\nử\ni\n \nyêu\n \nc\nầ\nu\n \nthay\n \nđ\nổ\ni\n \nvai\n \ntrò\n \nsau\n \nkhi\n \nđăng\n \nnh\nậ\np.\"\n \n        \n},\n \n        \n\"consent\"\n:\n \n{\n\"type\"\n:\n \n\"Checkbox\"\n,\n \n\"id\"\n:\n \n\"termsConsent\"\n,\n \n\"label\"\n:\n \n\"Tôi\n \nđ\nồ\nng\n \ný\n \nv\nớ\ni\n \nđi\nề\nu\n \nkho\nả\nn\n \ns\nử\n \nd\nụ\nng\n \nvà\n \nchính\n \nsách\n \nb\nả\no\n \nm\nậ\nt\"\n,\n \n\"required\"\n:\n \ntrue\n},\n \n        \n\"actions\"\n:\n \n[\n \n          \n{\n\"type\"\n:\n \n\"SubmitButton\"\n,\n \n\"id\"\n:\n \n\"submitRegistration\"\n,\n \n\"text\"\n:\n \n\"Đăng\n \nký\n \ntài\n \nkho\nả\nn\"\n,\n \n\"triggersUseCase\"\n:\n \n\"UC001_RegisterNewAccount\"\n}\n \n        \n],\n \n        \n\"links\"\n:\n \n[\n \n          \n{\n\"id\"\n:\n \n\"loginLink\"\n,\n \n\"text\"\n:\n \n\"Đã\n \ncó\n \ntài\n \nkho\nả\nn?\n \nĐăng\n \nnh\nậ\np\n \nngay\"\n,\n \n\"navigatesTo\"\n:\n \n\"LoginPage\"\n}\n \n        \n]\n \n      \n},\n \n      \n\"footerLink\"\n:\n \n{\n\"id\"\n:\n \n\"backToHomeLink\"\n,\n \n\"text\"\n:\n \n\"←\n \nV\nề\n \ntrang\n \nch\nủ\n\"\n,\n \n\"navigatesTo\"\n:\n \n\"Homepage\"\n}\n \n    \n}\n \n  \n]\n\n}\n \n \nElement\n \nName\n \nHTML\n \nTag\n \n/\n \nSelector\n \nPurpose\n \nDescription\n \nRegister\n \nCard\n \nContainer\n \ndiv.", "match_type": "exact", "matched_with": "1.2 Screen: LoginPage & RegisterPage"}]