import os
import json
import re
import logging
from typing import List, Optional, Dict, Any
from PyPDF2 import PdfReader
from PyPDF2.errors import PdfReadError
import google.generativeai as genai
from google import genai as google_genai
from google.genai import types
from rotate_api_key import APIKeyRotator
from prompt_storing import extract_keyword_prompt
from count_gemini_token import GeminiCostCalculator
from billing_logger import billing_logger

# Cấu hình logging - Keep basic config but WebSocket handler will capture these logs
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")  # Capture all log levels

class DocumentProcessor:
    def __init__(
        self,
        pdf_path: str,
        manual_sections: Optional[List[Dict[str, Any]]] = None,
        config_file: str = None,
        output_dir: str = None,
        merged_json: str = None,
        analyzed_json: str = None,
        use_auto_toc: bool = True,
        count_token: bool = False,
        enable_caching: bool = True
    ) -> None:
        """
        Khởi tạo với các đường dẫn và cấu hình.

        Args:
            pdf_path: Đường dẫn đến file PDF
            manual_sections: Danh sách sections thủ công (optional, sẽ bị bỏ qua nếu use_auto_toc=True)
            config_file: Đường dẫn file config Gemini
            output_dir: Thư mục output
            merged_json: Đường dẫn file JSON merged
            analyzed_json: Đường dẫn file JSON analyzed
            use_auto_toc: Nếu True, tự động sinh TOC từ PDF outline thay vì dùng manual_sections
            count_token: Nếu True, tính toán chi phí token cho các lần gọi LLM
            enable_caching: Nếu True, tạo cache cho merged_output.json với display name "merged_output"
        """
        self.pdf_path = pdf_path
        self.use_auto_toc = use_auto_toc
        self.count_token = count_token
        self.enable_caching = enable_caching
        
        # Set default paths nếu không được cung cấp
        self.config_file = config_file
        self.output_dir = output_dir 
        self.merged_json = merged_json 
        self.analyzed_json = analyzed_json 
        
        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config

        # Initialize caching
        self.client = None  # Gemini client for caching
        self.cache = None   # Cache object
        self.cache_tokens = 0  # Track cache tokens
        self.cache_created = False  # Track if cache was created
        
        # Khởi tạo manual_sections
        if self.use_auto_toc:
            logging.info("Sử dụng chế độ tự động sinh TOC từ PDF outline.")
            self.manual_sections = self._generate_toc_from_pdf()
        else:
            self.manual_sections = manual_sections or []
            logging.info(f"Sử dụng manual_sections được cung cấp với {len(self.manual_sections)} mục.")
        
        # This will now store list of dicts with "heading_number", "title", "Content"
        self.extracted_sections: List[Dict[str, str]] = [] 
        
        os.makedirs(self.output_dir, exist_ok=True)
        logging.info("Output directory set to: %s", self.output_dir)

        # Load Gemini config only if config_file is provided and exists
        self.model = None
        if self.config_file and os.path.exists(self.config_file):
            self.config = self._load_config()
            if self.config:
                # Determine model type for token calculation
                if self.count_token:
                    self._determine_model_type()
                    
                rotator = APIKeyRotator()
                api_key = rotator.get_api_key()
                genai.configure(api_key=api_key)
                self.model = genai.GenerativeModel(
                    self.config.get("config_gemini_model", {}).get("model_name", "gemini-1.5-pro"),
                    generation_config={"response_mime_type": self.config.get("config_gemini_model", {}).get("response_mime_type", "application/json")}
                )

                # Initialize client for caching if caching is enabled
                if self.enable_caching:
                    self.client = google_genai.Client(api_key=api_key)
                    logging.info("Gemini client initialized for caching")
                    logging.info(f"🔧 Gemini client initialized for caching with API key: ...{api_key[-4:]}")

                rotator.rotate_api_key()
            else:
                logging.error("Failed to load Gemini configuration.")
        elif self.config_file:
            logging.warning(f"Config file not found: {self.config_file}. LLM features will be disabled.")
        else:
            logging.info("No config file provided. LLM features will be disabled.")

    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"
            
        if self.count_token:
            logging.info(f"Token counting enabled for model type: {self.model_type}")

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)

    def get_token_summary(self) -> Dict[str, Any]:
        """
        Trả về tổng kết chi phí token.

        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}

        total_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)

        # Calculate cache cost if cache was created
        cache_cost_info = {}
        if self.cache_created and self.cache_tokens > 0:
            if self.model_type == "flash":
                cache_cost_info = self.cost_calculator.calculate_flash_cache_cost(self.cache_tokens)
            else:
                cache_cost_info = self.cost_calculator.calculate_pro_cache_cost(self.cache_tokens)

        result = {
            "token_counting_enabled": True,
            "model_type": self.model_type,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            "cache_created": self.cache_created,
            "cache_tokens": self.cache_tokens,
            **total_cost_info
        }

        # Add cache cost information if available
        if cache_cost_info:
            result.update(cache_cost_info)

        return result

    def _generate_toc_from_pdf(self) -> List[Dict[str, Any]]:
        """
        Tự động sinh manual_sections từ PDF outline.
        
        Returns:
            List[Dict[str, Any]]: Danh sách manual_sections tương tự format cũ
        """
        def process_outline_item(item, reader, level=0):
            """Hàm đệ quy xử lý từng phần tử trong outline."""
            if isinstance(item, list):
                # Nếu là list, xử lý đệ quy từng phần tử trong list
                for subitem in item:
                    yield from process_outline_item(subitem, reader, level)
            else:
                # Nếu không phải list, giả định là đối tượng có title
                try:
                    title = item.title.strip()
                    page_num = reader.get_destination_page_number(item) + 1  # Trang bắt đầu từ 1
                    
                    # Tạo heading_number dựa trên cấu trúc title
                    heading_number = self._generate_heading_number(title, level)
                    
                    yield {
                        "title": title,
                        "search_title": title,
                        "heading_number": heading_number,
                        "page": page_num,
                        "level": level
                    }
                except AttributeError:
                    logging.warning(f"Phần tử trong outline không có thuộc tính 'title': {item}")
                except Exception as e:
                    logging.warning(f"Lỗi xử lý outline item: {e}")

        try:
            with open(self.pdf_path, "rb") as f:
                reader = PdfReader(f)
                if not reader.outline:
                    logging.warning(f"PDF '{self.pdf_path}' không có outline. Sử dụng fallback method.")
                    return self._generate_fallback_toc()

                toc_items = list(process_outline_item(reader.outline, reader))
                
                if not toc_items:
                    logging.warning("Không trích xuất được mục nào từ PDF outline. Sử dụng fallback method.")
                    return self._generate_fallback_toc()
                
                logging.info(f"Đã trích xuất {len(toc_items)} mục từ PDF outline:")
                for item in toc_items:
                    logging.info(f"  - '{item['title']}' (Trang {item['page']}, Level {item.get('level', 0)})")
                
                return toc_items

        except FileNotFoundError:
            logging.error(f"Lỗi: Không tìm thấy file PDF '{self.pdf_path}'.")
            return []
        except PdfReadError as e:
            logging.error(f"Lỗi khi đọc PDF '{self.pdf_path}': {e}")
            return []
        except Exception as e:
            logging.error(f"Lỗi không xác định khi xử lý PDF outline '{self.pdf_path}': {e}")
            return []

    def _generate_heading_number(self, title: str, level: int) -> str:
        """
        Tạo heading_number dựa trên title và level của outline.
        
        Args:
            title: Tiêu đề của mục
            level: Cấp độ trong outline hierarchy
            
        Returns:
            str: Heading number được tạo
        """
        # Thử trích xuất số từ đầu title
        number_match = re.match(r'^(\d+(?:\.\d+)*)', title.strip())
        if number_match:
            return number_match.group(1)
        
        # Thử trích xuất Roman numerals
        roman_match = re.match(r'^([IVX]+)\.?\s', title.strip())
        if roman_match:
            return roman_match.group(1)
        
        # Fallback dựa trên level và vị trí
        clean_title = re.sub(r'[^\w\s]', '', title).strip().replace(' ', '_')
        return f"section_{level}_{clean_title[:20]}"

    def _generate_fallback_toc(self) -> List[Dict[str, Any]]:
        """
        Phương pháp fallback khi PDF không có outline - tạo một mục cho toàn bộ document.
        
        Returns:
            List[Dict[str, Any]]: Danh sách chứa một mục duy nhất cho toàn bộ document
        """
        logging.info("Sử dụng fallback TOC - tạo mục cho toàn bộ document.")
        return [{
            "title": "Full Document Content",
            "search_title": "Full Document Content",
            "heading_number": "full_document",
            "page": 1,
            "level": 0
        }]

    def _load_config(self) -> Optional[dict]:
        try:
            with open(self.config_file, "r", encoding="utf-8") as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return None

    @staticmethod
    def _extract_text_from_pdf_pypdf2(pdf_path: str) -> Optional[Dict[int, str]]:
        pages_content = {}
        try:
            with open(pdf_path, "rb") as f:
                reader = PdfReader(f)
                num_pages = len(reader.pages)
                for page_num_idx in range(num_pages):
                    page = reader.pages[page_num_idx]
                    text = page.extract_text()
                    pages_content[page_num_idx + 1] = text if text else ""
            return pages_content
        except FileNotFoundError:
            logging.error(f"Lỗi: Không tìm thấy file PDF '{pdf_path}'.")
            return None
        except PdfReadError as e:
            logging.error(f"Lỗi khi đọc PDF '{pdf_path}' bằng PyPDF2: {e}.")
            return None
        except Exception as e:
            logging.error(f"Lỗi không xác định khi xử lý PDF '{pdf_path}': {e}")
            return None

    @staticmethod
    def _find_title_occurrence_on_page(page_text: str, title_to_find: str, search_offset: int = 0, 
                                     score_threshold: float = 85.0) -> int:
        """
        Tìm vị trí của title trên trang sử dụng fuzzy matching với RapidFuzz.
        
        Args:
            page_text: Nội dung text của trang
            title_to_find: Title cần tìm
            search_offset: Vị trí bắt đầu tìm kiếm
            score_threshold: Ngưỡng điểm fuzzy matching (0-100)
            
        Returns:
            int: Vị trí của title, -1 nếu không tìm thấy
        """
        # Chiến lược 1: Tìm chính xác trước
        exact_pos = page_text.find(title_to_find, search_offset)
        if exact_pos != -1:
            return exact_pos
        
        # Chiến lược 1.5: Thử variations phổ biến của spacing trước khi dùng fuzzy
        title_variations = [
            title_to_find.replace(' ', '  '),   # "3. System High Level Design" -> "3. System  High  Level  Design"  
            title_to_find.replace(' ', '   '),  # Triple spaces
            ' '.join(title_to_find.split()),    # Normalize spaces
            # Specific pattern for this case
            "3. System  High  Level  Design",   # Direct match for the exact pattern in PDF
        ]
        
        for variation in title_variations:
            exact_pos = page_text.find(variation, search_offset)
            if exact_pos != -1:
                return exact_pos
        
        # Chiến lược 2: Fuzzy matching với RapidFuzz
        try:
            from rapidfuzz import fuzz, process
            
            # Lấy phần text từ search_offset
            search_text = page_text[search_offset:]
            
            # Tách text thành các dòng để tìm kiếm
            lines = search_text.split('\n')
            
            # Tạo danh sách candidates từ các dòng và chunks
            candidates = []
            current_pos = search_offset
            
            for line in lines:
                line = line.strip()
                if line:  # Bỏ qua dòng trống
                    candidates.append((line, current_pos))
                    
                    # Tạo thêm candidates từ các phần của dòng
                    words = line.split()
                    if len(words) > 1:
                        # Tạo chunks với window size khác nhau
                        for window_size in [2, 3, 4, 5]:
                            for i in range(len(words) - window_size + 1):
                                chunk = ' '.join(words[i:i+window_size])
                                chunk_pos = current_pos + line.find(chunk)
                                if chunk_pos >= current_pos:  # Đảm bảo position hợp lệ
                                    candidates.append((chunk, chunk_pos))
                
                current_pos += len(line) + 1  # +1 cho ký tự newline
            
            if not candidates:
                return -1
            
            # Sử dụng RapidFuzz để tìm match tốt nhất
            texts, positions = zip(*candidates)
            
            # Thử nhiều phương pháp scoring để tìm match tốt nhất
            scoring_methods = [
                fuzz.ratio,           # Tỷ lệ tương đồng cơ bản
                fuzz.partial_ratio,   # Tỷ lệ tương đồng partial
                fuzz.token_sort_ratio,# Tỷ lệ sau khi sort tokens
                fuzz.token_set_ratio  # Tỷ lệ dựa trên set of tokens
            ]
            
            best_match = None
            best_score = 0
            best_pos = -1
            
            for method in scoring_methods:
                try:
                    result = process.extractOne(
                        title_to_find, 
                        texts, 
                        scorer=method,
                        score_cutoff=score_threshold
                    )
                    
                    if result and result[1] > best_score:
                        best_score = result[1]
                        best_match = result[0]
                        # Tìm position tương ứng
                        for text, pos in candidates:
                            if text == best_match:
                                best_pos = pos
                                break
                
                except Exception as e:
                    # Nếu method này lỗi, thử method khác
                    continue
            
            if best_match and best_score >= score_threshold:
                logging.debug(f"Fuzzy match found: '{best_match}' (score: {best_score:.1f}) for title '{title_to_find}'")
                return best_pos
                
        except ImportError:
            logging.warning("RapidFuzz not available, falling back to basic search")
        except Exception as e:
            logging.debug(f"RapidFuzz search error: {e}, falling back to basic search")
        
        # Chiến lược 3: Fallback đơn giản nếu RapidFuzz không có hoặc lỗi
        # Thử tìm kiếm partial với các từ đầu tiên
        words = title_to_find.split()
        if len(words) >= 2:
            # Thử tìm 2-3 từ đầu tiên
            for word_count in [3, 2]:
                if len(words) >= word_count:
                    partial_title = ' '.join(words[:word_count])
                    pos = page_text.find(partial_title, search_offset)
                    if pos != -1:
                        logging.debug(f"Partial match found: '{partial_title}' for title '{title_to_find}'")
                        return pos
        
        return -1

    def _extract_sections_from_text_pages(
        self,
        text_pages_content: Dict[int, str],
        manual_sections_def: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        extracted_data = []
        num_sections = len(manual_sections_def)

        for i in range(num_sections):
            current_section = manual_sections_def[i]
            current_title_to_search = current_section.get("search_title", current_section["title"])
            current_display_title = current_section.get("display_title", current_section["title"])
            current_heading_num = current_section.get("heading_number", f"section_{i+1}")
            current_start_page_num = current_section["page"]

            section_content_parts = []

            effective_next_section_start_page = float('inf')
            title_of_very_next_section = None
            next_section_start_page_for_iterator = float('inf')

            if i + 1 < num_sections:
                next_section_definition = manual_sections_def[i+1]
                effective_next_section_start_page = next_section_definition["page"]
                title_of_very_next_section = next_section_definition.get("search_title", next_section_definition["title"])
                next_section_start_page_for_iterator = effective_next_section_start_page

            page_iterator_end_val = int(next_section_start_page_for_iterator) \
                if next_section_start_page_for_iterator != float('inf') \
                else (max(text_pages_content.keys(), default=current_start_page_num) + 1 if text_pages_content else current_start_page_num + 1)
            
            for page_num in range(current_start_page_num, page_iterator_end_val +1 ):
                if page_num > next_section_start_page_for_iterator and next_section_start_page_for_iterator != float('inf'):
                    break
                
                if page_num not in text_pages_content or not text_pages_content[page_num]:
                    logging.debug(f"Trang {page_num} không có nội dung hoặc không tồn tại, bỏ qua cho mục '{current_display_title}'.")
                    continue
                
                page_text = text_pages_content[page_num]
                
                start_slice_index = 0
                end_slice_index = len(page_text)
                title_pos_on_start_page = -1

                if page_num == current_start_page_num:
                    title_pos_on_start_page = self._find_title_occurrence_on_page(page_text, current_title_to_search)
                    if title_pos_on_start_page != -1:
                        start_slice_index = title_pos_on_start_page + len(current_title_to_search)
                        
                        # Tìm đến cuối dòng chứa title để tránh lấy phần còn lại của title
                        title_line_end = page_text.find('\n', start_slice_index)
                        if title_line_end != -1:
                            start_slice_index = title_line_end + 1
                        else:
                            # Nếu không có newline, có thể đây là dòng cuối
                            start_slice_index = len(page_text)
                    else:
                        # Nếu không tìm thấy title chính xác, thử tìm heading number
                        heading_pos = self._find_title_occurrence_on_page(page_text, current_heading_num)
                        if heading_pos != -1:
                            start_slice_index = heading_pos
                            logging.info(f"Tìm thấy heading number '{current_heading_num}' thay vì title '{current_title_to_search}' trên trang {page_num}")
                        else:
                            # Nếu vẫn không tìm thấy, log warning và để start_slice_index = 0
                            logging.warning(f"Không tìm thấy title '{current_title_to_search}' hoặc heading '{current_heading_num}' trên trang {page_num}. Sẽ lấy từ đầu trang.")
                            start_slice_index = 0
                
                next_title_pos_on_boundary_page = -1
                if page_num == effective_next_section_start_page and title_of_very_next_section:
                    search_offset_for_next_title = start_slice_index if page_num == current_start_page_num else 0
                    next_title_pos_on_boundary_page = self._find_title_occurrence_on_page(page_text, title_of_very_next_section, search_offset_for_next_title)
                    
                    if next_title_pos_on_boundary_page != -1:
                        if page_num == current_start_page_num and next_title_pos_on_boundary_page < start_slice_index:
                            logging.warning(f"Cảnh báo: Tiêu đề tiếp theo '{title_of_very_next_section}' (trang {page_num}) được tìm thấy trước khi nội dung của mục hiện tại '{current_title_to_search}' bắt đầu. Kiểm tra manual_sections.")
                        else:
                            end_slice_index = next_title_pos_on_boundary_page
                
                extracted_page_content = page_text[start_slice_index:end_slice_index].strip()
                
                if extracted_page_content:
                    section_content_parts.append(extracted_page_content)

                if page_num == effective_next_section_start_page and title_of_very_next_section:
                     if next_title_pos_on_boundary_page != -1 and end_slice_index == next_title_pos_on_boundary_page: 
                        break 
        
            full_content = "\n\n".join(s for s in section_content_parts if s).strip()
            
            extracted_data.append({
                "heading_number": current_heading_num,
                "title": current_display_title,
                "Content": full_content # Key is "Content" with capital 'C'
            })
            if not full_content:
                 logging.warning(f"Mục '{current_display_title}' (số trang {current_start_page_num}) không trích xuất được nội dung.")

        return extracted_data

    def process_pdf(self) -> None:
        logging.info(f"Bắt đầu xử lý PDF: {self.pdf_path} bằng PyPDF2.")
        pdf_pages_content = self._extract_text_from_pdf_pypdf2(self.pdf_path)

        if not pdf_pages_content:
            logging.error(f"Không thể trích xuất nội dung từ PDF: {self.pdf_path}. Dừng xử lý.")
            self.extracted_sections = []
            return

        logging.info(f"Đã trích xuất được {len(pdf_pages_content)} trang từ PDF.")
        
        if not self.manual_sections:
            logging.warning("Không có manual_sections được cung cấp hoặc sinh ra. Sẽ không có mục nào được trích xuất theo cấu trúc.")
            all_text = "\n\n".join(pdf_pages_content.get(p, "") for p in sorted(pdf_pages_content.keys()))
            if all_text.strip():
                 self.extracted_sections.append({
                    "heading_number": "full_document",
                    "title": "Full Document Content",
                    "Content": all_text.strip()
                })
                 logging.info("Đã gộp toàn bộ nội dung PDF thành một mục do không có manual_sections.")
            else:
                logging.info("PDF không có nội dung hoặc manual_sections trống, không có mục nào được tạo.")
            return

        logging.info(f"Đang xử lý các mục dựa trên {len(self.manual_sections)} định nghĩa trong manual_sections...")
        raw_extracted_sections = self._extract_sections_from_text_pages(pdf_pages_content, self.manual_sections)
        
        self.extracted_sections = [] # Reset before populating
        for i, section_data in enumerate(raw_extracted_sections):
            # Defensive handling: ensure section_data is a dictionary
            processed_section = None

            if isinstance(section_data, list) and len(section_data) > 0:
                # If it's a list, log a warning and take the first element
                # This handles cases where the processing unexpectedly returns a list with one item
                logging.warning(f"Section {i} received a list instead of a dict, taking the first element. List: {section_data}")
                processed_section = section_data[0]
            elif isinstance(section_data, dict):
                # If it's a dict, use it directly. This is the expected path.
                processed_section = section_data
            else:
                # If it's something else or empty, raise a clear error
                logging.error(f"Section {i} has unexpected data type: {type(section_data)}. Expected a dict or a non-empty list. Data: {section_data}")
                continue  # Skip this section instead of crashing

            # Additional validation: ensure processed_section is a dictionary
            if not isinstance(processed_section, dict):
                logging.error(f"Section {i} processed data is not a dictionary. Type: {type(processed_section)}. Skipping.")
                continue

            # Now safely use the processed_section dictionary
            self.extracted_sections.append(processed_section)
            title = processed_section.get('title', 'Unknown')
            heading = processed_section.get('heading_number', 'Unknown')
            content = processed_section.get('Content', '')
            logging.info(f"Đã xử lý mục: '{title}' (Heading: {heading}), nội dung dài {len(content)} ký tự.")
        
        if not self.extracted_sections and self.manual_sections:
            logging.warning("Quá trình xử lý PDF hoàn tất nhưng không có mục nào được trích xuất thành công dựa trên manual_sections.")
        elif self.extracted_sections:
            logging.info(f"✅ Hoàn tất trích xuất {len(self.extracted_sections)} mục từ PDF.")

    def merge_to_json(self) -> None:
        if not self.extracted_sections:
            logging.warning(f"Không có nội dung trích xuất để ghi vào file JSON '{self.merged_json}'. Bỏ qua.")
            return

        try:
            with open(self.merged_json, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_sections, f, indent=2, ensure_ascii=False)
            logging.info(f"Đã xuất kết quả thành công vào file: '{self.merged_json}'")

            # Create cache for merged_output.json if caching is enabled
            if self.enable_caching:
                logging.info("🔄 Checking cache for merged_output.json...")
                content_for_cache = json.dumps(self.extracted_sections, ensure_ascii=False, indent=2)
                cache_success = self._create_cache(content_for_cache)
                if cache_success:
                    if self.cache_created:
                        logging.info("✅ New cache created successfully for merged_output.json")
                    else:
                        logging.info("✅ Using existing cache for merged_output.json")
                else:
                    logging.info("⚠️ Cache creation/retrieval failed, but file was saved successfully")
            else:
                logging.info("Caching disabled, skipping cache creation for merged_output.json")

        except IOError as e:
            logging.error(f"Lỗi IO khi ghi file JSON '{self.merged_json}': {e}")
        except Exception as e:
            logging.error(f"Lỗi không xác định khi ghi file JSON '{self.merged_json}': {e}")

    def analyze_json(self) -> Optional[List[dict]]:
        if not self.extracted_sections:
            logging.error("No extracted sections available for analysis.")
            return None

        if not self.model:
            logging.error("Gemini model is not initialized.")
            return None

        combined_input = json.dumps(self.extracted_sections, indent=2, ensure_ascii=False)
        
        if len(combined_input) > 3000000: 
            logging.warning(f"Input JSON size ({len(combined_input)} chars) is very large. May exceed Gemini token limits.")

        # MODIFIED PROMPT to reflect new input structure and adjust output keys
        prompt = f"""
            {extract_keyword_prompt}

            -----BEGIN Input JSON:-----
            {combined_input}
            -----END Input JSON:-----
        """
        try:
            # Count input tokens
            input_token_count = self.model.count_tokens(prompt)
            input_tokens = input_token_count.total_tokens

            logging.info(f"Total tokens to be sent: {input_token_count}")

            
            logging.info(f"Sending {len(self.extracted_sections)} sections to Gemini for analysis...")
            
            response = self.model.generate_content(prompt)
            logging.info("Received Gemini response.")
            
            # Count output tokens
            output_tokens = 0
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                output_tokens = response.usage_metadata.candidates_token_count
            else:
                # Fallback: estimate tokens based on response text
                output_token_count = self.model.count_tokens(response.text)
                output_tokens = output_token_count.total_tokens
            
            # Track tokens if enabled
            if self.count_token:
                self.total_input_tokens += input_tokens
                self.total_output_tokens += output_tokens
                
                # Calculate cost for this request
                cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                
                logging.info(f"Token usage - Input: {input_tokens:,}, Output: {output_tokens:,}")
                if cost_info:
                    logging.info(f"Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    logging.info(f"Cumulative cost: ${self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0):.6f}")
            
            response_text = response.text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[len("```json"):].strip()
            elif response_text.startswith("```"):
                 response_text = response_text[len("```"):].strip()

            if response_text.endswith("```"):
                response_text = response_text[:-len("```")].strip()

            try:
                results = json.loads(response_text)
            except json.JSONDecodeError as je:
                logging.error(f"Failed to decode Gemini JSON response: {je}")
                logging.error(f"Raw Gemini response text:\n{response.text[:1000]}...") 
                match = re.search(r"\[\s*\{.*\}\s*\]", response.text, re.DOTALL) 
                if not match: 
                     match = re.search(r"\{\s*\".*\"\s*:\s*.*\}", response.text, re.DOTALL)
                if match:
                    logging.info("Attempting to parse JSON from regex match.")
                    try:
                        results = json.loads(match.group(0).strip())
                    except json.JSONDecodeError as je2:
                        logging.error(f"Fallback JSON parsing also failed: {je2}")
                        return None
                else:
                    logging.error("Could not find a JSON structure in Gemini response using regex.")
                    return None

            # Defensive handling of LLM response structure
            if not isinstance(results, list) and self.extracted_sections:
                 logging.warning(f"Gemini response was valid JSON but not a list as expected. Type: {type(results)}. Attempting to wrap if it's a single dict for a single input section.")
                 if isinstance(results, dict) and len(self.extracted_sections) == 1:
                     results = [results]
                 else:
                     logging.error("Gemini response is not a list and cannot be straightforwardly converted.")
                     return None  # Fail fast instead of continuing with invalid data

            # Additional validation: ensure results is a valid list before proceeding
            if not isinstance(results, list):
                logging.error(f"Final results validation failed. Expected list, got {type(results)}. Cannot proceed.")
                return None

            # Validate each item in the list is a dictionary (expected structure)
            for i, item in enumerate(results):
                if not isinstance(item, dict):
                    logging.error(f"Results item {i} is not a dictionary. Type: {type(item)}. Expected dict with .get() method.")
                    return None

            with open(self.analyzed_json, "w", encoding="utf-8") as out:
                json.dump(results, out, indent=2, ensure_ascii=False)
            logging.info(f"Analysis saved to {self.analyzed_json}")
            return results
        except Exception as e: 
            logging.error(f"Error processing Gemini input or handling response: {e}")
            if hasattr(e, 'response') and e.response:
                 logging.error(f"Gemini API Error Response: {e.response}")
            return None

    def execute(self, verbose: bool = False, skip_llm: bool = False) -> None:
        """
        Thực hiện toàn bộ quy trình xử lý PDF.
        
        Args:
            verbose: In summary chi tiết về kết quả
            skip_llm: Bỏ qua phần tạo description bằng LLM
        """
        if verbose:
            logging.info(f"=== PROCESSING PDF: {self.pdf_path} ===")
            logging.info(f"Output directory: {self.output_dir}")

        if verbose:
            logging.info(f"\n=== TOC INFORMATION ===")
            self.print_toc_info()

        if verbose:
            logging.info(f"\n=== PROCESSING PDF ===")
        self.process_pdf()

        if verbose:
            logging.info(f"\n=== CREATING JSON OUTPUT ===")
        self.merge_to_json()

        if not skip_llm and self.extracted_sections and self.model:
            if verbose:
                logging.info(f"\n=== ANALYZING WITH LLM ===")
            self.analyze_json()
        elif not skip_llm and self.extracted_sections and not self.model:
            logging.info("Skipping LLM analysis because no model is configured.")
        elif not self.extracted_sections:
            logging.info("Skipping analysis because no sections were extracted.")

        if verbose:
            self.print_processing_summary()
            logging.info(f"\n=== PROCESSING COMPLETE ===")
            
        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()

    def print_toc_info(self) -> None:
        """In thông tin về TOC được tạo."""
        if not self.manual_sections:
            logging.info("Không có manual_sections nào được tạo.")
            return

        logging.info(f"\n=== THÔNG TIN TOC ===")
        logging.info(f"Chế độ: {'Tự động từ PDF outline' if self.use_auto_toc else 'Manual sections'}")
        logging.info(f"Tổng số mục: {len(self.manual_sections)}")

        if self.manual_sections:
            logging.info(f"Trang đầu tiên: {min(item['page'] for item in self.manual_sections)}")
            logging.info(f"Trang cuối cùng: {max(item['page'] for item in self.manual_sections)}")

            logging.info(f"\n=== CHI TIẾT CÁC MỤC ===")
            for i, item in enumerate(self.manual_sections, 1):
                level_info = f" (Level {item['level']})" if 'level' in item else ""
                logging.info(f"{i:2d}. '{item['title']}' - Trang {item['page']}{level_info}")

    def print_processing_summary(self) -> None:
        """In summary chi tiết về kết quả xử lý."""
        logging.info(f"\n=== PROCESSING SUMMARY ===")
        if self.extracted_sections:
            logging.info(f"✅ Successfully extracted {len(self.extracted_sections)} sections:")

            total_content_length = 0
            for i, section in enumerate(self.extracted_sections, 1):
                title = section.get('title', '')
                heading = section.get('heading_number', '')
                content = section.get('Content', '')
                content_len = len(content)
                total_content_length += content_len

                status = "📝" if content_len > 0 else "📄"
                logging.info(f"  {i:2d}. {status} [{heading}] '{title}' - {content_len:,} chars")

                # Special check for sections that should be empty
                if title in ["I. Overview", "2. Overall Structure", "3. System High Level Design"]:
                    if content_len == 0:
                        logging.info(f"      ✅ Correctly empty")
                    else:
                        logging.info(f"      ❌ Should be empty but has {content_len} chars")

            logging.info(f"\n📊 Total content: {total_content_length:,} characters")
            
            # Check output files
            if os.path.exists(self.merged_json):
                file_size = os.path.getsize(self.merged_json)
                logging.info(f"📁 Merged JSON: {self.merged_json} ({file_size:,} bytes)")

            if hasattr(self, 'analyzed_json') and os.path.exists(self.analyzed_json):
                file_size = os.path.getsize(self.analyzed_json)
                logging.info(f"📁 Analyzed JSON: {self.analyzed_json} ({file_size:,} bytes)")

        else:
            logging.info("❌ No sections extracted")

        logging.info(f"\n=== FILES LOCATION ===")
        logging.info(f"📂 Output directory: {self.output_dir}")

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost."""
        if not self.count_token or not self.cost_calculator:
            return

        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "DOCUMENT PROCESSOR")

        # Log billing information
        billing_logger.log_module_cost("DocumentProcessor", token_summary)

    def _find_cache_by_display_name(self, display_name: str):
        """
        Find cache by display name.

        Args:
            display_name: The display name of the cache to find

        Returns:
            Cache object if found, None otherwise
        """
        if not self.client:
            logging.warning(f"Client not initialized, cannot find cache with display name: {display_name}")
            logging.info(f"⚠️ Client not initialized, cannot search for cache: {display_name}")
            return None

        try:
            logging.info(f"🔍 Searching for existing cache with display name: '{display_name}'")
            cache_list = list(self.client.caches.list())
            logging.info(f"📋 Found {len(cache_list)} total caches")

            for cache in cache_list:
                logging.info(f"  - Cache: '{cache.display_name}' (comparing with '{display_name}')")
                if cache.display_name == display_name:
                    logging.info(f"✅ Found existing cache with display name: {display_name}")
                    logging.info(f"✅ Found matching cache: '{display_name}'")
                    return cache

            logging.info(f"Cache not found with display name: {display_name}")
            logging.info(f"❌ No cache found with display name: '{display_name}'")
            return None

        except Exception as e:
            logging.error(f"Error searching for cache: {e}")
            logging.info(f"❌ Error searching for cache: {e}")
            return None

    def _create_cache(self, content: str) -> bool:
        """Tạo cache cho merged_output.json với display name 'merged_output'."""
        if not self.enable_caching:
            logging.info("Caching is disabled, skipping cache creation")
            return False

        if not self.client:
            logging.warning("Client not initialized, cannot create cache")
            return False

        display_name = "merged_output"

        # Check if cache already exists
        print(f"🔍 Checking if cache '{display_name}' already exists...")
        existing_cache = self._find_cache_by_display_name(display_name)
        if existing_cache:
            print(f"✅ Cache '{display_name}' already exists, skipping creation")
            self.cache = existing_cache
            self.cache_created = False  # We didn't create it, just found it
            return True

        print(f"🆕 Cache '{display_name}' not found, creating new cache...")

        try:
            # Count cache tokens if token counting is enabled
            if self.count_token and self.model:
                try:
                    cache_token_count = self.model.count_tokens(content)
                    self.cache_tokens = cache_token_count.total_tokens
                    logging.info(f"Cache tokens: {self.cache_tokens:,}")

                    # Calculate and log cache cost
                    if self.cost_calculator and self.model_type:
                        if self.model_type == "flash":
                            cache_cost_info = self.cost_calculator.calculate_flash_cache_cost(self.cache_tokens)
                        else:
                            cache_cost_info = self.cost_calculator.calculate_pro_cache_cost(self.cache_tokens)

                        logging.info(f"Cache cost: ${cache_cost_info.get('total_cache_cost_usd', 0):.6f}")
                except Exception as e:
                    logging.warning(f"Failed to count cache tokens: {e}")

            self.cache = self.client.caches.create(
                model="gemini-2.5-pro",
                config=types.CreateCachedContentConfig(
                    system_instruction="Load all content into cache, do not replace or modify anything, keep the original content intact, including formatting",
                    contents=content,
                    display_name=display_name
                )
            )
            self.cache_created = True
            logging.info(f"Cache created successfully with name: {display_name}")
            print(f"🆕 New cache created for merged_output.json with display name: {display_name}")
            return True
        except Exception as e:
            logging.error(f"Error creating cache: {e}")
            print(f"❌ Failed to create cache: {e}")
            return False
