"""
Business Flow Detector Module for Pipeline
Detects and extracts business flows from processed documents
"""

import logging
import asyncio
from pathlib import Path
from typing import Dict, Any

from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

logger = logging.getLogger(__name__)

class BusinessFlowDetectorModule(PipelineModule):
    """Pipeline module for detecting business flows from documents"""
    
    def __init__(self):
        config = PipelineModuleConfig(
            id="business_flow_detector",
            name="Business Flow Detector",
            description="Detects and extracts business flows from processed document content",
            module_type=PipelineModuleType.PROCESSOR,
            version="1.0.0",
            dependencies=["document_processor"],  # Depends on document processor output
            input_types=["structured_json", "analyzed_json"],
            output_types=["business_flow_json"],
            metadata={
                "ai_powered": True,
                "supports_multiple_flows": True,
                "output_format": "JSON"
            }
        )
        super().__init__(config)
        
        # Set up paths
        self.output_base_dir = Path("output")
        self.business_flow_script = Path("back_end/BusinessFlowDetector.py")

        # Set up config file path - use absolute path
        back_end_path = Path(__file__).parent.parent.parent.parent  # Go up 4 levels to back_end
        self.config_file = back_end_path / "document" / "gemini_config_flash.json"
        
        logger.info(f"BusinessFlowDetectorModule initialized")
        logger.info(f"Output base directory: {self.output_base_dir}")
        logger.info(f"Business flow script: {self.business_flow_script}")

    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data for business flow detection"""
        try:
            # Check required fields
            if "document_id" not in input_data:
                logger.error("Missing required field: document_id")
                return False
                
            if "file_path" not in input_data:
                logger.error("Missing required field: file_path")
                return False
            
            document_id = input_data["document_id"]
            
            # Check if document processor outputs exist
            doc_output_dir = self.output_base_dir / document_id / "document_processor"
            
            merged_json = doc_output_dir / "merged_output.json"
            analyzed_json = doc_output_dir / "analyzed_output.json"
            
            if not merged_json.exists():
                logger.error(f"Required input file not found: {merged_json}")
                return False
                
            if not analyzed_json.exists():
                logger.error(f"Required input file not found: {analyzed_json}")
                return False
            
            # Check if BusinessFlowDetector script exists
            if not self.business_flow_script.exists():
                logger.error(f"BusinessFlowDetector script not found: {self.business_flow_script}")
                return False
            
            logger.info("Business flow detector input validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False

    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute business flow detection"""
        try:
            logger.info("BusinessFlowDetectorModule.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing document: {document_id}")
            logger.info(f"Original file: {file_path}")
            
            # Create output directory for this module
            doc_output_dir = self.output_base_dir / document_id / "business_flow_detector"
            doc_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Input files from document processor
            doc_processor_dir = self.output_base_dir / document_id / "document_processor"
            input_json = doc_processor_dir / "merged_output.json"
            
            # Output directory for business flows
            business_flow_dir = doc_output_dir / "business_flows"
            business_flow_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"Input JSON: {input_json}")
            logger.info(f"Business flow output directory: {business_flow_dir}")
            
            # Check if input file exists
            if not input_json.exists():
                raise FileNotFoundError(f"Input file not found: {input_json}")
            
            # Import and execute BusinessFlowDetector
            try:
                logger.info("Importing BusinessFlowDetector...")
                
                # Import the BusinessFlowDetector class
                import sys
                sys.path.append(str(Path("back_end").absolute()))
                
                from BusinessFlowDetector import GenBusinessFlow
                logger.info("BusinessFlowDetector imported successfully")

                logger.info("Initializing GenBusinessFlow...")
                detector = GenBusinessFlow(
                    input_json_file=str(input_json),
                    business_flow_dir=str(business_flow_dir),
                    config_file=str(self.config_file),
                    count_token=False
                )
                logger.info("GenBusinessFlow initialized")

                # Execute business flow detection in thread pool to avoid blocking
                logger.info("Starting business flow detection in thread pool...")
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    detector.execute
                )
                logger.info("Business flow detection completed")

            except ImportError as e:
                logger.error(f"Failed to import BusinessFlowDetector: {e}")
                return {
                    "success": False,
                    "error": f"BusinessFlowDetector not available: {e}",
                    "document_id": document_id
                }

            # Check outputs and prepare result
            outputs = {}
            summary = {
                "business_flows_detected": 0,
                "output_files": []
            }
            
            # Scan for generated business flow files (both .txt and .json)
            if business_flow_dir.exists():
                # Check for .txt files (primary output format)
                for flow_file in business_flow_dir.glob("*.txt"):
                    flow_name = flow_file.stem.replace(" ", "_")  # Replace spaces with underscores for key
                    outputs[f"business_flow_{flow_name}"] = str(flow_file)
                    summary["output_files"].append(str(flow_file))
                    summary["business_flows_detected"] += 1

                # Also check for .json files (alternative format)
                for flow_file in business_flow_dir.glob("*.json"):
                    flow_name = flow_file.stem.replace(" ", "_")
                    outputs[f"business_flow_{flow_name}"] = str(flow_file)
                    summary["output_files"].append(str(flow_file))
                    summary["business_flows_detected"] += 1
            
            # Determine success
            success = summary["business_flows_detected"] > 0
            
            if success:
                message = f"Successfully detected {summary['business_flows_detected']} business flows"
                logger.info(f"Business flow detection completed: {document_id}")
            else:
                message = "No business flows detected"
                logger.warning(f"No business flows detected for: {document_id}")

            result = {
                "success": success,
                "document_id": document_id,
                "input_file": str(input_json),
                "output_directory": str(doc_output_dir),
                "business_flow_directory": str(business_flow_dir),
                "output_files": outputs,
                "summary": summary,
                "message": message
            }
            
            logger.info(f"Business flow detection completed: {document_id}")
            return result

        except Exception as e:
            logger.error(f"Business flow detection failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": input_data.get("document_id", "unknown")
            }

    async def get_status(self) -> Dict[str, Any]:
        """Get current module status"""
        return {
            "module_id": self.config.id,
            "status": "ready",
            "version": self.config.version,
            "business_flow_script_exists": self.business_flow_script.exists(),
            "output_directory": str(self.output_base_dir)
        }
