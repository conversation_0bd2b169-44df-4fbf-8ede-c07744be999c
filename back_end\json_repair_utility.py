import json
import re
from typing import Dict, Optional, Tuple, Any, List, Union

class JSONRepairUtility:
    """
    A utility class to repair and validate malformed JSON data.
    Handles common JSON syntax problems and formatting issues.
    """
    
    @staticmethod
    def repair_json(json_str: str) -> str:
        """
        Repair common JSON syntax issues and return valid JSON.
        
        Args:
            json_str: Potentially malformed JSON string
            
        Returns:
            Valid JSON string
        """
        if not json_str or not isinstance(json_str, str):
            print("Error: Input must be a non-empty string")
            return "{}"
            
        try:
            # First check if it's already valid
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            # Continue with repairs
            pass
        
        # Remove comments (both single-line and multi-line)
        json_str = re.sub(r'//.*?(\r\n|\r|\n)', '\n', json_str)  # Single-line comments
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)  # Multi-line comments
        
        # Fix trailing commas in arrays and objects
        json_str = re.sub(r',(\s*[\]}])', r'\1', json_str)
        
        # Add missing quotes to keys
        json_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', json_str)
        
        # Handle quote issues
        repaired = JSONRepairUtility._fix_quotes(json_str)
        
        # Balance brackets and braces
        repaired = JSONRepairUtility._balance_brackets(repaired)
        
        # Try parsing the repaired JSON
        try:
            parsed_json = json.loads(repaired)
            return json.dumps(parsed_json, ensure_ascii=False)
        except json.JSONDecodeError as e:
            print(f"JSON repair attempts failed: {e}")
            
            # Last resort: Use a more aggressive approach
            try:
                # Try to use ast.literal_eval which is more forgiving
                import ast
                parsed = ast.literal_eval(repaired)
                if isinstance(parsed, (dict, list)):
                    return json.dumps(parsed, ensure_ascii=False)
            except Exception:
                # If all else fails, return empty JSON
                pass
            
            return "{}"  # Return empty JSON as a last resort
    
    @staticmethod
    def _fix_quotes(text: str) -> str:
        """
        Fix quote issues in JSON strings.
        
        Args:
            text: Text with potential quote issues
            
        Returns:
            Text with fixed quotes
        """
        # Two-pass approach:
        # 1. Replace standalone single quotes that are likely to be JSON quotes
        # 2. Handle any remaining edge cases
        
        # Simple case: replace all single quotes not preceded by a backslash
        text = re.sub(r'(?<!\\)\'', '"', text)
        
        # Fix any doubled quotes that might have been created
        text = re.sub(r'""', '"', text)
        
        return text
    
    @staticmethod
    def _balance_brackets(text: str) -> str:
        """
        Balance brackets and braces in JSON text.
        
        Args:
            text: Text with potentially unbalanced brackets
            
        Returns:
            Text with balanced brackets
        """
        stack = []
        for i, char in enumerate(text):
            if char in '{[':
                stack.append(char)
            elif char in '}]':
                if not stack:
                    # More closing than opening brackets
                    # Insert a matching opening bracket at the beginning
                    opening = '{' if char == '}' else '['
                    text = opening + text
                    stack.append(opening)
                
                expected = '{' if char == '}' else '['
                if stack[-1] == expected:
                    stack.pop()
                else:
                    # Mismatched bracket type
                    correct_char = '}' if stack[-1] == '{' else ']'
                    text = text[:i] + correct_char + text[i+1:]
                    stack.pop()
        
        # Add any missing closing brackets at the end
        while stack:
            bracket = stack.pop()
            closing = '}' if bracket == '{' else ']'
            text += closing
            
        return text
    
    @staticmethod
    def validate_json_file(file_path: str, repair: bool = False) -> Tuple[bool, Optional[Dict]]:
        """
        Validate a JSON file and optionally repair it if it's invalid.
        
        Args:
            file_path: Path to the JSON file
            repair: Whether to repair and save the file if invalid
            
        Returns:
            Tuple of (is_valid, parsed_data)
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Try to parse as is
            try:
                parsed_data = json.loads(content)
                return True, parsed_data
            except json.JSONDecodeError:
                if not repair:
                    return False, None
                    
                # Repair the JSON
                repaired_json = JSONRepairUtility.repair_json(content)
                
                if repaired_json != "{}":
                    # Write the repaired JSON back to the file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(repaired_json)
                    
                    parsed_data = json.loads(repaired_json)
                    print(f"JSON file repaired and saved: {file_path}")
                    return True, parsed_data
                else:
                    return False, None
                    
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
            return False, None
            
    @staticmethod
    def extract_json_from_text(text: str) -> str:
        """
        Extract JSON content from text that might contain markdown code blocks
        or other non-JSON content.
        
        Args:
            text: Text that may contain JSON
            
        Returns:
            Extracted JSON string
        """
        # Try to extract from markdown code blocks first
        json_pattern = r"```(?:json)?\s*([\s\S]*?)```"
        matches = re.findall(json_pattern, text)
        
        if matches:
            # Return the first match that successfully parses as JSON
            for match in matches:
                try:
                    json.loads(match.strip())
                    return match.strip()
                except:
                    continue
        
        # If no valid JSON in code blocks, try to find JSON objects/arrays directly
        json_object_pattern = r"\{[\s\S]*\}"
        json_array_pattern = r"\[[\s\S]*\]"
        
        for pattern in [json_object_pattern, json_array_pattern]:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    json.loads(match.strip())
                    return match.strip()
                except:
                    continue
        
        # If no JSON found, return the original text
        return text
    
    @staticmethod
    def fix_unhashable_keys(data: Any) -> Any:
        """
        Recursively process a data structure, converting any unhashable keys
        (like lists) to hashable types (tuples or strings) for dictionaries.
        
        Args:
            data: The data structure to fix
            
        Returns:
            Fixed data structure
        """
        if isinstance(data, dict):
            result = {}
            for k, v in data.items():
                # Convert unhashable keys to strings or tuples
                if isinstance(k, list):
                    new_key = tuple(k) if all(isinstance(x, (int, float, str, bool, type(None))) for x in k) else str(k)
                else:
                    new_key = k
                # Recursively process values
                result[new_key] = JSONRepairUtility.fix_unhashable_keys(v)
            return result
        elif isinstance(data, list):
            return [JSONRepairUtility.fix_unhashable_keys(item) for item in data]
        else:
            return data
    
    @staticmethod
    def safe_parse_json(json_str: str) -> Tuple[Any, str]:
        """
        Safely parse JSON with error handling and repair attempts.
        
        Args:
            json_str: JSON string to parse
            
        Returns:
            Tuple of (parsed_json, error_message)
            If successful, error_message will be empty
        """
        error_msg = ""
        
        # Step 1: Try parsing the original string
        try:
            data = json.loads(json_str)
            return data, ""
        except json.JSONDecodeError as e:
            error_msg = f"Initial parsing error: {str(e)}"
            
        # Step 2: Try extracting JSON from text
        extracted_json = JSONRepairUtility.extract_json_from_text(json_str)
        if extracted_json != json_str:
            try:
                data = json.loads(extracted_json)
                return data, ""
            except json.JSONDecodeError as e:
                error_msg += f"\nExtracted JSON parsing error: {str(e)}"
        
        # Step 3: Try repairing the JSON
        repaired_json = JSONRepairUtility.repair_json(extracted_json)
        try:
            data = json.loads(repaired_json)
            return data, ""
        except json.JSONDecodeError as e:
            error_msg += f"\nRepaired JSON parsing error: {str(e)}"
            
        # Step 4: Give up
        return None, error_msg
    
    @staticmethod
    def analyze_json(json_str: str) -> Dict[str, Any]:
        """
        Analyze a JSON string for potential issues.
        
        Args:
            json_str: JSON string to analyze
            
        Returns:
            Dictionary with analysis results
        """
        result = {
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "structure_type": None,
            "size_chars": len(json_str),
            "repair_suggestions": []
        }
        
        # Check if empty or whitespace
        if not json_str or json_str.isspace():
            result["errors"].append("Empty or whitespace-only JSON")
            return result
            
        # Try to parse with standard JSON parser
        try:
            data = json.loads(json_str)
            result["is_valid"] = True
            result["structure_type"] = "object" if isinstance(data, dict) else "array" if isinstance(data, list) else "scalar"
        except json.JSONDecodeError as e:
            result["errors"].append(f"JSON parsing error: {str(e)}")
            line_col_match = re.search(r'line (\d+) column (\d+)', str(e))
            if line_col_match:
                line, col = line_col_match.groups()
                line_idx = int(line) - 1
                lines = json_str.split('\n')
                
                if 0 <= line_idx < len(lines):
                    context_start = max(0, line_idx - 2)
                    context_end = min(len(lines), line_idx + 3)
                    context = lines[context_start:context_end]
                    
                    result["errors"].append(f"Error context (lines {context_start+1}-{context_end}):")
                    for i, ctx_line in enumerate(context):
                        line_num = context_start + i + 1
                        if line_num == int(line):
                            # Add a marker pointing to the error column
                            pointer = ' ' * (int(col) - 1) + '^'
                            result["errors"].append(f"{line_num}: {ctx_line}")
                            result["errors"].append(f"    {pointer}")
                        else:
                            result["errors"].append(f"{line_num}: {ctx_line}")
            
            # Check for common errors and add repair suggestions
            if "Expecting ',' delimiter" in str(e):
                result["repair_suggestions"].append("Missing comma between elements")
            elif "Expecting property name" in str(e):
                result["repair_suggestions"].append("Missing or improperly formatted property name")
            elif "Expecting ':' delimiter" in str(e):
                result["repair_suggestions"].append("Missing colon after property name")
            elif "Expecting value" in str(e):
                result["repair_suggestions"].append("Missing value or unexpected end of JSON")
            elif "Extra data" in str(e):
                result["repair_suggestions"].append("Extra characters after JSON document")
            
            # Try to repair
            repaired = JSONRepairUtility.repair_json(json_str)
            try:
                data = json.loads(repaired)
                result["repair_suggestions"].append("JSON could be repaired automatically")
                result["repaired_json"] = repaired
            except:
                pass
        
        return result
    
    @staticmethod
    def validate_json_file(file_path: str) -> Dict[str, Any]:
        """
        Validate a JSON file and report any issues.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Dictionary with validation results
        """
        result = {
            "file_path": file_path,
            "is_valid": False,
            "errors": [],
            "size_bytes": 0
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                result["size_bytes"] = len(content)
                
                analysis = JSONRepairUtility.analyze_json(content)
                result.update(analysis)
                
                # Check if unhashable keys are present
                if result["is_valid"]:
                    data = json.loads(content)
                    try:
                        # Try to check if there are unhashable keys by traversing the structure
                        JSONRepairUtility.fix_unhashable_keys(data)
                    except TypeError as e:
                        result["warnings"].append(f"Potential unhashable key issue: {str(e)}")
                
        except FileNotFoundError:
            result["errors"].append(f"File not found: {file_path}")
        except Exception as e:
            result["errors"].append(f"Error reading file: {str(e)}")
            
        return result
    
    @staticmethod
    def validate_screen_graph_structure(json_data: dict) -> dict:
        """
        Validate and fix a screen graph structure for BFS algorithms.
        
        Args:
            json_data: Parsed JSON data that should contain a graph structure
            
        Returns:
            Fixed JSON data with valid graph structure
        """
        # Check if the basic structure exists
        if not isinstance(json_data, dict):
            return {"graph": {"nodes": [], "edges": []}}
            
        if "graph" not in json_data:
            json_data["graph"] = {"nodes": [], "edges": []}
        elif not isinstance(json_data["graph"], dict):
            json_data["graph"] = {"nodes": [], "edges": []}
        
        graph = json_data["graph"]
        
        # Ensure nodes and edges exist and are lists
        if "nodes" not in graph or not isinstance(graph["nodes"], list):
            graph["nodes"] = []
            
        if "edges" not in graph or not isinstance(graph["edges"], list):
            graph["edges"] = []
            
        # Validate node structure
        for i, node in enumerate(graph["nodes"]):
            if not isinstance(node, dict):
                graph["nodes"][i] = {"id": f"unknown_node_{i}", "name": "Unknown Screen", "type": "screen"}
                continue
                
            # Ensure required node fields
            if "id" not in node or not node["id"]:
                node["id"] = f"node_{i}"
                
            if "name" not in node or not node["name"]:
                node["name"] = f"Screen {i}"
                
            if "type" not in node:
                node["type"] = "screen"
                
        # Validate edge structure
        for i, edge in enumerate(graph["edges"]):
            if not isinstance(edge, dict):
                graph["edges"][i] = {
                    "source": "unknown_source", 
                    "target": "unknown_target", 
                    "action": "Unknown action",
                    "element": "Unknown element"
                }
                continue
                
            # Ensure required edge fields
            if "source" not in edge or not edge["source"]:
                edge["source"] = "unknown_source"
                
            if "target" not in edge or not edge["target"]:
                edge["target"] = "unknown_target"
                
            if "action" not in edge:
                edge["action"] = "Navigate"
                
            if "element" not in edge:
                edge["element"] = "Unknown element"
        
        # Create a set of node IDs to validate edge references
        node_ids = {node["id"] for node in graph["nodes"]}
        
        # Check if edge source/target references exist in nodes
        missing_nodes = set()
        for edge in graph["edges"]:
            if edge["source"] not in node_ids:
                missing_nodes.add(edge["source"])
            if edge["target"] not in node_ids:
                missing_nodes.add(edge["target"])
        
        # Add missing nodes
        for missing_id in missing_nodes:
            graph["nodes"].append({
                "id": missing_id,
                "name": f"Auto-created {missing_id}",
                "type": "screen"
            })
            
        return json_data 