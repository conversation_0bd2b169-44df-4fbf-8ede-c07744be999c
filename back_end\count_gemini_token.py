import json

class GeminiCostCalculator:
    """
    <PERSON><PERSON><PERSON> lớp tiện ích để tính toán chi phí ước tính cho các lệnh gọi API 
    của Google Gemini 2.5 Pro và Gemini 2.5 Flash dựa trên bảng giá đã cung cấp.

    Lưu ý: Bảng giá có thể thay đổi. <PERSON><PERSON><PERSON> tham khảo tài liệu chính thức của Google
    để có thông tin giá mới nhất.
    Giá trong lớp này được tính cho mỗi 1,000,000 tokens.
    """

    # --- Hằng số giá cho Gemini 2.5 Pro (Paid Tier) ---
    # Gi<PERSON> cho mỗi 1,000,000 tokens
    PRO_TIER_BREAKPOINT_TOKENS = 200_000
    
    # Gi<PERSON> khi prompt <= 200k tokens
    PRO_INPUT_PRICE_TIER1 = 1.25  
    PRO_OUTPUT_PRICE_TIER1 = 10.00
    
    # Gi<PERSON> khi prompt > 200k tokens
    PRO_INPUT_PRICE_TIER2 = 2.50
    PRO_OUTPUT_PRICE_TIER2 = 15.00

    # --- Hằng số giá cho Context Caching của Gemini 2.5 Pro ---
    # Giá context caching cho mỗi 1,000,000 tokens
    PRO_CACHE_PRICE_TIER1 = 0.31  # prompts <= 200k tokens
    PRO_CACHE_PRICE_TIER2 = 0.625  # prompts > 200k tokens
    PRO_CACHE_STORAGE_PRICE = 4.50  # per 1M tokens per hour

    # --- Hằng số giá cho Gemini 2.5 Flash (Paid Tier, Text/Image) ---
    # Giá cho mỗi 1,000,000 tokens (giả sử đầu vào là text/image)
    FLASH_INPUT_PRICE_TEXT = 0.30
    FLASH_OUTPUT_PRICE = 2.50
    
    # --- Hằng số giá cho Context Caching của Gemini 2.5 Flash ---
    # Giá context caching cho mỗi 1,000,000 tokens
    FLASH_CACHE_PRICE_TEXT = 0.075  # text/image/video
    FLASH_CACHE_PRICE_AUDIO = 0.25  # audio
    FLASH_CACHE_STORAGE_PRICE = 1.00  # per 1M tokens per hour
    
    def _calculate_cost(self, tokens: int, price_per_million: float) -> float:
        """Hàm trợ giúp để tính chi phí dựa trên số token và giá."""
        return (tokens / 1_000_000) * price_per_million

    def calculate_pro_cost(self, input_tokens: int, output_tokens: int) -> dict:
        """
        Tính toán chi phí cho Gemini 2.5 Pro.
        
        Bảng giá của Pro phụ thuộc vào kích thước của prompt đầu vào.

        Args:
            input_tokens (int): Tổng số token của prompt đầu vào.
            output_tokens (int): Tổng số token của response đầu ra.

        Returns:
            dict: Một dictionary chứa chi phí đầu vào, đầu ra và tổng chi phí.
        """
        if input_tokens <= self.PRO_TIER_BREAKPOINT_TOKENS:
            # Sử dụng giá của Bậc 1 (Tier 1)
            input_price = self.PRO_INPUT_PRICE_TIER1
            output_price = self.PRO_OUTPUT_PRICE_TIER1
        else:
            # Sử dụng giá của Bậc 2 (Tier 2)
            input_price = self.PRO_INPUT_PRICE_TIER2
            output_price = self.PRO_OUTPUT_PRICE_TIER2

        input_cost = self._calculate_cost(input_tokens, input_price)
        output_cost = self._calculate_cost(output_tokens, output_price)
        total_cost = input_cost + output_cost

        return {
            "model": "Gemini 2.5 Pro",
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "input_cost_usd": round(input_cost, 6),
            "output_cost_usd": round(output_cost, 6),
            "total_cost_usd": round(total_cost, 6)
        }

    def calculate_flash_cost(self, input_tokens: int, output_tokens: int) -> dict:
        """
        Tính toán chi phí cho Gemini 2.5 Flash (cho đầu vào dạng text/image).

        Args:
            input_tokens (int): Tổng số token của prompt đầu vào.
            output_tokens (int): Tổng số token của response đầu ra.

        Returns:
            dict: Một dictionary chứa chi phí đầu vào, đầu ra và tổng chi phí.
        """
        input_cost = self._calculate_cost(input_tokens, self.FLASH_INPUT_PRICE_TEXT)
        output_cost = self._calculate_cost(output_tokens, self.FLASH_OUTPUT_PRICE)
        total_cost = input_cost + output_cost

        return {
            "model": "Gemini 2.5 Flash",
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "input_cost_usd": round(input_cost, 6),
            "output_cost_usd": round(output_cost, 6),
            "total_cost_usd": round(total_cost, 6)
        }

    def calculate_pro_cache_cost(self, cache_tokens: int, storage_hours: float = 1.0) -> dict:
        """
        Tính toán chi phí cho Context Caching của Gemini 2.5 Pro.

        Args:
            cache_tokens (int): Số token được cache.
            storage_hours (float): Số giờ lưu trữ cache (mặc định 1 giờ).

        Returns:
            dict: Dictionary chứa chi phí caching và storage.
        """
        # Xác định tier pricing dựa trên cache size
        if cache_tokens <= self.PRO_TIER_BREAKPOINT_TOKENS:
            cache_price = self.PRO_CACHE_PRICE_TIER1
        else:
            cache_price = self.PRO_CACHE_PRICE_TIER2

        cache_cost = self._calculate_cost(cache_tokens, cache_price)
        storage_cost = self._calculate_cost(cache_tokens, self.PRO_CACHE_STORAGE_PRICE) * storage_hours
        total_cost = cache_cost + storage_cost

        return {
            "model": "Gemini 2.5 Pro",
            "cache_tokens": cache_tokens,
            "storage_hours": storage_hours,
            "cache_cost_usd": round(cache_cost, 6),
            "storage_cost_usd": round(storage_cost, 6),
            "total_cache_cost_usd": round(total_cost, 6),
            "tier": "Tier 1" if cache_tokens <= self.PRO_TIER_BREAKPOINT_TOKENS else "Tier 2"
        }

    def calculate_flash_cache_cost(self, cache_tokens: int, storage_hours: float = 1.0, content_type: str = "text") -> dict:
        """
        Tính toán chi phí cho Context Caching của Gemini 2.5 Flash.

        Args:
            cache_tokens (int): Số token được cache.
            storage_hours (float): Số giờ lưu trữ cache (mặc định 1 giờ).
            content_type (str): Loại nội dung ('text', 'audio'). Mặc định 'text'.

        Returns:
            dict: Dictionary chứa chi phí caching và storage.
        """
        # Xác định giá dựa trên loại nội dung
        if content_type.lower() == "audio":
            cache_price = self.FLASH_CACHE_PRICE_AUDIO
        else:
            cache_price = self.FLASH_CACHE_PRICE_TEXT

        cache_cost = self._calculate_cost(cache_tokens, cache_price)
        storage_cost = self._calculate_cost(cache_tokens, self.FLASH_CACHE_STORAGE_PRICE) * storage_hours
        total_cost = cache_cost + storage_cost

        return {
            "model": "Gemini 2.5 Flash",
            "cache_tokens": cache_tokens,
            "storage_hours": storage_hours,
            "content_type": content_type,
            "cache_cost_usd": round(cache_cost, 6),
            "storage_cost_usd": round(storage_cost, 6),
            "total_cache_cost_usd": round(total_cost, 6)
        }

    def calculate_combined_cost_with_cache(self, model_type: str, input_tokens: int, output_tokens: int, 
                                         cache_tokens: int = 0, storage_hours: float = 1.0, 
                                         content_type: str = "text") -> dict:
        """
        Tính toán tổng chi phí bao gồm cả API calls và caching.

        Args:
            model_type (str): Loại model ('pro' hoặc 'flash').
            input_tokens (int): Số token input cho API calls.
            output_tokens (int): Số token output từ API calls.
            cache_tokens (int): Số token được cache.
            storage_hours (float): Số giờ lưu trữ cache.
            content_type (str): Loại nội dung cho Flash cache ('text' hoặc 'audio').

        Returns:
            dict: Dictionary chứa tổng chi phí bao gồm API và cache.
        """
        model_type = model_type.lower()
        
        # Tính chi phí API calls
        if model_type == "flash":
            api_cost_info = self.calculate_flash_cost(input_tokens, output_tokens)
        else:
            api_cost_info = self.calculate_pro_cost(input_tokens, output_tokens)
        
        # Tính chi phí cache nếu có
        cache_cost_info = {}
        if cache_tokens > 0:
            if model_type == "flash":
                cache_cost_info = self.calculate_flash_cache_cost(cache_tokens, storage_hours, content_type)
            else:
                cache_cost_info = self.calculate_pro_cache_cost(cache_tokens, storage_hours)
        
        # Kết hợp chi phí
        total_cost = api_cost_info.get("total_cost_usd", 0) + cache_cost_info.get("total_cache_cost_usd", 0)
        
        result = {
            "model": api_cost_info.get("model"),
            "api_costs": {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "input_cost_usd": api_cost_info.get("input_cost_usd", 0),
                "output_cost_usd": api_cost_info.get("output_cost_usd", 0),
                "api_total_cost_usd": api_cost_info.get("total_cost_usd", 0)
            },
            "combined_total_cost_usd": round(total_cost, 6)
        }
        
        # Thêm thông tin cache nếu có
        if cache_tokens > 0:
            result["cache_costs"] = {
                "cache_tokens": cache_tokens,
                "storage_hours": storage_hours,
                "cache_cost_usd": cache_cost_info.get("cache_cost_usd", 0),
                "storage_cost_usd": cache_cost_info.get("storage_cost_usd", 0),
                "cache_total_cost_usd": cache_cost_info.get("total_cache_cost_usd", 0)
            }
            if model_type == "flash":
                result["cache_costs"]["content_type"] = content_type
            else:
                result["cache_costs"]["tier"] = cache_cost_info.get("tier")
        
        return result

    def print_token_summary(self, token_summary: dict, module_name: str = "MODULE") -> None:
        """
        In tổng kết chi phí token và cost cho bất kỳ module nào.
        
        Args:
            token_summary (dict): Dictionary chứa thông tin token summary từ module
            module_name (str): Tên module để hiển thị trong header
        """
        if not token_summary.get("token_counting_enabled", False):
            print(f"\n=== {module_name.upper()} TOKEN USAGE SUMMARY ===")
            print("❌ Token counting is disabled")
            return
            
        print(f"\n=== {module_name.upper()} TOKEN USAGE SUMMARY ===")
        print(f"🤖 Model Type: {token_summary.get('model_type', 'Unknown').upper()}")
        
        # Display API call count if available
        if 'api_call_count' in token_summary:
            print(f"📞 API Calls Made: {token_summary.get('api_call_count', 0)}")
        
        print(f"📥 Total Input Tokens: {token_summary.get('total_input_tokens', 0):,}")
        print(f"📤 Total Output Tokens: {token_summary.get('total_output_tokens', 0):,}")
        print(f"🔢 Total Tokens: {token_summary.get('total_tokens', 0):,}")
        
        # Check if cache is enabled
        if token_summary.get('cache_enabled', False):
            print(f"🗄️ Cache Enabled: Yes")
            print(f"📦 Cache Tokens: {token_summary.get('cache_tokens', 0):,}")
            
            # Display combined costs
            if 'combined_total_cost_usd' in token_summary:
                print(f"💰 Combined Total Cost: ${token_summary.get('combined_total_cost_usd', 0):.6f} USD")
                
                # API costs breakdown
                api_costs = token_summary.get('api_costs', {})
                if api_costs:
                    print(f"📡 API Costs:")
                    print(f"  💵 Input Cost: ${api_costs.get('input_cost_usd', 0):.6f} USD")
                    print(f"  💸 Output Cost: ${api_costs.get('output_cost_usd', 0):.6f} USD")
                    print(f"  🎯 API Total: ${api_costs.get('api_total_cost_usd', 0):.6f} USD")
                
                # Cache costs breakdown
                cache_costs = token_summary.get('cache_costs', {})
                if cache_costs:
                    print(f"🗄️ Cache Costs:")
                    print(f"  💾 Cache Cost: ${cache_costs.get('cache_cost_usd', 0):.6f} USD")
                    print(f"  ⏱️ Storage Cost: ${cache_costs.get('storage_cost_usd', 0):.6f} USD ({cache_costs.get('storage_hours', 1):.1f}h)")
                    print(f"  📦 Cache Total: ${cache_costs.get('cache_total_cost_usd', 0):.6f} USD")
                    
                    # Display tier info for Pro model
                    if token_summary.get('model_type') == 'pro' and 'tier' in cache_costs:
                        print(f"  🎚️ Pricing Tier: {cache_costs.get('tier')}")
                    # Display content type for Flash model
                    elif token_summary.get('model_type') == 'flash' and 'content_type' in cache_costs:
                        print(f"  📄 Content Type: {cache_costs.get('content_type')}")
        else:
            print(f"🗄️ Cache Enabled: No")
            
            # Display regular API costs
            if 'total_cost_usd' in token_summary:
                print(f"💰 Total Cost: ${token_summary.get('total_cost_usd', 0):.6f} USD")
                print(f"💵 Input Cost: ${token_summary.get('input_cost_usd', 0):.6f} USD")
                print(f"💸 Output Cost: ${token_summary.get('output_cost_usd', 0):.6f} USD")
                
        print("=" * 60)