"""
Pipeline Registry System
Dynamic pipeline module registration and management
"""

import logging
from typing import Dict, List, Any, Optional, Callable, Awaitable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PipelineModuleType(str, Enum):
    """Types of pipeline modules"""
    PROCESSOR = "processor"
    ANALYZER = "analyzer"
    GENERATOR = "generator"
    EVALUATOR = "evaluator"

@dataclass
class PipelineModuleConfig:
    """Configuration for a pipeline module"""
    id: str
    name: str
    description: str
    module_type: PipelineModuleType
    version: str = "1.0.0"
    dependencies: List[str] = None
    input_types: List[str] = None
    output_types: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.input_types is None:
            self.input_types = []
        if self.output_types is None:
            self.output_types = []
        if self.metadata is None:
            self.metadata = {}

class PipelineModule(ABC):
    """Abstract base class for pipeline modules"""
    
    def __init__(self, config: PipelineModuleConfig):
        self.config = config
        self.logger = logging.getLogger(f"pipeline.{config.id}")
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the pipeline module"""
        pass
    
    @abstractmethod
    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data"""
        pass
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current module status"""
        return {
            "module_id": self.config.id,
            "status": "ready",
            "version": self.config.version
        }

class PipelineRegistry:
    """Registry for managing pipeline modules"""
    
    def __init__(self):
        self._modules: Dict[str, PipelineModule] = {}
        self._module_configs: Dict[str, PipelineModuleConfig] = {}
        self._execution_order: List[str] = []
        self.logger = logging.getLogger("pipeline.registry")
    
    def register_module(self, module: PipelineModule) -> None:
        """Register a pipeline module"""
        module_id = module.config.id
        
        if module_id in self._modules:
            self.logger.warning(f"Module {module_id} already registered, overwriting")
        
        self._modules[module_id] = module
        self._module_configs[module_id] = module.config
        
        # Update execution order based on dependencies
        self._update_execution_order()
        
        self.logger.info(f"Registered pipeline module: {module_id}")
    
    def unregister_module(self, module_id: str) -> None:
        """Unregister a pipeline module"""
        if module_id in self._modules:
            del self._modules[module_id]
            del self._module_configs[module_id]
            self._update_execution_order()
            self.logger.info(f"Unregistered pipeline module: {module_id}")
        else:
            self.logger.warning(f"Module {module_id} not found for unregistration")
    
    def get_module(self, module_id: str) -> Optional[PipelineModule]:
        """Get a registered module"""
        return self._modules.get(module_id)
    
    def get_module_config(self, module_id: str) -> Optional[PipelineModuleConfig]:
        """Get module configuration"""
        return self._module_configs.get(module_id)
    
    def list_modules(self) -> List[Dict[str, Any]]:
        """List all registered modules"""
        modules = []
        for module_id in self._execution_order:
            config = self._module_configs[module_id]
            modules.append({
                "id": config.id,
                "name": config.name,
                "description": config.description,
                "type": config.module_type,
                "version": config.version,
                "dependencies": config.dependencies,
                "input_types": config.input_types,
                "output_types": config.output_types,
                "metadata": config.metadata
            })
        return modules
    
    def get_execution_order(self) -> List[str]:
        """Get modules in execution order"""
        return self._execution_order.copy()
    
    def _update_execution_order(self) -> None:
        """Update execution order based on dependencies"""
        # Simple topological sort for dependency resolution
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(module_id: str):
            if module_id in temp_visited:
                raise ValueError(f"Circular dependency detected involving {module_id}")
            if module_id in visited:
                return
            
            temp_visited.add(module_id)
            
            config = self._module_configs.get(module_id)
            if config:
                for dep in config.dependencies:
                    if dep in self._module_configs:
                        visit(dep)
            
            temp_visited.remove(module_id)
            visited.add(module_id)
            order.append(module_id)
        
        # Visit all modules
        for module_id in self._module_configs:
            if module_id not in visited:
                visit(module_id)
        
        self._execution_order = order
        self.logger.info(f"Updated execution order: {order}")
    
    async def validate_pipeline(self) -> Dict[str, Any]:
        """Validate the entire pipeline"""
        issues = []
        
        for module_id in self._execution_order:
            config = self._module_configs[module_id]
            
            # Check dependencies
            for dep in config.dependencies:
                if dep not in self._modules:
                    issues.append(f"Module {module_id} depends on missing module {dep}")
            
            # Check input/output compatibility
            # This is a simplified check - in practice you'd want more sophisticated validation
            
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "module_count": len(self._modules),
            "execution_order": self._execution_order
        }

# Global registry instance
pipeline_registry = PipelineRegistry()
