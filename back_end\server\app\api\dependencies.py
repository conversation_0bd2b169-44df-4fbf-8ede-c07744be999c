"""
API Dependencies
Dependency injection for FastAPI routes
"""

from app.services.websocket_service import WebSocketManager
from app.services.pipeline_service import PipelineService

# Global service instances (will be initialized in main.py)
_websocket_manager: WebSocketManager = None
_pipeline_service: PipelineService = None

def init_services(websocket_manager: WebSocketManager, pipeline_service: PipelineService):
    """Initialize global service instances"""
    global _websocket_manager, _pipeline_service
    _websocket_manager = websocket_manager
    _pipeline_service = pipeline_service

def get_websocket_manager() -> WebSocketManager:
    """Get WebSocket manager instance"""
    if _websocket_manager is None:
        raise RuntimeError("WebSocket manager not initialized")
    return _websocket_manager

def get_pipeline_service() -> PipelineService:
    """Get pipeline service instance"""
    if _pipeline_service is None:
        raise RuntimeError("Pipeline service not initialized")
    return _pipeline_service
