"""
Relevant Content Processor Module for Pipeline
Processes and extracts relevant content from documents
"""

import logging
import asyncio
from pathlib import Path
from typing import Dict, Any

from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

logger = logging.getLogger(__name__)

class RelevantContentProcessorModule(PipelineModule):
    """Pipeline module for processing relevant content from documents"""
    
    def __init__(self):
        config = PipelineModuleConfig(
            id="relevant_content_processor",
            name="Relevant Content Processor",
            description="Processes and extracts relevant content from document analysis",
            module_type=PipelineModuleType.PROCESSOR,
            version="1.0.0",
            dependencies=["document_processor", "business_flow_detector"],  # Depends on previous modules
            input_types=["structured_json", "business_flow_json"],
            output_types=["relevant_content_json"],
            metadata={
                "ai_powered": True,
                "content_filtering": True,
                "output_format": "JSON"
            }
        )
        super().__init__(config)
        
        # Set up paths
        self.output_base_dir = Path("output")
        self.processor_script = Path("back_end/relevant_content_processor.py")

        # Set up config file path - use absolute path
        back_end_path = Path(__file__).parent.parent.parent.parent  # Go up 4 levels to back_end
        self.config_file = back_end_path / "document" / "gemini_config_pro.json"
        
        logger.info(f"RelevantContentProcessorModule initialized")
        logger.info(f"Output base directory: {self.output_base_dir}")
        logger.info(f"Processor script: {self.processor_script}")

    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data for relevant content processing"""
        try:
            # Check required fields
            if "document_id" not in input_data:
                logger.error("Missing required field: document_id")
                return False
                
            if "file_path" not in input_data:
                logger.error("Missing required field: file_path")
                return False
            
            document_id = input_data["document_id"]
            
            # Check if previous module outputs exist
            doc_processor_dir = self.output_base_dir / document_id / "document_processor"
            business_flow_dir = self.output_base_dir / document_id / "business_flow_detector"
            
            # Check document processor outputs
            merged_json = doc_processor_dir / "merged_output.json"
            if not merged_json.exists():
                logger.error(f"Required input file not found: {merged_json}")
                return False
            
            # Check business flow detector outputs (optional - may not exist)
            business_flows_exist = business_flow_dir.exists() and any(business_flow_dir.glob("*.json"))
            
            # Check if processor script exists
            if not self.processor_script.exists():
                logger.error(f"Relevant content processor script not found: {self.processor_script}")
                return False
            
            logger.info("Relevant content processor input validation passed")
            logger.info(f"Business flows available: {business_flows_exist}")
            return True
            
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False

    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute relevant content processing"""
        try:
            logger.info("RelevantContentProcessorModule.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing document: {document_id}")
            logger.info(f"Original file: {file_path}")
            
            # Create output directory for this module
            doc_output_dir = self.output_base_dir / document_id / "relevant_content_processor"
            doc_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Input files from previous modules
            doc_processor_dir = self.output_base_dir / document_id / "document_processor"
            business_flow_dir = self.output_base_dir / document_id / "business_flow_detector" / "business_flows"
            
            input_json = doc_processor_dir / "merged_output.json"

            logger.info(f"Input JSON: {input_json}")
            logger.info(f"Output directory: {doc_output_dir}")
            
            # Check if input file exists
            if not input_json.exists():
                raise FileNotFoundError(f"Input file not found: {input_json}")
            
            # Import and execute RelevantContentProcessor
            try:
                logger.info("Importing RelevantContentProcessor...")
                
                # Import the processor class
                import sys
                sys.path.append(str(Path("back_end").absolute()))
                
                from relevant_content_processor import RelevantContentProcessor
                logger.info("RelevantContentProcessor imported successfully")

                logger.info("Initializing RelevantContentProcessor...")
                processor = RelevantContentProcessor(
                    description_json=str(input_json),  # Use merged_output.json as description
                    merged_json=str(input_json),       # Same file for both
                    config_file=str(self.config_file),
                    business_flows_dir=str(business_flow_dir) if business_flow_dir.exists() else "",
                    base_output_dir=str(doc_output_dir),
                    rotate_api_key=False,
                    count_token=False,
                    max_workers=6,
                    enable_caching=True
                )
                logger.info("RelevantContentProcessor initialized")

                # Execute processing in thread pool to avoid blocking
                logger.info("Starting relevant content processing in thread pool...")
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    processor.execute,
                    None,  # business_process (deprecated)
                    False, # rotate_api_key
                    True   # use_threading
                )
                logger.info("Relevant content processing completed")

            except ImportError as e:
                logger.error(f"Failed to import RelevantContentProcessor: {e}")
                return {
                    "success": False,
                    "error": f"RelevantContentProcessor not available: {e}",
                    "document_id": document_id
                }

            # Check outputs and prepare result
            outputs = {}
            summary = {
                "content_sections_processed": 0,
                "relevant_items_found": 0,
                "output_files": []
            }

            # Scan for business flow output directories
            business_flow_count = 0
            total_relevant_items = 0

            for item in doc_output_dir.iterdir():
                if item.is_dir() and item.name.startswith("business_flow_"):
                    business_flow_count += 1

                    # Check for relevant_content.json in this business flow directory
                    relevant_file = item / "relevant_content.json"
                    merged_file = item / "merged_relevant_content.json"

                    if relevant_file.exists():
                        outputs[f"relevant_content_{item.name}"] = str(relevant_file)
                        summary["output_files"].append(str(relevant_file))

                        # Try to count relevant items
                        try:
                            import json
                            with open(relevant_file, 'r', encoding='utf-8') as f:
                                content_data = json.load(f)
                                if isinstance(content_data, dict):
                                    total_relevant_items += 1
                                elif isinstance(content_data, list):
                                    total_relevant_items += len(content_data)
                        except Exception as e:
                            logger.warning(f"Could not parse relevant content file {relevant_file}: {e}")

                    if merged_file.exists():
                        outputs[f"merged_content_{item.name}"] = str(merged_file)
                        summary["output_files"].append(str(merged_file))

            # Also check for shared screen_graph.json
            screen_graph_file = doc_output_dir / "screen_graph.json"
            if screen_graph_file.exists():
                outputs["screen_graph"] = str(screen_graph_file)
                summary["output_files"].append(str(screen_graph_file))

            # Update summary
            summary["content_sections_processed"] = business_flow_count
            summary["relevant_items_found"] = total_relevant_items
            
            # Determine success
            success = len(outputs) > 0
            
            if success:
                message = f"Successfully processed {summary['content_sections_processed']} content sections, found {summary['relevant_items_found']} relevant items"
                logger.info(f"Relevant content processing completed: {document_id}")
            else:
                message = "No relevant content processed"
                logger.warning(f"No relevant content processed for: {document_id}")

            result = {
                "success": success,
                "document_id": document_id,
                "input_file": str(input_json),
                "output_directory": str(doc_output_dir),
                "output_files": outputs,
                "summary": summary,
                "message": message
            }
            
            logger.info(f"Relevant content processing completed: {document_id}")
            return result

        except Exception as e:
            logger.error(f"Relevant content processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": input_data.get("document_id", "unknown")
            }

    async def get_status(self) -> Dict[str, Any]:
        """Get current module status"""
        return {
            "module_id": self.config.id,
            "status": "ready",
            "version": self.config.version,
            "processor_script_exists": self.processor_script.exists(),
            "output_directory": str(self.output_base_dir)
        }
