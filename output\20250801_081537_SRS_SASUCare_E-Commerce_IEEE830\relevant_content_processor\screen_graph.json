{"graph": {"nodes": [{"id": "home_page", "name": "Home Page", "type": "screen"}, {"id": "product_detail_page", "name": "Product Detail Page", "type": "screen"}, {"id": "search_results_page", "name": "Search Results Page", "type": "screen"}, {"id": "about_us_page", "name": "About Us Page", "type": "screen"}, {"id": "contact_page", "name": "Contact Page", "type": "screen"}, {"id": "collaborations_page", "name": "Collaborations Page", "type": "screen"}, {"id": "login_page", "name": "<PERSON><PERSON>", "type": "screen"}, {"id": "customer_registration_page", "name": "Customer Registration Page", "type": "screen"}, {"id": "seller_registration_page", "name": "Seller Registration Page", "type": "screen"}, {"id": "shopping_cart_page", "name": "Shopping Cart Page", "type": "screen"}, {"id": "customer_bookings_page", "name": "Customer Bookings Page", "type": "screen"}, {"id": "booking_details_page", "name": "Booking Details Page", "type": "screen"}, {"id": "create_booking_page", "name": "Create Booking Page", "type": "screen"}, {"id": "user_profile_page", "name": "User Profile Page", "type": "screen"}, {"id": "edit_profile_page", "name": "Edit Profile Page", "type": "screen"}, {"id": "change_password_page", "name": "Change Password Page", "type": "screen"}, {"id": "user_orders_page", "name": "User Orders Page", "type": "screen"}, {"id": "seller_dashboard", "name": "Seller Dashboard", "type": "screen"}, {"id": "seller_products_page", "name": "Seller Products Page", "type": "screen"}, {"id": "edit_product_page", "name": "Edit Product Page", "type": "screen"}, {"id": "seller_orders_page", "name": "Seller Orders Page", "type": "screen"}, {"id": "seller_order_details_page", "name": "Seller Order Details Page", "type": "screen"}, {"id": "seller_bookings_page", "name": "Se<PERSON> Bookings Page", "type": "screen"}, {"id": "seller_booking_details_page", "name": "Seller Booking Details Page", "type": "screen"}, {"id": "admin_dashboard", "name": "Admin Dashboard", "type": "screen"}, {"id": "admin_products_page", "name": "Admin Products Page", "type": "screen"}, {"id": "admin_pending_products_page", "name": "Admin Pending Products Page", "type": "screen"}, {"id": "admin_categories_page", "name": "Admin Categories Page", "type": "screen"}, {"id": "admin_users_page", "name": "Admin Users Page", "type": "screen"}, {"id": "admin_orders_page", "name": "Admin Orders Page", "type": "screen"}, {"id": "admin_order_details_page", "name": "Admin Order Details Page", "type": "screen"}, {"id": "admin_sale_codes_page", "name": "Admin Sale Codes Page", "type": "screen"}, {"id": "access_denied_page", "name": "Access Denied Page", "type": "screen"}, {"id": "error_page", "name": "Error <PERSON>", "type": "screen"}], "edges": [{"source": "home_page", "target": "login_page", "action": "<PERSON><PERSON> (UC-102)", "element": "User Account <PERSON>u"}, {"source": "home_page", "target": "customer_registration_page", "action": "Click Sign Up (UC-101)", "element": "Sign Up"}, {"source": "home_page", "target": "seller_registration_page", "action": "Click Sign Up (UC-101)", "element": "Sign Up"}, {"source": "home_page", "target": "product_detail_page", "action": "Select product", "element": "Product Cards"}, {"source": "home_page", "target": "search_results_page", "action": "Submit search", "element": "Search Bar"}, {"source": "search_results_page", "target": "product_detail_page", "action": "Select product from results", "element": "Results Grid"}, {"source": "customer_registration_page", "target": "login_page", "action": "Submit registration form (UC-101)", "element": "Register <PERSON>"}, {"source": "seller_registration_page", "target": "login_page", "action": "Submit registration form (UC-101)", "element": "Register <PERSON>"}, {"source": "login_page", "target": "home_page", "action": "Successful login as Customer (UC-102)", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "seller_dashboard", "action": "Successful login as <PERSON><PERSON> (UC-102)", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "admin_dashboard", "action": "Successful login as <PERSON><PERSON> (UC-102)", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "customer_registration_page", "action": "Navigate to customer registration", "element": "Registration Links"}, {"source": "login_page", "target": "seller_registration_page", "action": "Navigate to seller registration", "element": "Registration Links"}, {"source": "product_detail_page", "target": "shopping_cart_page", "action": "Add product to cart and view cart", "element": "Add to Cart Button"}, {"source": "shopping_cart_page", "target": "user_orders_page", "action": "Complete checkout process (UC-207)", "element": "Checkout <PERSON><PERSON>"}, {"source": "shopping_cart_page", "target": "home_page", "action": "Continue shopping", "element": "Continue Shopping Button"}, {"source": "user_profile_page", "target": "edit_profile_page", "action": "Select to edit profile", "element": "Account <PERSON><PERSON>"}, {"source": "user_profile_page", "target": "change_password_page", "action": "Select to change password", "element": "Account <PERSON><PERSON>"}, {"source": "user_profile_page", "target": "user_orders_page", "action": "View order history (UC-208)", "element": "'Order History'"}, {"source": "user_profile_page", "target": "customer_bookings_page", "action": "View booking history (UC-211)", "element": "'Booking History'"}, {"source": "edit_profile_page", "target": "user_profile_page", "action": "Save profile updates", "element": "Save Changes <PERSON>"}, {"source": "change_password_page", "target": "user_profile_page", "action": "Update password", "element": "Change Password <PERSON>"}, {"source": "product_detail_page", "target": "create_booking_page", "action": "Initiate new service booking", "element": "Book Service Button"}, {"source": "create_booking_page", "target": "customer_bookings_page", "action": "Submit new booking", "element": "Submit Booking Button"}, {"source": "customer_bookings_page", "target": "booking_details_page", "action": "View details of a booking (UC-211)", "element": "Booking Details Link"}, {"source": "booking_details_page", "target": "customer_bookings_page", "action": "Return to booking list", "element": "Back to Bookings"}, {"source": "seller_dashboard", "target": "seller_products_page", "action": "Manage products (UC-302)", "element": "Navigation Menu"}, {"source": "seller_dashboard", "target": "seller_orders_page", "action": "Manage orders (UC-305)", "element": "Navigation Menu"}, {"source": "seller_dashboard", "target": "seller_bookings_page", "action": "Manage bookings", "element": "Navigation Menu"}, {"source": "seller_dashboard", "target": "edit_profile_page", "action": "Manage Seller Profile/Shop Information (UC-301)", "element": "Shop settings"}, {"source": "seller_products_page", "target": "edit_product_page", "action": "Add a new product", "element": "Add Product Button"}, {"source": "seller_products_page", "target": "edit_product_page", "action": "Edit an existing product", "element": "Product Actions"}, {"source": "edit_product_page", "target": "seller_products_page", "action": "Save product changes", "element": "Save/Update But<PERSON>"}, {"source": "seller_orders_page", "target": "seller_order_details_page", "action": "View specific order details", "element": "Order Details Link"}, {"source": "seller_order_details_page", "target": "seller_orders_page", "action": "Return to orders list", "element": "Back to Orders"}, {"source": "seller_bookings_page", "target": "seller_booking_details_page", "action": "View specific booking details", "element": "Booking Details Link"}, {"source": "seller_booking_details_page", "target": "seller_bookings_page", "action": "Return to bookings list", "element": "Back to Bookings"}, {"source": "admin_dashboard", "target": "admin_products_page", "action": "Manage all products (UC-405)", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_pending_products_page", "action": "Review pending products", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_categories_page", "action": "Manage categories (UC-404)", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_users_page", "action": "Manage users", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_orders_page", "action": "Oversee all orders", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_sale_codes_page", "action": "Manage sale codes", "element": "Navigation Menu"}, {"source": "admin_orders_page", "target": "admin_order_details_page", "action": "View specific order details", "element": "Order Details Link"}, {"source": "admin_order_details_page", "target": "admin_orders_page", "action": "Return to all orders list", "element": "Back to orders"}, {"source": "product_detail_page", "target": "home_page", "action": "Navigate to homepage", "element": "Site Logo"}, {"source": "shopping_cart_page", "target": "home_page", "action": "Navigate to homepage", "element": "Site Logo"}, {"source": "user_profile_page", "target": "home_page", "action": "Navigate to homepage", "element": "Site Logo"}, {"source": "home_page", "target": "shopping_cart_page", "action": "View shopping cart", "element": "Shopping Cart Icon"}, {"source": "product_detail_page", "target": "shopping_cart_page", "action": "View shopping cart", "element": "Shopping Cart Icon"}, {"source": "seller_dashboard", "target": "access_denied_page", "action": "Attempt to access admin page", "element": "Direct URL Navigation"}, {"source": "shopping_cart_page", "target": "error_page", "action": "System fails during order processing", "element": "System Event"}]}}