"""
WebSocket Routes
WebSocket connection management endpoints
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime

from app.models.schemas import PipelineResponse
from app.services.websocket_service import WebSocketManager
from app.api.dependencies import get_websocket_manager

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/stats", response_model=PipelineResponse)
async def get_websocket_stats(
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """Get WebSocket connection statistics"""
    try:
        stats = await websocket_manager.get_connection_stats()
        return PipelineResponse(
            success=True,
            data=stats,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"❌ Error getting WebSocket stats: {e}")
        raise HTTPException(status_code=500, detail={
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })
