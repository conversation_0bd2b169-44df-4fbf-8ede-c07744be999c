"""
Logging Configuration
Centralized logging setup
"""

import logging
import sys
import asyncio
from pathlib import Path
from typing import Optional, List, Dict, Any
from app.core.config import settings

class WebSocketLogHandler(logging.Handler):
    """Custom logging handler that sends logs to WebSocket clients with delay"""

    def __init__(self):
        super().__init__()
        self.websocket_manager = None
        self.log_queue: List[Dict[str, Any]] = []
        self.is_processing = False

    def set_websocket_manager(self, websocket_manager):
        """Set the WebSocket manager instance"""
        self.websocket_manager = websocket_manager

    def emit(self, record):
        """Add log record to queue for delayed sending"""
        if not self.websocket_manager:
            return

        try:
            # Format the log message
            log_message = self.format(record)

            # Only queue INFO, WARNING, ERROR from root logger
            if record.name == "root" and record.levelno >= logging.INFO:
                # Add to queue
                self.log_queue.append({
                    "message": log_message,
                    "level": record.levelname
                })

                # Start processing queue if not already processing
                if not self.is_processing:
                    # Use ensure_future to properly handle the coroutine
                    asyncio.ensure_future(self._process_log_queue())
        except Exception:
            # Don't let logging errors crash the application
            pass

    async def _process_log_queue(self):
        """Process log queue with delay between messages"""
        if self.is_processing:
            return

        self.is_processing = True

        try:
            while self.log_queue:
                log_item = self.log_queue.pop(0)  # Get first item (FIFO)

                # Broadcast the log message
                await self._broadcast_log(log_item["message"], log_item["level"])

                # Wait 0.15 seconds before next message (if there are more)
                if self.log_queue:
                    await asyncio.sleep(0.04)

        except Exception as e:
            print(f"Error processing log queue: {e}")
        finally:
            self.is_processing = False

            # If there are still items in queue after error, try to process them
            if self.log_queue:
                asyncio.ensure_future(self._process_log_queue())

    def flush_queue(self):
        """Force flush all remaining logs in queue immediately"""
        if self.log_queue and self.websocket_manager:
            # Create a task to flush remaining logs without delay
            asyncio.ensure_future(self._flush_remaining_logs())

    async def _flush_remaining_logs(self):
        """Flush all remaining logs without delay"""
        try:
            while self.log_queue:
                log_item = self.log_queue.pop(0)
                await self._broadcast_log(log_item["message"], log_item["level"])
        except Exception as e:
            print(f"Error flushing log queue: {e}")

    async def _broadcast_log(self, message: str, level: str):
        """Broadcast log message via WebSocket"""
        try:
            await self.websocket_manager.broadcast({
                "type": "root_log",
                "data": {
                    "level": level.lower(),
                    "message": message
                }
            })
        except Exception:
            # Silently ignore WebSocket broadcast errors
            pass

# Global WebSocket log handler instance
websocket_log_handler = WebSocketLogHandler()

def flush_log_queue():
    """Flush any remaining logs in the queue"""
    global websocket_log_handler
    if websocket_log_handler:
        websocket_log_handler.flush_queue()

def setup_logging():
    """Setup application logging"""
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Create handlers
    console_handler = logging.StreamHandler(sys.stdout)
    file_handler = logging.FileHandler(logs_dir / "server.log")
    error_handler = logging.FileHandler(logs_dir / "error.log")
    error_handler.setLevel(logging.ERROR)

    # Configure root logger with WebSocket handler
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format=settings.LOG_FORMAT,
        handlers=[console_handler, file_handler, error_handler, websocket_log_handler]
    )
    
    # Configure specific loggers
    loggers = {
        "uvicorn": logging.INFO,
        "uvicorn.error": logging.INFO,
        "uvicorn.access": logging.INFO,
        "fastapi": logging.INFO,
        "websockets": logging.WARNING,
    }
    
    for logger_name, level in loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
    
    # Get main application logger
    logger = logging.getLogger(__name__)
    logger.info("Logging configured successfully")
    
    return logger
